import { defineConfig, presetUno, presetAttributify, presetIcons } from 'unocss'

export default defineConfig({
  presets: [
    presetUno(),
    presetAttributify(),
    presetIcons({
      scale: 1.2,
      warn: true,
    }),
  ],
  theme: {
    colors: {
      primary: {
        50: '#f0f9ff',
        100: '#e0f2fe',
        200: '#bae6fd',
        300: '#7dd3fc',
        400: '#38bdf8',
        500: '#0ea5e9',
        600: '#0284c7',
        700: '#0369a1',
        800: '#075985',
        900: '#0c4a6e',
      },
    },
    breakpoints: {
      xs: '480px',
      sm: '640px',
      md: '768px',
      lg: '1024px',
      xl: '1280px',
      '2xl': '1536px',
    },
  },
  shortcuts: {
    'btn': 'px-4 py-2 rounded inline-block bg-primary-600 text-white cursor-pointer hover:bg-primary-700 disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50',
    'btn-primary': 'btn bg-primary-600 hover:bg-primary-700',
    'btn-secondary': 'btn bg-gray-600 hover:bg-gray-700',
    'card': 'bg-white rounded-lg shadow-md p-6',
    'input': 'px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent',
  },
  rules: [
    // 自定义规则
    [/^text-(.*)$/, ([, c]) => ({ color: c })],
    [/^bg-(.*)$/, ([, c]) => ({ 'background-color': c })],
  ],
})
