/**
 * 请求日志中间件
 * 记录API请求的详细信息，用于监控和调试
 */

import { Context, Next } from 'koa';
import { config, isDevelopment } from '../config';

// 日志级别
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error'
}

// 日志接口
export interface LogEntry {
  level: LogLevel;
  timestamp: string;
  requestId: string;
  method: string;
  url: string;
  status: number;
  responseTime: number;
  userAgent?: string;
  ip: string;
  contentLength?: number;
  error?: string;
}

/**
 * 格式化日志输出
 */
function formatLog(entry: LogEntry): string {
  if (config.logging.format === 'json') {
    return JSON.stringify(entry);
  }
  
  // 简单格式
  const { timestamp, method, url, status, responseTime, ip, requestId } = entry;
  const statusColor = getStatusColor(status);
  const methodColor = getMethodColor(method);
  
  return `${timestamp} ${methodColor}${method}${resetColor} ${url} ${statusColor}${status}${resetColor} ${responseTime}ms ${ip} [${requestId}]`;
}

/**
 * 获取状态码颜色
 */
function getStatusColor(status: number): string {
  if (!isDevelopment) return '';
  
  if (status >= 500) return '\x1b[31m'; // 红色
  if (status >= 400) return '\x1b[33m'; // 黄色
  if (status >= 300) return '\x1b[36m'; // 青色
  if (status >= 200) return '\x1b[32m'; // 绿色
  return '\x1b[37m'; // 白色
}

/**
 * 获取请求方法颜色
 */
function getMethodColor(method: string): string {
  if (!isDevelopment) return '';
  
  switch (method) {
    case 'GET': return '\x1b[32m';    // 绿色
    case 'POST': return '\x1b[33m';   // 黄色
    case 'PUT': return '\x1b[34m';    // 蓝色
    case 'DELETE': return '\x1b[31m'; // 红色
    case 'PATCH': return '\x1b[35m';  // 紫色
    default: return '\x1b[37m';       // 白色
  }
}

const resetColor = isDevelopment ? '\x1b[0m' : '';

/**
 * 判断是否应该记录日志
 */
function shouldLog(ctx: Context): boolean {
  // 跳过健康检查和静态资源请求
  const skipPaths = ['/health', '/favicon.ico', '/robots.txt'];
  return !skipPaths.includes(ctx.path);
}

/**
 * 获取客户端IP地址
 */
function getClientIP(ctx: Context): string {
  return ctx.get('X-Forwarded-For') || 
         ctx.get('X-Real-IP') || 
         ctx.ip || 
         'unknown';
}

/**
 * 请求日志中间件
 */
export function requestLogger() {
  return async (ctx: Context, next: Next) => {
    if (!shouldLog(ctx)) {
      await next();
      return;
    }
    
    const start = Date.now();
    const timestamp = new Date().toISOString();
    
    try {
      await next();
      
      const responseTime = Date.now() - start;
      const contentLength = ctx.response.length;
      
      const logEntry: LogEntry = {
        level: LogLevel.INFO,
        timestamp,
        requestId: ctx.state.requestId,
        method: ctx.method,
        url: ctx.url,
        status: ctx.status,
        responseTime,
        userAgent: ctx.get('User-Agent'),
        ip: getClientIP(ctx),
        contentLength
      };
      
      // 根据状态码调整日志级别
      if (ctx.status >= 500) {
        logEntry.level = LogLevel.ERROR;
      } else if (ctx.status >= 400) {
        logEntry.level = LogLevel.WARN;
      }
      
      console.log(formatLog(logEntry));
      
    } catch (error: any) {
      const responseTime = Date.now() - start;
      
      const logEntry: LogEntry = {
        level: LogLevel.ERROR,
        timestamp,
        requestId: ctx.state.requestId,
        method: ctx.method,
        url: ctx.url,
        status: error.status || 500,
        responseTime,
        userAgent: ctx.get('User-Agent'),
        ip: getClientIP(ctx),
        error: error.message
      };
      
      console.error(formatLog(logEntry));
      throw error; // 重新抛出错误
    }
  };
}

/**
 * 应用启动日志
 */
export function logAppStart(): void {
  const banner = `
╔══════════════════════════════════════════════════════════════╗
║                    期末复习平台 API 服务                      ║
║                  Final Review Platform API                  ║
╠══════════════════════════════════════════════════════════════╣
║  版本: ${config.app.version.padEnd(10)} │ 环境: ${config.app.env.padEnd(12)} │ 端口: ${String(config.app.port).padEnd(5)} ║
║  数据库: ${config.database.path.slice(-20).padEnd(42)} ║
║  日志级别: ${config.logging.level.padEnd(8)} │ 格式: ${config.logging.format.padEnd(10)} │ 时间: ${new Date().toLocaleString()} ║
╚══════════════════════════════════════════════════════════════╝
`;
  
  console.log(banner);
  console.log('🚀 服务器启动成功!');
  console.log(`📡 监听地址: http://${config.app.host}:${config.app.port}`);
  console.log(`🗄️  数据库路径: ${config.database.path}`);
  console.log(`📝 日志级别: ${config.logging.level}`);
  console.log('');
}

/**
 * 应用关闭日志
 */
export function logAppShutdown(): void {
  console.log('');
  console.log('🛑 服务器正在关闭...');
  console.log('👋 再见!');
}