/**
 * 学科数据访问层 (DAO)
 * 负责学科相关的数据库操作
 * 
 * <AUTHOR> (Engineer)
 * @date 2025-01-27
 */

import { Database } from 'better-sqlite3';
import { getDatabase } from '../database/connection';
import { Subject } from '../types';

export class SubjectDao {
  private db: Database | null = null;

  constructor() {
    // 延迟初始化数据库连接
  }

  private getDb(): Database {
    if (!this.db) {
      this.db = getDatabase();
    }
    return this.db;
  }

  /**
   * 获取所有学科列表
   * @param includeDisabled 是否包含禁用的学科，默认false
   * @returns 学科列表
   */
  findAll(includeDisabled: boolean = false): Subject[] {
    try {
      const sql = includeDisabled
        ? 'SELECT * FROM subjects ORDER BY sort_order ASC, created_at ASC'
        : 'SELECT * FROM subjects WHERE status = 1 ORDER BY sort_order ASC, created_at ASC';

      const stmt = this.getDb().prepare(sql);
      const rows = stmt.all() as Subject[];

      return rows;
    } catch (error) {
      console.error('SubjectDao.findAll error:', error);
      throw new Error('获取学科列表失败');
    }
  }

  /**
   * 根据ID获取单个学科
   * @param id 学科ID
   * @param includeDisabled 是否包含禁用的学科（默认false，管理员操作时应设为true）
   * @returns 学科信息或null
   */
  findById(id: number, includeDisabled: boolean = false): Subject | null {
    try {
      const sql = includeDisabled
        ? 'SELECT * FROM subjects WHERE id = ?'
        : 'SELECT * FROM subjects WHERE id = ? AND status = 1';
      const stmt = this.getDb().prepare(sql);
      const row = stmt.get(id) as Subject | undefined;

      return row || null;
    } catch (error) {
      console.error('SubjectDao.findById error:', error);
      throw new Error('获取学科信息失败');
    }
  }

  /**
   * 根据名称获取学科
   * @param name 学科名称
   * @returns 学科信息或null
   */
  findByName(name: string): Subject | null {
    try {
      const sql = 'SELECT * FROM subjects WHERE name = ? AND status = 1';
      const stmt = this.getDb().prepare(sql);
      const row = stmt.get(name) as Subject | undefined;

      return row || null;
    } catch (error) {
      console.error('SubjectDao.findByName error:', error);
      throw new Error('获取学科信息失败');
    }
  }

  /**
   * 创建新学科
   * @param subject 学科信息
   * @returns 创建的学科ID
   */
  create(subject: Omit<Subject, 'id' | 'created_at' | 'updated_at'>): number {
    try {
      const sql = `
        INSERT INTO subjects (name, description, status, sort_order)
        VALUES (?, ?, ?, ?)
      `;
      const stmt = this.getDb().prepare(sql);
      const result = stmt.run(
        subject.name,
        subject.description || '',
        subject.status || 1,
        subject.sort_order || 0
      );

      return result.lastInsertRowid as number;
    } catch (error) {
      console.error('SubjectDao.create error:', error);
      if (error instanceof Error && error.message.includes('UNIQUE constraint failed')) {
        throw new Error('学科名称已存在');
      }
      throw new Error('创建学科失败');
    }
  }

  /**
   * 更新学科信息
   * @param id 学科ID
   * @param updates 更新的字段
   * @returns 是否更新成功
   */
  update(id: number, updates: Partial<Omit<Subject, 'id' | 'created_at' | 'updated_at'>>): boolean {
    try {
      const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
      const values = Object.values(updates);

      const sql = `UPDATE subjects SET ${fields} WHERE id = ?`;
      const stmt = this.getDb().prepare(sql);
      const result = stmt.run(...values, id);

      return result.changes > 0;
    } catch (error) {
      console.error('SubjectDao.update error:', error);
      if (error instanceof Error && error.message.includes('UNIQUE constraint failed')) {
        throw new Error('学科名称已存在');
      }
      throw new Error('更新学科失败');
    }
  }

  /**
   * 软删除学科（设置status为0）
   * @param id 学科ID
   * @returns 是否删除成功
   */
  softDelete(id: number): boolean {
    try {
      const sql = 'UPDATE subjects SET status = 0 WHERE id = ?';
      const stmt = this.getDb().prepare(sql);
      const result = stmt.run(id);

      return result.changes > 0;
    } catch (error) {
      console.error('SubjectDao.softDelete error:', error);
      throw new Error('删除学科失败');
    }
  }

  /**
   * 硬删除学科（管理员专用）
   * @param id 学科ID
   * @returns 是否删除成功
   */
  hardDelete(id: number): boolean {
    try {
      const sql = 'DELETE FROM subjects WHERE id = ?';
      const stmt = this.getDb().prepare(sql);
      const result = stmt.run(id);

      return result.changes > 0;
    } catch (error) {
      console.error('SubjectDao.hardDelete error:', error);
      throw new Error('硬删除学科失败');
    }
  }

  /**
   * 批量获取学科
   * @param ids 学科ID数组
   * @returns 学科列表
   */
  findByIds(ids: number[]): Subject[] {
    try {
      if (ids.length === 0) {
        return [];
      }

      const placeholders = ids.map(() => '?').join(',');
      const sql = `SELECT * FROM subjects WHERE id IN (${placeholders}) ORDER BY sort_order ASC, created_at ASC`;
      const stmt = this.getDb().prepare(sql);
      const rows = stmt.all(...ids) as Subject[];

      return rows;
    } catch (error) {
      console.error('SubjectDao.findByIds error:', error);
      throw new Error('批量获取学科失败');
    }
  }

  /**
   * 批量更新学科状态
   * @param ids 学科ID数组
   * @param status 新状态
   * @returns 更新成功的数量
   */
  batchUpdateStatus(ids: number[], status: number): number {
    try {
      if (ids.length === 0) {
        return 0;
      }

      const placeholders = ids.map(() => '?').join(',');
      const sql = `UPDATE subjects SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id IN (${placeholders})`;
      const stmt = this.getDb().prepare(sql);
      const result = stmt.run(status, ...ids);

      return result.changes;
    } catch (error) {
      console.error('SubjectDao.batchUpdateStatus error:', error);
      throw new Error('批量更新学科状态失败');
    }
  }

  /**
   * 获取学科统计信息
   * @param id 学科ID
   * @returns 统计信息
   */
  getStats(id: number): { fileCount: number; folderCount: number; totalSize: number } {
    try {
      const sql = `
        SELECT
          COUNT(CASE WHEN type = 'file' THEN 1 END) as fileCount,
          COUNT(CASE WHEN type = 'folder' THEN 1 END) as folderCount,
          COALESCE(SUM(CASE WHEN type = 'file' THEN size ELSE 0 END), 0) as totalSize
        FROM file_nodes
        WHERE subject_id = ? AND is_deleted = 0
      `;
      const stmt = this.getDb().prepare(sql);
      const result = stmt.get(id) as any;

      return {
        fileCount: result.fileCount || 0,
        folderCount: result.folderCount || 0,
        totalSize: result.totalSize || 0
      };
    } catch (error) {
      console.error('SubjectDao.getStats error:', error);
      throw new Error('获取学科统计信息失败');
    }
  }
}

// 导出单例实例
export const subjectDao = new SubjectDao();