/**
 * 管理功能集成测试
 * 测试前后端集成、数据同步、错误处理等功能
 * 
 * <AUTHOR> (Engineer)
 * @date 2025-07-27
 */

import { test, expect } from '@playwright/test'

test.describe('管理功能集成测试', () => {
  test.beforeEach(async ({ page }) => {
    // 启动前端和后端服务
    await page.goto('http://localhost:5173')

    // 等待页面加载完成
    await page.waitForLoadState('networkidle')
  })

  test('前后端API集成验证', async ({ page }) => {
    // 导航到管理后台
    await page.goto('http://localhost:5173/admin-panel-abcdef')
    await page.waitForLoadState('networkidle')

    // 验证管理后台页面加载
    await expect(page.locator('h1')).toContainText('管理后台')

    // 导航到学科管理页面
    await page.click('text=学科管理')
    await page.waitForLoadState('networkidle')

    // 验证学科列表API调用
    await expect(page.locator('.ant-table-tbody tr')).toHaveCount.greaterThan(0)

    // 验证API响应格式
    const response = await page.waitForResponse(response =>
      response.url().includes('/api/admin/subjects') && response.status() === 200
    )
    const data = await response.json()
    expect(data).toHaveProperty('success', true)
    expect(data).toHaveProperty('data')
    expect(Array.isArray(data.data)).toBe(true)
  })

  test('学科创建集成测试', async ({ page }) => {
    await page.goto('http://localhost:5173/admin-panel-abcdef/subjects')
    await page.waitForLoadState('networkidle')

    // 点击创建按钮
    await page.click('text=创建学科')
    await page.waitForSelector('.ant-modal')

    // 填写表单
    const testSubjectName = `测试学科_${Date.now()}`
    await page.fill('input[placeholder="请输入学科名称"]', testSubjectName)
    await page.fill('textarea[placeholder="请输入学科描述"]', '这是一个测试学科')

    // 提交表单
    await page.click('.ant-modal .ant-btn-primary')

    // 等待API响应
    const createResponse = await page.waitForResponse(response =>
      response.url().includes('/api/admin/subjects') &&
      response.request().method() === 'POST'
    )
    expect(createResponse.status()).toBe(201)

    // 验证创建成功消息
    await expect(page.locator('.ant-message')).toContainText('创建成功')

    // 验证表格中出现新学科
    await expect(page.locator(`text=${testSubjectName}`)).toBeVisible()
  })

  test('学科编辑集成测试', async ({ page }) => {
    await page.goto('http://localhost:5173/admin-panel-abcdef/subjects')
    await page.waitForLoadState('networkidle')

    // 点击第一行的编辑按钮
    await page.click('.ant-table-tbody tr:first-child .ant-btn:has-text("编辑")')
    await page.waitForSelector('.ant-modal')

    // 修改学科名称
    const updatedName = `更新学科_${Date.now()}`
    await page.fill('input[placeholder="请输入学科名称"]', updatedName)

    // 提交表单
    await page.click('.ant-modal .ant-btn-primary')

    // 等待API响应
    const updateResponse = await page.waitForResponse(response =>
      response.url().includes('/api/admin/subjects/') &&
      response.request().method() === 'PUT'
    )
    expect(updateResponse.status()).toBe(200)

    // 验证更新成功消息
    await expect(page.locator('.ant-message')).toContainText('更新成功')

    // 验证表格中显示更新后的名称
    await expect(page.locator(`text=${updatedName}`)).toBeVisible()
  })

  test('数据同步验证', async ({ page }) => {
    // 在管理后台创建学科
    await page.goto('http://localhost:5173/admin-panel-abcdef/subjects')
    await page.waitForLoadState('networkidle')

    await page.click('text=创建学科')
    await page.waitForSelector('.ant-modal')

    const syncTestName = `同步测试_${Date.now()}`
    await page.fill('input[placeholder="请输入学科名称"]', syncTestName)
    await page.click('.ant-modal .ant-btn-primary')

    // 等待创建完成
    await page.waitForResponse(response =>
      response.url().includes('/api/admin/subjects') &&
      response.request().method() === 'POST'
    )

    // 导航到普通用户页面
    await page.goto('http://localhost:5173')
    await page.waitForLoadState('networkidle')

    // 验证新创建的学科在普通用户页面可见
    await expect(page.locator(`text=${syncTestName}`)).toBeVisible()
  })

  test('错误处理集成测试', async ({ page }) => {
    await page.goto('http://localhost:5173/admin-panel-abcdef/subjects')
    await page.waitForLoadState('networkidle')

    // 测试创建重复学科名称
    await page.click('text=创建学科')
    await page.waitForSelector('.ant-modal')

    // 使用已存在的学科名称
    await page.fill('input[placeholder="请输入学科名称"]', '数学')
    await page.click('.ant-modal .ant-btn-primary')

    // 验证错误处理
    await expect(page.locator('.ant-message-error')).toBeVisible()
    await expect(page.locator('.ant-message')).toContainText('学科名称已存在')
  })

  test('性能测试', async ({ page }) => {
    await page.goto('http://localhost:5173/admin-panel-abcdef/subjects')

    // 测试页面加载性能
    const startTime = Date.now()
    await page.waitForLoadState('networkidle')
    const loadTime = Date.now() - startTime

    // 验证页面加载时间小于3秒
    expect(loadTime).toBeLessThan(3000)

    // 测试API响应性能
    const apiStartTime = Date.now()
    const response = await page.waitForResponse(response =>
      response.url().includes('/api/admin/subjects')
    )
    const apiTime = Date.now() - apiStartTime

    // 验证API响应时间小于1秒
    expect(apiTime).toBeLessThan(1000)
    expect(response.status()).toBe(200)
  })

  test('浏览器控制台错误检查', async ({ page }) => {
    const consoleErrors: string[] = []

    // 监听控制台错误
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text())
      }
    })

    // 访问管理后台各个页面
    await page.goto('http://localhost:5173/admin-panel-abcdef')
    await page.waitForLoadState('networkidle')

    await page.click('text=学科管理')
    await page.waitForLoadState('networkidle')

    await page.click('text=仪表盘')
    await page.waitForLoadState('networkidle')

    // 验证没有控制台错误
    expect(consoleErrors).toHaveLength(0)
  })

  test('用户体验流畅性测试', async ({ page }) => {
    await page.goto('http://localhost:5173/admin-panel-abcdef/subjects')
    await page.waitForLoadState('networkidle')

    // 测试搜索功能响应性
    await page.fill('input[placeholder="搜索学科名称或描述"]', '数学')

    // 验证搜索结果立即显示（防抖后）
    await page.waitForTimeout(500) // 等待防抖
    const searchResults = await page.locator('.ant-table-tbody tr').count()
    expect(searchResults).toBeGreaterThan(0)

    // 测试分页功能
    if (await page.locator('.ant-pagination').isVisible()) {
      await page.click('.ant-pagination-next')
      await page.waitForLoadState('networkidle')

      // 验证分页切换成功
      await expect(page.locator('.ant-pagination-item-active')).toContainText('2')
    }

    // 测试状态切换
    await page.selectOption('select[placeholder="选择状态"]', '1')
    await page.waitForTimeout(300)

    // 验证状态过滤生效
    const filteredResults = await page.locator('.ant-table-tbody tr').count()
    expect(filteredResults).toBeGreaterThanOrEqual(0)
  })
})

test.describe('扩展集成测试', () => {
  test('管理界面与现有系统集成测试', async ({ page }) => {
    // 验证管理界面不影响现有功能
    await page.goto('http://localhost:5173')
    await page.waitForLoadState('networkidle')

    // 验证普通用户界面正常工作
    await expect(page.locator('h1:has-text("期末复习平台")')).toBeVisible()

    // 导航到学科列表
    await page.click('text=开始浏览学科')
    await page.waitForLoadState('networkidle')

    // 验证学科列表正常显示
    await expect(page.locator('.subject-card')).toHaveCountGreaterThan(0)

    // 访问管理后台
    await page.goto('http://localhost:5173/admin-panel-abcdef/subjects')
    await page.waitForLoadState('networkidle')

    // 验证管理后台正常工作
    await expect(page.locator('h1:has-text("学科管理")')).toBeVisible()
    await expect(page.locator('.ant-table')).toBeVisible()

    // 返回普通用户界面
    await page.goto('http://localhost:5173')
    await page.waitForLoadState('networkidle')

    // 验证普通用户界面仍然正常
    await expect(page.locator('h1:has-text("期末复习平台")')).toBeVisible()
  })

  test('API集成一致性测试', async ({ page }) => {
    // 测试管理API和普通API的数据一致性
    await page.goto('http://localhost:5173/admin-panel-abcdef/subjects')
    await page.waitForLoadState('networkidle')

    // 获取管理API数据
    const adminApiResponse = await page.waitForResponse(response =>
      response.url().includes('/api/admin/subjects') &&
      response.request().method() === 'GET'
    )
    const adminData = await adminApiResponse.json()

    // 导航到普通用户页面
    await page.goto('http://localhost:5173/subjects')
    await page.waitForLoadState('networkidle')

    // 获取普通API数据
    const userApiResponse = await page.waitForResponse(response =>
      response.url().includes('/api/subjects') &&
      response.request().method() === 'GET'
    )
    const userData = await userApiResponse.json()

    // 验证数据结构一致性
    expect(adminData.success).toBe(true)
    expect(userData.success).toBe(true)
    expect(Array.isArray(adminData.data)).toBe(true)
    expect(Array.isArray(userData.data)).toBe(true)

    // 验证基础数据一致性（排除管理专用字段）
    if (adminData.data.length > 0 && userData.data.length > 0) {
      const adminSubject = adminData.data[0]
      const userSubject = userData.data.find(s => s.id === adminSubject.id)

      if (userSubject) {
        expect(userSubject.name).toBe(adminSubject.name)
        expect(userSubject.description).toBe(adminSubject.description)
        expect(userSubject.status).toBe(adminSubject.status)
      }
    }
  })

  test('缓存机制集成测试', async ({ page }) => {
    await page.goto('http://localhost:5173/admin-panel-abcdef/subjects')
    await page.waitForLoadState('networkidle')

    // 第一次加载数据
    const firstLoadTime = Date.now()
    await page.reload()
    await page.waitForLoadState('networkidle')
    const firstLoadDuration = Date.now() - firstLoadTime

    // 第二次加载数据（应该使用缓存）
    const secondLoadTime = Date.now()
    await page.reload()
    await page.waitForLoadState('networkidle')
    const secondLoadDuration = Date.now() - secondLoadTime

    // 验证缓存效果（第二次加载应该更快）
    console.log(`First load: ${firstLoadDuration}ms, Second load: ${secondLoadDuration}ms`)

    // 验证页面功能正常
    await expect(page.locator('h1:has-text("学科管理")')).toBeVisible()
    await expect(page.locator('.ant-table')).toBeVisible()
  })

  test('错误恢复集成测试', async ({ page }) => {
    await page.goto('http://localhost:5173/admin-panel-abcdef/subjects')
    await page.waitForLoadState('networkidle')

    // 模拟网络错误
    await page.route('**/api/admin/subjects', route => {
      route.abort('failed')
    })

    // 尝试刷新数据
    await page.click('button:has-text("刷新")')
    await page.waitForTimeout(2000)

    // 恢复网络连接
    await page.unroute('**/api/admin/subjects')

    // 再次尝试刷新
    await page.click('button:has-text("刷新")')
    await page.waitForLoadState('networkidle')

    // 验证功能恢复正常
    await expect(page.locator('.ant-table')).toBeVisible()
  })

  test('浏览器兼容性测试', async ({ page, browserName }) => {
    await page.goto('http://localhost:5173/admin-panel-abcdef/subjects')
    await page.waitForLoadState('networkidle')

    // 验证基本功能在不同浏览器中正常工作
    await expect(page.locator('h1:has-text("学科管理")')).toBeVisible()
    await expect(page.locator('.ant-table')).toBeVisible()

    // 测试搜索功能
    await page.fill('input[placeholder="搜索学科名称或描述"]', '数学')
    await page.waitForTimeout(500)

    // 验证搜索结果
    const searchResults = page.locator('.ant-table tbody tr')
    const count = await searchResults.count()
    expect(count).toBeGreaterThanOrEqual(0)

    console.log(`Browser ${browserName}: Search functionality working`)
  })
})
