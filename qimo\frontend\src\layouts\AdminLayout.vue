<template>
  <a-layout class="admin-layout">
    <!-- 侧边栏 -->
    <a-layout-sider
      v-model:collapsed="collapsed"
      :trigger="null"
      collapsible
      class="admin-sider"
      :width="220"
      :collapsed-width="80"
    >
      <!-- Logo区域 -->
      <div class="admin-logo">
        <div class="logo-icon">
          <BookOutlined />
        </div>
        <div v-if="!collapsed" class="logo-text">
          <div class="logo-title">管理后台</div>
          <div class="logo-subtitle">期末复习平台</div>
        </div>
      </div>

      <!-- 菜单 -->
      <a-menu
        v-model:selectedKeys="selectedKeys"
        v-model:openKeys="openKeys"
        mode="inline"
        theme="dark"
        class="admin-menu"
        :inline-collapsed="collapsed"
      >
        <a-menu-item key="dashboard" @click="navigateTo('AdminDashboard')">
          <template #icon>
            <DashboardOutlined />
          </template>
          <span>仪表盘</span>
        </a-menu-item>

        <a-sub-menu key="content" title="内容管理">
          <template #icon>
            <FolderOutlined />
          </template>
          <a-menu-item key="subjects" @click="navigateTo('AdminSubjects')">
            <template #icon>
              <BookOutlined />
            </template>
            <span>学科管理</span>
          </a-menu-item>
        </a-sub-menu>

        <a-menu-item key="settings">
          <template #icon>
            <SettingOutlined />
          </template>
          <span>系统设置</span>
        </a-menu-item>
      </a-menu>
    </a-layout-sider>

    <!-- 主体区域 -->
    <a-layout class="admin-main">
      <!-- 顶部导航 -->
      <a-layout-header class="admin-header">
        <div class="header-left">
          <a-button
            type="text"
            class="trigger-btn"
            @click="toggleCollapsed"
          >
            <MenuUnfoldOutlined v-if="collapsed" />
            <MenuFoldOutlined v-else />
          </a-button>

          <!-- 面包屑导航 -->
          <a-breadcrumb class="breadcrumb">
            <a-breadcrumb-item>
              <HomeOutlined />
              <span>首页</span>
            </a-breadcrumb-item>
            <a-breadcrumb-item v-if="currentBreadcrumb">
              {{ currentBreadcrumb }}
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>

        <div class="header-right">
          <!-- 用户信息 -->
          <a-dropdown>
            <a-button type="text" class="user-btn">
              <UserOutlined />
              <span class="user-name">管理员</span>
              <DownOutlined />
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item key="profile">
                  <UserOutlined />
                  个人资料
                </a-menu-item>
                <a-menu-item key="settings">
                  <SettingOutlined />
                  账户设置
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout" @click="handleLogout">
                  <LogoutOutlined />
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>

      <!-- 内容区域 -->
      <a-layout-content class="admin-content">
        <div class="content-wrapper">
          <router-view />
        </div>
      </a-layout-content>

      <!-- 底部 -->
      <a-layout-footer class="admin-footer">
        <div class="footer-content">
          <span>期末复习平台管理后台 © 2025</span>
          <span class="footer-links">
            <a href="#" @click.prevent>帮助文档</a>
            <a-divider type="vertical" />
            <a href="#" @click.prevent>技术支持</a>
          </span>
        </div>
      </a-layout-footer>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  BookOutlined,
  FolderOutlined,
  SettingOutlined,
  UserOutlined,
  HomeOutlined,
  DownOutlined,
  LogoutOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const route = useRoute()

// 响应式状态
const collapsed = ref(false)
const selectedKeys = ref<string[]>([])
const openKeys = ref<string[]>(['content'])

// 计算当前面包屑
const currentBreadcrumb = computed(() => {
  const routeMap: Record<string, string> = {
    'AdminDashboard': '仪表盘',
    'AdminSubjects': '学科管理'
  }
  return routeMap[route.name as string] || ''
})

// 监听路由变化，更新选中的菜单项
watch(
  () => route.name,
  (newName) => {
    const keyMap: Record<string, string> = {
      'AdminDashboard': 'dashboard',
      'AdminSubjects': 'subjects'
    }
    const key = keyMap[newName as string]
    if (key) {
      selectedKeys.value = [key]
      if (key === 'subjects') {
        openKeys.value = ['content']
      }
    }
  },
  { immediate: true }
)

// 切换侧边栏折叠状态
const toggleCollapsed = () => {
  collapsed.value = !collapsed.value
}

// 导航到指定路由
const navigateTo = (routeName: string) => {
  router.push({ name: routeName })
}

// 处理退出登录
const handleLogout = () => {
  message.success('退出登录成功')
  // 这里可以添加实际的登出逻辑
  router.push('/')
}
</script>

<style scoped>
/* 整体布局 */
.admin-layout {
  min-height: 100vh;
}

/* 侧边栏样式 */
.admin-sider {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 100;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
}

/* Logo区域 */
.admin-logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s;
}

.logo-icon {
  font-size: 24px;
  color: #1890ff;
  margin-right: 12px;
  transition: all 0.3s;
}

.logo-text {
  flex: 1;
  overflow: hidden;
}

.logo-title {
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  line-height: 1.2;
  margin-bottom: 2px;
}

.logo-subtitle {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1;
}

/* 菜单样式 */
.admin-menu {
  border-right: none;
  height: calc(100vh - 64px);
  overflow-y: auto;
}

.admin-menu :deep(.ant-menu-item),
.admin-menu :deep(.ant-menu-submenu-title) {
  height: 48px;
  line-height: 48px;
  margin: 0;
  border-radius: 0;
}

.admin-menu :deep(.ant-menu-item-selected) {
  background-color: #1890ff !important;
}

.admin-menu :deep(.ant-menu-item:hover),
.admin-menu :deep(.ant-menu-submenu-title:hover) {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 主体区域 */
.admin-main {
  margin-left: 220px;
  transition: all 0.3s;
}

.admin-layout :deep(.ant-layout-sider-collapsed) + .admin-main {
  margin-left: 80px;
}

/* 顶部导航 */
.admin-header {
  background: #fff;
  padding: 0 24px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 99;
}

.header-left {
  display: flex;
  align-items: center;
}

.trigger-btn {
  font-size: 18px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  border-radius: 6px;
  transition: all 0.3s;
}

.trigger-btn:hover {
  background-color: rgba(0, 0, 0, 0.06);
}

.breadcrumb {
  margin: 0;
}

.breadcrumb :deep(.ant-breadcrumb-link) {
  display: flex;
  align-items: center;
  gap: 4px;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-btn {
  height: 40px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 6px;
  transition: all 0.3s;
}

.user-btn:hover {
  background-color: rgba(0, 0, 0, 0.06);
}

.user-name {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}

/* 内容区域 */
.admin-content {
  margin: 0;
  padding: 0;
  background: #f0f2f5;
  min-height: calc(100vh - 64px - 70px);
}

.content-wrapper {
  padding: 24px;
  min-height: 100%;
}

/* 底部 */
.admin-footer {
  background: #fff;
  border-top: 1px solid #f0f0f0;
  padding: 16px 24px;
  text-align: center;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}

.footer-links a {
  color: rgba(0, 0, 0, 0.45);
  text-decoration: none;
  transition: color 0.3s;
}

.footer-links a:hover {
  color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-sider {
    transform: translateX(-100%);
    transition: transform 0.3s;
  }

  .admin-sider.mobile-visible {
    transform: translateX(0);
  }

  .admin-main {
    margin-left: 0;
  }

  .content-wrapper {
    padding: 16px;
  }

  .footer-content {
    flex-direction: column;
    gap: 8px;
  }
}

/* 滚动条样式 */
.admin-menu::-webkit-scrollbar {
  width: 6px;
}

.admin-menu::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.admin-menu::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.admin-menu::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
