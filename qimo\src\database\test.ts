/**
 * 数据库功能测试脚本
 * 验证数据库连接、迁移和基础CRUD操作
 */

import { initializeDatabase, getDB, withTransaction, closeDatabase } from './index';

async function testDatabase() {
  console.log('🧪 开始数据库功能测试...\n');

  try {
    // 1. 测试数据库初始化
    console.log('1️⃣ 测试数据库初始化...');
    await initializeDatabase();
    console.log('✅ 数据库初始化成功\n');

    // 2. 测试基础查询
    console.log('2️⃣ 测试基础查询...');
    const db = getDB();
    const testResult = db.prepare('SELECT 1 as test').get();
    console.log('✅ 基础查询成功:', testResult);

    // 3. 测试表结构
    console.log('\n3️⃣ 测试表结构...');
    const tables = db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
      ORDER BY name
    `).all();
    console.log('✅ 数据库表:', tables.map(t => t.name).join(', '));

    // 4. 测试学科表CRUD操作
    console.log('\n4️⃣ 测试学科表CRUD操作...');
    
    // 插入测试数据
    const insertSubject = db.prepare('INSERT INTO subjects (name, description) VALUES (?, ?)');
    const subjectResult = insertSubject.run('测试学科', '这是一个测试学科');
    console.log('✅ 插入学科成功, ID:', subjectResult.lastInsertRowid);

    // 查询测试数据
    const selectSubject = db.prepare('SELECT * FROM subjects WHERE id = ?');
    const subject = selectSubject.get(subjectResult.lastInsertRowid);
    console.log('✅ 查询学科成功:', subject);

    // 5. 测试文件节点表操作
    console.log('\n5️⃣ 测试文件节点表操作...');
    
    const insertFileNode = db.prepare(`
      INSERT INTO file_nodes (subject_id, parent_id, name, type, path, size) 
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    
    // 插入根文件夹
    const folderResult = insertFileNode.run(
      subjectResult.lastInsertRowid, null, '根目录', 'folder', '/', 0
    );
    console.log('✅ 插入根文件夹成功, ID:', folderResult.lastInsertRowid);

    // 插入子文件
    const fileResult = insertFileNode.run(
      subjectResult.lastInsertRowid, folderResult.lastInsertRowid, 
      'test.md', 'file', '/test.md', 1024
    );
    console.log('✅ 插入文件成功, ID:', fileResult.lastInsertRowid);

    // 6. 测试事务操作
    console.log('\n6️⃣ 测试事务操作...');
    
    const transactionResult = withTransaction((db) => {
      const stmt1 = db.prepare('INSERT INTO subjects (name) VALUES (?)');
      const result1 = stmt1.run('事务测试学科1');
      
      const stmt2 = db.prepare('INSERT INTO subjects (name) VALUES (?)');
      const result2 = stmt2.run('事务测试学科2');
      
      return { id1: result1.lastInsertRowid, id2: result2.lastInsertRowid };
    });
    
    console.log('✅ 事务操作成功:', transactionResult);

    // 7. 测试索引使用
    console.log('\n7️⃣ 测试索引使用...');
    
    const explainQuery = db.prepare(`
      EXPLAIN QUERY PLAN 
      SELECT * FROM file_nodes WHERE subject_id = ?
    `).all(subjectResult.lastInsertRowid);
    
    console.log('✅ 查询计划:', explainQuery);

    // 8. 测试外键约束
    console.log('\n8️⃣ 测试外键约束...');
    
    try {
      // 尝试插入无效的subject_id
      insertFileNode.run(99999, null, '无效文件', 'file', '/invalid.md', 0);
      console.log('❌ 外键约束测试失败：应该抛出错误');
    } catch (error) {
      console.log('✅ 外键约束正常工作:', error.message);
    }

    // 9. 清理测试数据
    console.log('\n9️⃣ 清理测试数据...');
    
    const deleteFileNodes = db.prepare('DELETE FROM file_nodes WHERE subject_id = ?');
    const deleteSubjects = db.prepare('DELETE FROM subjects WHERE name LIKE ?');
    
    deleteFileNodes.run(subjectResult.lastInsertRowid);
    deleteSubjects.run('%测试%');
    
    console.log('✅ 测试数据清理完成');

    console.log('\n🎉 所有数据库测试通过！');

  } catch (error) {
    console.error('❌ 数据库测试失败:', error);
    throw error;
  } finally {
    // 关闭数据库连接
    closeDatabase();
    console.log('🔒 数据库连接已关闭');
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  testDatabase()
    .then(() => {
      console.log('\n✅ 数据库测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 数据库测试失败:', error);
      process.exit(1);
    });
}

export { testDatabase };