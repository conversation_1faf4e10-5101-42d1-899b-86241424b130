import { describe, test, expect, beforeEach, afterEach } from '@jest/globals'
import request from 'supertest'
import app from '../../src/app.js'
import { SubjectModel } from '../../src/models/Subject.js'
import { FileNodeModel } from '../../src/models/FileNode.js'
import type { Subject, FileNode } from '../../src/types/index.js'

describe('File Management API Integration Tests', () => {
  let testSubject: Subject
  let testFiles: FileNode[]

  beforeEach(async () => {
    // 清理测试数据
    await cleanupTestData()

    // 创建测试学科
    testSubject = SubjectModel.create({ name: '测试学科' })

    // 创建测试文件节点数据
    testFiles = await createTestFileNodes(testSubject.id)
  })

  afterEach(async () => {
    await cleanupTestData()
  })

  async function cleanupTestData() {
    // 清理文件节点
    const allFiles = FileNodeModel.findAll()
    for (const file of allFiles) {
      FileNodeModel.delete(file.id)
    }

    // 清理学科
    const subjects = SubjectModel.findAll()
    for (const subject of subjects) {
      SubjectModel.delete(subject.id)
    }
  }

  async function createTestFileNodes(subjectId: string): Promise<FileNode[]> {
    const nodes: FileNode[] = []

    // 创建文件夹
    const folder1 = FileNodeModel.create({
      id: 'folder-1',
      subject_id: subjectId,
      name: '第一章',
      type: 'folder',
      path: '/第一章',
      parent_id: null,
      size: null
    })
    nodes.push(folder1)

    const folder2 = FileNodeModel.create({
      id: 'folder-2',
      subject_id: subjectId,
      name: '第二章',
      type: 'folder',
      path: '/第二章',
      parent_id: null,
      size: null
    })
    nodes.push(folder2)

    // 创建文件
    const file1 = FileNodeModel.create({
      id: 'file-1',
      subject_id: subjectId,
      name: '概述.pdf',
      type: 'file',
      path: '/第一章/概述.pdf',
      parent_id: 'folder-1',
      size: 1024000
    })
    nodes.push(file1)

    const file2 = FileNodeModel.create({
      id: 'file-2',
      subject_id: subjectId,
      name: '练习题.docx',
      type: 'file',
      path: '/第一章/练习题.docx',
      parent_id: 'folder-1',
      size: 512000
    })
    nodes.push(file2)

    const file3 = FileNodeModel.create({
      id: 'file-3',
      subject_id: subjectId,
      name: '重要文档.txt',
      type: 'file',
      path: '/第二章/重要文档.txt',
      parent_id: 'folder-2',
      size: 256000
    })
    nodes.push(file3)

    return nodes
  }

  describe('GET /api/subjects/:id/files', () => {
    test('应能成功获取根目录文件列表', async () => {
      const startTime = Date.now()

      const response = await request(app.callback())
        .get(`/api/subjects/${testSubject.id}/files`)
        .expect(200)

      const responseTime = Date.now() - startTime
      expect(responseTime).toBeLessThan(2000) // 性能要求：<2秒

      expect(response.body.success).toBe(true)
      expect(response.body.data.files).toBeDefined()
      expect(Array.isArray(response.body.data.files)).toBe(true)
      expect(response.body.data.total).toBeGreaterThanOrEqual(2) // 至少有两个文件夹
      expect(response.body.data.page).toBe(1)
      expect(response.body.data.limit).toBe(10)
      expect(typeof response.body.data.has_more).toBe('boolean')

      // 验证文件夹优先排序
      const folders = response.body.data.files.filter((f: any) => f.type === 'folder')
      expect(folders.length).toBeGreaterThan(0)
    })

    test('应能成功获取指定父目录下的文件列表', async () => {
      const response = await request(app.callback())
        .get(`/api/subjects/${testSubject.id}/files?parent_id=folder-1`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.files).toBeDefined()
      expect(response.body.data.total).toBeGreaterThanOrEqual(2) // folder-1下有两个文件

      // 验证所有文件都属于指定父目录
      response.body.data.files.forEach((file: any) => {
        expect(file.parent_id).toBe('folder-1')
      })
    })

    test('应支持分页查询', async () => {
      const response = await request(app.callback())
        .get(`/api/subjects/${testSubject.id}/files?page=1&limit=1`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.files.length).toBeLessThanOrEqual(1)
      expect(response.body.data.page).toBe(1)
      expect(response.body.data.limit).toBe(1)
    })

    test('应支持搜索功能', async () => {
      const response = await request(app.callback())
        .get(`/api/subjects/${testSubject.id}/files?search=${encodeURIComponent('概述')}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.files).toBeDefined()

      // 验证搜索结果包含关键词
      const foundFile = response.body.data.files.find((f: any) => f.name.includes('概述'))
      expect(foundFile).toBeDefined()
    })

    test('应拒绝无效的学科ID', async () => {
      const response = await request(app.callback())
        .get('/api/subjects/invalid-id/files')
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('VALIDATION_ERROR')
    })

    test('应拒绝不存在的学科', async () => {
      const fakeId = '550e8400-e29b-41d4-a716-446655440000'

      const response = await request(app.callback())
        .get(`/api/subjects/${fakeId}/files`)
        .expect(404)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('NOT_FOUND')
      expect(response.body.message).toContain('学科不存在')
    })

    test('应拒绝无效的父节点ID', async () => {
      const response = await request(app.callback())
        .get(`/api/subjects/${testSubject.id}/files?parent_id=invalid-parent`)
        .expect(404)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('NOT_FOUND')
    })

    test('应正确处理空结果', async () => {
      const response = await request(app.callback())
        .get(`/api/subjects/${testSubject.id}/files?search=${encodeURIComponent('不存在的文件名xyz123')}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.files).toEqual([])
      expect(response.body.data.total).toBe(0)
      expect(response.body.data.has_more).toBe(false)
    })
  })

  describe('GET /api/files/:id/breadcrumb', () => {
    test('应能成功获取根目录的面包屑（空路径）', async () => {
      const startTime = Date.now()

      const response = await request(app.callback())
        .get(`/api/files/root/breadcrumb?subject_id=${testSubject.id}`)
        .expect(200)

      const responseTime = Date.now() - startTime
      expect(responseTime).toBeLessThan(2000) // 性能要求：<2秒

      expect(response.body.success).toBe(true)
      expect(response.body.data.breadcrumb).toEqual([])
      expect(response.body.message).toContain('面包屑路径获取成功')
    })

    test('应能成功获取文件的面包屑路径', async () => {
      const response = await request(app.callback())
        .get(`/api/files/file-1/breadcrumb?subject_id=${testSubject.id}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.breadcrumb).toBeDefined()
      expect(Array.isArray(response.body.data.breadcrumb)).toBe(true)

      // 验证面包屑路径包含父文件夹
      const folderInPath = response.body.data.breadcrumb.find((item: any) => item.id === 'folder-1')
      expect(folderInPath).toBeDefined()
      expect(folderInPath?.name).toBe('第一章')
    })

    test('应能成功获取文件夹的面包屑路径', async () => {
      const response = await request(app.callback())
        .get(`/api/files/folder-1/breadcrumb?subject_id=${testSubject.id}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.breadcrumb).toBeDefined()
      expect(Array.isArray(response.body.data.breadcrumb)).toBe(true)

      // 根目录下的文件夹面包屑应该只包含自己
      expect(response.body.data.breadcrumb.length).toBe(1)
      expect(response.body.data.breadcrumb[0].id).toBe('folder-1')
      expect(response.body.data.breadcrumb[0].name).toBe('第一章')
    })

    test('应拒绝无效的节点ID', async () => {
      const response = await request(app.callback())
        .get('/api/files/invalid-id/breadcrumb')
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('VALIDATION_ERROR')
    })

    test('应拒绝不存在的节点', async () => {
      const fakeId = '550e8400-e29b-41d4-a716-446655440000'

      const response = await request(app.callback())
        .get(`/api/files/${fakeId}/breadcrumb?subject_id=${testSubject.id}`)
        .expect(404)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('NOT_FOUND')
    })

    test('应拒绝空字符串节点ID', async () => {
      const response = await request(app.callback())
        .get(`/api/files//breadcrumb?subject_id=${testSubject.id}`)
        .expect(404) // 路由不匹配

      // 这个测试验证路由层面的处理
    })
  })

  describe('GET /api/subjects/:id/search', () => {
    test('应能成功搜索文件名', async () => {
      const startTime = Date.now()

      const response = await request(app.callback())
        .get(`/api/subjects/${testSubject.id}/search?query=${encodeURIComponent('概述')}`)
        .expect(200)

      const responseTime = Date.now() - startTime
      expect(responseTime).toBeLessThan(2000) // 性能要求：<2秒

      expect(response.body.success).toBe(true)
      expect(response.body.data.files).toBeDefined()
      expect(response.body.data.total).toBeGreaterThanOrEqual(1)
      expect(response.body.data.query).toBe('概述')

      // 验证搜索结果包含关键词
      const foundFile = response.body.data.files.find((f: any) => f.name.includes('概述'))
      expect(foundFile).toBeDefined()
      expect(foundFile?.name).toBe('概述.pdf')
    })

    test('应支持类型过滤 - 仅文件', async () => {
      const response = await request(app.callback())
        .get(`/api/subjects/${testSubject.id}/search?query=${encodeURIComponent('文')}&type=file`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.files).toBeDefined()

      // 验证所有结果都是文件类型
      response.body.data.files.forEach((file: any) => {
        expect(file.type).toBe('file')
      })
    })

    test('应支持类型过滤 - 仅文件夹', async () => {
      const response = await request(app.callback())
        .get(`/api/subjects/${testSubject.id}/search?query=${encodeURIComponent('章')}&type=folder`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.files).toBeDefined()

      // 验证所有结果都是文件夹类型
      response.body.data.files.forEach((file: any) => {
        expect(file.type).toBe('folder')
      })
    })

    test('应支持类型过滤 - 所有类型', async () => {
      const response = await request(app.callback())
        .get(`/api/subjects/${testSubject.id}/search?query=${encodeURIComponent('第')}&type=all`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.files).toBeDefined()
      expect(response.body.data.total).toBeGreaterThanOrEqual(2) // 应该找到"第一章"和"第二章"

      // 验证结果包含不同类型
      const hasFolder = response.body.data.files.some((f: any) => f.type === 'folder')
      expect(hasFolder).toBe(true)
    })

    test('应支持分页查询', async () => {
      const page1 = await request(app.callback())
        .get(`/api/subjects/${testSubject.id}/search?query=${encodeURIComponent('第')}&page=1&limit=1`)
        .expect(200)

      expect(page1.body.data.files.length).toBeLessThanOrEqual(1)
      expect(page1.body.data.page).toBe(1)
      expect(page1.body.data.limit).toBe(1)

      if (page1.body.data.has_more) {
        const page2 = await request(app.callback())
          .get(`/api/subjects/${testSubject.id}/search?query=${encodeURIComponent('第')}&page=2&limit=1`)
          .expect(200)

        expect(page2.body.data.page).toBe(2)
        expect(page2.body.data.limit).toBe(1)
      }
    })

    test('应拒绝无效的学科ID', async () => {
      const response = await request(app.callback())
        .get(`/api/subjects/invalid-id/search?query=${encodeURIComponent('测试')}`)
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('VALIDATION_ERROR')
    })

    test('应拒绝空的搜索关键词', async () => {
      const response = await request(app.callback())
        .get(`/api/subjects/${testSubject.id}/search?query=`)
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('VALIDATION_ERROR')
      expect(response.body.message).toContain('搜索关键词不能为空')
    })

    test('应拒绝过长的搜索关键词', async () => {
      const longQuery = 'a'.repeat(101)

      const response = await request(app.callback())
        .get(`/api/subjects/${testSubject.id}/search?query=${longQuery}`)
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('VALIDATION_ERROR')
      expect(response.body.message).toContain('搜索关键词长度不能超过100个字符')
    })

    test('应拒绝不存在的学科', async () => {
      const fakeId = '550e8400-e29b-41d4-a716-446655440000'

      const response = await request(app.callback())
        .get(`/api/subjects/${fakeId}/search?query=${encodeURIComponent('测试')}`)
        .expect(404)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('NOT_FOUND')
      expect(response.body.message).toContain('学科不存在')
    })

    test('应拒绝无效的类型过滤参数', async () => {
      const response = await request(app.callback())
        .get(`/api/subjects/${testSubject.id}/search?query=${encodeURIComponent('测试')}&type=invalid`)
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('VALIDATION_ERROR')
      expect(response.body.message).toContain('文件类型过滤参数无效')
    })

    test('应正确处理无搜索结果', async () => {
      const response = await request(app.callback())
        .get(`/api/subjects/${testSubject.id}/search?query=${encodeURIComponent('不存在的关键词xyz123')}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.files).toEqual([])
      expect(response.body.data.total).toBe(0)
      expect(response.body.data.has_more).toBe(false)
      expect(response.body.data.query).toBe('不存在的关键词xyz123')
    })
  })
})
