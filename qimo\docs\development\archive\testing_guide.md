# Playwright自动化测试指南

## 概述

本文档描述了期末复习平台的Playwright自动化测试体系，包括测试环境配置、测试用例设计、执行策略和最佳实践。

## 测试架构

### 测试目录结构

```
tests/
├── playwright.config.ts          # Playwright配置文件
├── subject-list.spec.ts          # 学科列表浏览测试
├── file-list.spec.ts             # 文件列表浏览测试
├── markdown-reader.spec.ts       # Markdown阅读测试
├── utils/
│   ├── test-data.ts              # 测试数据管理
│   ├── test-helpers.ts           # 测试辅助工具
│   ├── global-setup.ts           # 全局设置
│   └── global-teardown.ts        # 全局清理
└── test-results/                 # 测试结果输出
    ├── html-report/              # HTML测试报告
    ├── artifacts/                # 测试截图和视频
    ├── results.json              # JSON格式结果
    └── junit.xml                 # JUnit格式结果
```

### 核心组件

#### 1. 测试配置 (playwright.config.ts)
- **多浏览器支持**: Chrome、Firefox、Safari、移动端
- **并发执行**: 优化测试执行时间
- **自动服务器启动**: 前后端服务自动启动
- **报告生成**: HTML、JSON、JUnit多格式报告

#### 2. 测试数据管理 (test-data.ts)
- **数据隔离**: 独立的测试数据库
- **数据重置**: 每次测试前重置数据
- **测试数据**: 预定义的学科和文件数据

#### 3. 测试辅助工具 (test-helpers.ts)
- **通用断言**: 页面标题、URL、元素检查
- **性能测试**: 加载时间、响应时间监控
- **视觉回归**: 截图对比测试
- **响应式测试**: 多设备尺寸测试

## 测试用例覆盖

### 1. 学科列表浏览测试 (subject-list.spec.ts)

**测试场景**:
- ✅ 页面正确加载和标题显示
- ✅ 学科卡片正确展示 (3个测试学科)
- ✅ 点击学科卡片导航功能
- ✅ 加载状态处理
- ✅ 网络错误处理
- ✅ 响应式设计适配
- ✅ 性能指标验证
- ✅ 控制台错误检查
- ✅ 视觉回归测试

**关键验证点**:
- 学科卡片数量: 3个
- 学科信息: 名称、描述、图标正确显示
- 导航功能: 点击跳转到学科详情页
- 性能要求: 加载时间 < 3秒

### 2. 文件列表浏览测试 (file-list.spec.ts)

**测试场景**:
- ✅ 学科详情页面正确加载
- ✅ 文件树结构正确显示
- ✅ 文件夹展开和收起功能
- ✅ 文件选择和导航功能
- ✅ 文件元数据显示
- ✅ 空文件夹处理
- ✅ 面包屑导航功能
- ✅ 加载状态和错误处理
- ✅ 响应式设计和视觉测试

**关键验证点**:
- 文件树展示: Ant Design Tree组件
- 文件夹操作: 展开显示子文件
- 文件导航: 点击跳转到文件查看页
- 面包屑: 学科列表 → 学科详情导航

### 3. Markdown阅读测试 (markdown-reader.spec.ts)

**测试场景**:
- ✅ 文件查看页面正确加载
- ✅ Markdown内容正确渲染
- ✅ 文件元数据显示 (大小、修改时间)
- ✅ 面包屑导航功能
- ✅ 返回按钮功能
- ✅ 不同Markdown文件渲染
- ✅ 文件不存在处理
- ✅ 阅读体验优化验证
- ✅ 最近访问记录
- ✅ 性能和视觉测试

**关键验证点**:
- Markdown渲染: 标题、段落正确显示
- 文件信息: 大小、时间元数据
- 阅读体验: 字体大小、行高适中
- 导航功能: 面包屑和返回按钮

## 测试数据设计

### 测试学科数据
```typescript
const testSubjects = [
  { id: 1, name: '测试数学', description: '数学测试学科', icon: '📐' },
  { id: 2, name: '测试计算机', description: '计算机测试学科', icon: '💻' },
  { id: 3, name: '测试英语', description: '英语测试学科', icon: '🇬🇧' }
]
```

### 测试文件数据
```typescript
const testFiles = [
  // 数学学科文件
  { id: 1, name: '基础数学', type: 'folder', subject_id: 1 },
  { id: 2, name: '函数概念.md', type: 'file', subject_id: 1, parent_id: 1 },
  { id: 3, name: '极限理论.md', type: 'file', subject_id: 1, parent_id: 1 },
  
  // 计算机学科文件
  { id: 4, name: '数据结构', type: 'folder', subject_id: 2 },
  { id: 5, name: '链表.md', type: 'file', subject_id: 2, parent_id: 4 },
  { id: 6, name: '树结构.md', type: 'file', subject_id: 2, parent_id: 4 },
  
  // 英语学科文件
  { id: 7, name: '语法基础', type: 'folder', subject_id: 3 },
  { id: 8, name: '时态.md', type: 'file', subject_id: 3, parent_id: 7 }
]
```

## 执行策略

### 本地开发环境
```bash
# 安装依赖
npm install @playwright/test

# 安装浏览器
npx playwright install

# 运行所有测试
npx playwright test

# 运行特定测试
npx playwright test subject-list.spec.ts

# 调试模式
npx playwright test --debug

# 生成报告
npx playwright show-report
```

### CI/CD环境
```bash
# CI环境配置
export CI=true

# 运行测试 (单进程，重试2次)
npx playwright test --reporter=html,junit

# 上传测试报告
# 报告路径: test-results/html-report/
```

## 性能基准

### 页面加载性能要求
- **总加载时间**: < 3秒
- **DOM内容加载**: < 2秒
- **首次内容绘制**: < 1.5秒

### API响应时间要求
- **学科列表**: < 100ms
- **文件列表**: < 150ms
- **文件内容**: < 200ms

## 视觉回归测试

### 截图对比策略
- **阈值设置**: 20%像素差异容忍度
- **最大差异**: 1000像素
- **测试设备**: 桌面端、平板端、移动端

### 截图命名规范
- `{page-name}-{device}.png`
- 例如: `subject-list-desktop.png`

## 错误处理测试

### 网络错误模拟
- **慢网络**: 1秒延迟模拟
- **网络失败**: 请求中断模拟
- **服务器错误**: 5xx错误模拟

### 边界条件测试
- **空数据**: 无学科、无文件情况
- **大数据**: 大量学科和文件
- **异常数据**: 格式错误、缺失字段

## 最佳实践

### 1. 测试编写原则
- **独立性**: 每个测试用例独立运行
- **可重复**: 测试结果一致可重复
- **清晰性**: 测试意图明确易懂
- **完整性**: 覆盖正常和异常流程

### 2. 数据管理
- **隔离性**: 测试数据与生产数据隔离
- **一致性**: 每次测试使用相同数据
- **清理性**: 测试后自动清理数据

### 3. 断言策略
- **明确性**: 断言条件明确具体
- **稳定性**: 避免时间相关的不稳定断言
- **层次性**: 从页面级到元素级逐层验证

### 4. 维护策略
- **定期更新**: 随功能变更更新测试
- **性能监控**: 持续监控测试执行时间
- **报告分析**: 定期分析测试报告和趋势

## 故障排查

### 常见问题
1. **测试超时**: 检查网络连接和服务器状态
2. **元素定位失败**: 验证选择器和页面结构
3. **截图差异**: 检查浏览器版本和渲染差异
4. **数据不一致**: 验证测试数据重置逻辑

### 调试技巧
- 使用 `--debug` 模式逐步调试
- 查看测试录制的视频和截图
- 检查控制台日志和网络请求
- 使用 `page.pause()` 暂停调试

---

**文档版本**: v1.0
**最后更新**: 2025-07-27
**维护者**: Alex (Engineer)
