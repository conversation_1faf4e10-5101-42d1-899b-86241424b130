import Router from 'koa-router'
import subjectRoutes from './subjects.js'
import fileRoutes from './files.js'

const router = new Router({
  prefix: '/api'
})

// 健康检查
router.get('/health', async (ctx) => {
  ctx.body = {
    success: true,
    message: 'Term Review API is running',
    timestamp: new Date().toISOString(),
    version: '0.1.0'
  }
})

// 注册子路由
router.use('/subjects', subjectRoutes.routes(), subjectRoutes.allowedMethods())

// 注册文件浏览路由
router.use(fileRoutes.routes(), fileRoutes.allowedMethods())

export default router