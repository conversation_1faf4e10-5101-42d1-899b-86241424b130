import { beforeAll, afterAll, beforeEach } from '@jest/globals'
import { closeDatabase, initDatabase, db } from '../src/database/connection.js'
import { existsSync, unlinkSync } from 'fs'
import { join } from 'path'

// 测试数据库路径
const TEST_DB_PATH = join(process.cwd(), 'storage', 'test-term-review.db')

// 全局测试设置
beforeAll(async () => {
  console.log('🔧 Setting up test environment...')

  // 设置测试环境变量
  process.env.NODE_ENV = 'test'

  // 清理可能存在的测试数据库
  if (existsSync(TEST_DB_PATH)) {
    unlinkSync(TEST_DB_PATH)
  }

  // 初始化测试数据库
  initDatabase()
  console.log('✅ Test database initialized')
})

// 每个测试后重置mock数据库，确保测试隔离
afterEach(() => {
  if (process.env.NODE_ENV === 'test' && typeof (db as any).reset === 'function') {
    (db as any).reset()
  }
})

afterAll(async () => {
  console.log('🧹 Cleaning up test environment...')
  closeDatabase()

  // 清理测试数据库文件
  if (existsSync(TEST_DB_PATH)) {
    unlinkSync(TEST_DB_PATH)
    console.log('✅ Test database cleaned up')
  }
})

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection in tests:', promise, 'reason:', reason)
})

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception in tests:', error)
})