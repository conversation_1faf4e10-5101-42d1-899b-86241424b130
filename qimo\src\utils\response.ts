/**
 * 统一响应格式工具
 * 标准化API响应结构，确保前后端数据交互一致性
 */

import { Context } from 'koa';

// 响应数据接口
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
  requestId?: string;
}

// 错误代码枚举
export enum ErrorCode {
  // 通用错误
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  INVALID_REQUEST = 'INVALID_REQUEST',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  
  // 认证授权错误
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  
  // 资源错误
  NOT_FOUND = 'NOT_FOUND',
  RESOURCE_EXISTS = 'RESOURCE_EXISTS',
  
  // 数据库错误
  DATABASE_ERROR = 'DATABASE_ERROR',
  CONSTRAINT_VIOLATION = 'CONSTRAINT_VIOLATION',
  
  // 业务逻辑错误
  BUSINESS_ERROR = 'BUSINESS_ERROR',
  
  // 外部服务错误
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR'
}

// HTTP状态码映射
const statusCodeMap: Record<ErrorCode, number> = {
  [ErrorCode.INTERNAL_ERROR]: 500,
  [ErrorCode.INVALID_REQUEST]: 400,
  [ErrorCode.VALIDATION_ERROR]: 400,
  [ErrorCode.UNAUTHORIZED]: 401,
  [ErrorCode.FORBIDDEN]: 403,
  [ErrorCode.NOT_FOUND]: 404,
  [ErrorCode.RESOURCE_EXISTS]: 409,
  [ErrorCode.DATABASE_ERROR]: 500,
  [ErrorCode.CONSTRAINT_VIOLATION]: 400,
  [ErrorCode.BUSINESS_ERROR]: 422,
  [ErrorCode.EXTERNAL_SERVICE_ERROR]: 502
};

/**
 * 成功响应
 */
export function success<T>(ctx: Context, data?: T, statusCode: number = 200): void {
  const response: ApiResponse<T> = {
    success: true,
    data,
    timestamp: new Date().toISOString(),
    requestId: ctx.state.requestId
  };
  
  ctx.status = statusCode;
  ctx.body = response;
}

/**
 * 错误响应
 */
export function error(
  ctx: Context, 
  code: ErrorCode, 
  message: string, 
  details?: any,
  statusCode?: number
): void {
  const response: ApiResponse = {
    success: false,
    error: {
      code,
      message,
      details
    },
    timestamp: new Date().toISOString(),
    requestId: ctx.state.requestId
  };
  
  ctx.status = statusCode || statusCodeMap[code] || 500;
  ctx.body = response;
}

/**
 * 分页响应
 */
export interface PaginationMeta {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: PaginationMeta;
}

export function paginated<T>(
  ctx: Context,
  items: T[],
  page: number,
  pageSize: number,
  total: number
): void {
  const totalPages = Math.ceil(total / pageSize);
  
  const paginationMeta: PaginationMeta = {
    page,
    pageSize,
    total,
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1
  };
  
  const response: PaginatedResponse<T> = {
    items,
    pagination: paginationMeta
  };
  
  success(ctx, response);
}

/**
 * 创建响应工具类
 */
export class ResponseHelper {
  constructor(private ctx: Context) {}
  
  success<T>(data?: T, statusCode?: number): void {
    success(this.ctx, data, statusCode);
  }
  
  error(code: ErrorCode, message: string, details?: any, statusCode?: number): void {
    error(this.ctx, code, message, details, statusCode);
  }
  
  paginated<T>(items: T[], page: number, pageSize: number, total: number): void {
    paginated(this.ctx, items, page, pageSize, total);
  }
  
  // 便捷方法
  notFound(message: string = '资源不存在'): void {
    this.error(ErrorCode.NOT_FOUND, message);
  }
  
  badRequest(message: string = '请求参数错误'): void {
    this.error(ErrorCode.INVALID_REQUEST, message);
  }
  
  unauthorized(message: string = '未授权访问'): void {
    this.error(ErrorCode.UNAUTHORIZED, message);
  }
  
  forbidden(message: string = '禁止访问'): void {
    this.error(ErrorCode.FORBIDDEN, message);
  }
  
  internalError(message: string = '服务器内部错误'): void {
    this.error(ErrorCode.INTERNAL_ERROR, message);
  }
  
  validationError(message: string, details?: any): void {
    this.error(ErrorCode.VALIDATION_ERROR, message, details);
  }
}

/**
 * 响应中间件 - 为ctx添加响应工具
 */
export function responseMiddleware() {
  return async (ctx: Context, next: () => Promise<void>) => {
    // 生成请求ID
    ctx.state.requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 添加响应工具到ctx
    ctx.apiResponse = new ResponseHelper(ctx);
    
    await next();
  };
}

// 扩展Koa Context类型
declare module 'koa' {
  interface Context {
    response: ResponseHelper;
  }
}