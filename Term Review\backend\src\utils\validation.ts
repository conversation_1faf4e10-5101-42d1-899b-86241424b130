import Joi from 'joi'

// 学科创建验证规则
export const createSubjectSchema = Joi.object({
  name: Joi.string()
    .trim()
    .min(1)
    .max(50)
    .pattern(/^[^<>:"/\\|?*]+$/)
    .required()
    .messages({
      'string.empty': '学科名称不能为空',
      'string.min': '学科名称至少需要1个字符',
      'string.max': '学科名称不能超过50个字符',
      'string.pattern.base': '学科名称不能包含特殊字符 < > : " / \\ | ? *',
      'any.required': '学科名称是必填项'
    })
})

// 学科更新验证规则
export const updateSubjectSchema = Joi.object({
  name: Joi.string()
    .trim()
    .min(1)
    .max(50)
    .pattern(/^[^<>:"/\\|?*]+$/)
    .optional()
    .messages({
      'string.empty': '学科名称不能为空',
      'string.min': '学科名称至少需要1个字符',
      'string.max': '学科名称不能超过50个字符',
      'string.pattern.base': '学科名称不能包含特殊字符 < > : " / \\ | ? *'
    })
})

// UUID验证规则
export const uuidSchema = Joi.string()
  .uuid()
  .required()
  .messages({
    'string.guid': '无效的学科ID格式',
    'any.required': '学科ID是必填项'
  })

// 分页参数验证规则
export const paginationSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  pageSize: Joi.number().integer().min(1).max(100).default(20),
  sortBy: Joi.string().valid('name', 'createdAt', 'updatedAt', 'fileCount').default('createdAt'),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc')
})

// 文件浏览查询参数验证规则
export const getFilesQuerySchema = Joi.object({
  parent_id: Joi.string().uuid().optional().allow('').messages({
    'string.guid': '无效的父节点ID格式'
  }),
  search: Joi.string().trim().max(100).optional().allow('').messages({
    'string.max': '搜索关键词不能超过100个字符'
  }),
  page: Joi.number().integer().min(1).default(1).messages({
    'number.min': '页码必须大于0'
  }),
  limit: Joi.number().integer().min(1).max(100).default(20).messages({
    'number.min': '每页数量必须大于0',
    'number.max': '每页数量不能超过100'
  })
})

// 文件搜索查询参数验证规则
export const searchFilesQuerySchema = Joi.object({
  query: Joi.string().trim().min(1).max(100).required().messages({
    'string.empty': '搜索关键词不能为空',
    'string.min': '搜索关键词至少需要1个字符',
    'string.max': '搜索关键词不能超过100个字符',
    'any.required': '搜索关键词是必填项'
  }),
  type: Joi.string().valid('file', 'folder', 'all').default('all').messages({
    'any.only': '文件类型必须是 file、folder 或 all'
  }),
  page: Joi.number().integer().min(1).default(1).messages({
    'number.min': '页码必须大于0'
  }),
  limit: Joi.number().integer().min(1).max(100).default(20).messages({
    'number.min': '每页数量必须大于0',
    'number.max': '每页数量不能超过100'
  })
})

// 面包屑查询参数验证规则
export const getBreadcrumbQuerySchema = Joi.object({
  subject_id: Joi.string().uuid().required().messages({
    'string.guid': '无效的学科ID格式',
    'any.required': '学科ID不能为空'
  }),
  node_id: Joi.string().uuid().optional().allow('').messages({
    'string.guid': '无效的节点ID格式'
  })
})