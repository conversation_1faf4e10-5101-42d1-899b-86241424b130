import { describe, test, expect, beforeAll, afterAll } from '@jest/globals'

describe('Subject Management API Integration', () => {
  const baseUrl = 'http://localhost:3000/api'

  beforeAll(async () => {
    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 2000))
  })

  afterAll(async () => {
    // 清理测试数据
    try {
      const response = await fetch(`${baseUrl}/subjects`)
      const data = await response.json() as any

      if (data.success && data.data) {
        for (const subject of data.data) {
          await fetch(`${baseUrl}/subjects/${subject.id}`, {
            method: 'DELETE'
          })
        }
      }
    } catch (error: any) {
      console.log('Cleanup error (expected):', error.message)
    }
  })

  test('健康检查API应正常工作', async () => {
    const response = await fetch(`${baseUrl}/health`)
    const data = await response.json() as any

    expect(response.status).toBe(200)
    expect(data.success).toBe(true)
    expect(data.message).toContain('running')
  })

  test('应能创建学科', async () => {
    const response = await fetch(`${baseUrl}/subjects`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ name: '计算机科学' })
    })

    const data = await response.json() as any

    expect(response.status).toBe(201)
    expect(data.success).toBe(true)
    expect(data.data.name).toBe('计算机科学')
    expect(data.data.id).toBeDefined()
  })

  test('应拒绝重复的学科名称', async () => {
    // 先创建一个学科
    await fetch(`${baseUrl}/subjects`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ name: '数学' })
    })

    // 尝试创建重复学科
    const response = await fetch(`${baseUrl}/subjects`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ name: '数学' })
    })

    const data = await response.json() as any

    expect(response.status).toBe(409)
    expect(data.success).toBe(false)
    expect(data.error).toBe('CONFLICT')
  })

  test('应能获取学科列表', async () => {
    const response = await fetch(`${baseUrl}/subjects`)
    const data = await response.json() as any

    expect(response.status).toBe(200)
    expect(data.success).toBe(true)
    expect(Array.isArray(data.data)).toBe(true)
    expect(typeof data.total).toBe('number')
  })

  test('应拒绝无效的学科名称', async () => {
    const response = await fetch(`${baseUrl}/subjects`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ name: '' })
    })

    const data = await response.json() as any

    expect(response.status).toBe(400)
    expect(data.success).toBe(false)
    expect(data.error).toBe('VALIDATION_ERROR')
  })
})