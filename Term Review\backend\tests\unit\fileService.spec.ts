﻿import { describe, test, expect, beforeEach } from '@jest/globals'
import { FileService } from '../../src/services/FileService.js'
import { SubjectService } from '../../src/services/SubjectService.js'

describe('FileService Unit Tests', () => {
  let testSubjectId: string

  beforeEach(async () => {
    const subject = await SubjectService.createSubject({ name: '测试学科' })
    testSubjectId = subject.id
  })

  describe('getFilesByParent', () => {
    test('应拒绝空的学科ID', async () => {
      await expect(FileService.getFilesByParent({
        subject_id: '',
        page: 1,
        limit: 10
      })).rejects.toThrow('学科ID不能为空')
    })

    test('应拒绝不存在的学科', async () => {
      const fakeId = '550e8400-e29b-41d4-a716-446655440000'
      await expect(FileService.getFilesByParent({
        subject_id: fakeId,
        page: 1,
        limit: 10
      })).rejects.toThrow('学科不存在')
    })

    test('应能获取根目录下的文件列表', async () => {
      const result = await FileService.getFilesByParent({
        subject_id: testSubjectId,
        page: 1,
        limit: 10
      })

      expect(result.files).toBeDefined()
      expect(Array.isArray(result.files)).toBe(true)
      expect(result.total).toBeDefined()
      expect(result.page).toBe(1)
      expect(result.limit).toBe(10)
      expect(typeof result.has_more).toBe('boolean')
    })
  })

  describe('getBreadcrumb', () => {
    test('应拒绝空的学科ID', async () => {
      await expect(FileService.getBreadcrumb({
        subject_id: ''
      })).rejects.toThrow('学科ID不能为空')
    })

    test('应能获取根目录的面包屑', async () => {
      const result = await FileService.getBreadcrumb({
        subject_id: testSubjectId
      })

      expect(result.breadcrumb).toEqual([])
    })
  })

  describe('searchFiles', () => {
    test('应拒绝空的学科ID', async () => {
      await expect(FileService.searchFiles({
        subject_id: '',
        query: '测试',
        page: 1,
        limit: 10
      })).rejects.toThrow('学科ID不能为空')
    })

    test('应拒绝空的搜索关键词', async () => {
      await expect(FileService.searchFiles({
        subject_id: testSubjectId,
        query: '',
        page: 1,
        limit: 10
      })).rejects.toThrow('搜索关键词不能为空')
    })

    test('应能搜索文件', async () => {
      const result = await FileService.searchFiles({
        subject_id: testSubjectId,
        query: '测试',
        page: 1,
        limit: 10
      })

      expect(result.files).toBeDefined()
      expect(Array.isArray(result.files)).toBe(true)
      expect(result.total).toBeDefined()
      expect(result.page).toBe(1)
      expect(result.limit).toBe(10)
      expect(typeof result.has_more).toBe('boolean')
      expect(result.query).toBe('测试')
    })
  })
})
