import { Reporter, TestCase, TestResult, FullResult } from '@playwright/test/reporter'
import fs from 'fs'
import path from 'path'

/**
 * 自定义性能报告器
 * 收集和分析测试性能数据
 */
class PerformanceReporter implements Reporter {
  private performanceData: any[] = []
  private startTime: number = 0
  private outputDir: string = 'test-results'

  onBegin() {
    this.startTime = Date.now()
    console.log('🚀 开始管理功能性能测试...')
    
    // 确保输出目录存在
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true })
    }
  }

  onTestEnd(test: TestCase, result: TestResult) {
    const testData = {
      title: test.title,
      file: test.location?.file || '',
      duration: result.duration,
      status: result.status,
      startTime: result.startTime,
      retry: result.retry,
      workerIndex: result.workerIndex,
      errors: result.errors.map(error => ({
        message: error.message,
        location: error.location
      }))
    }

    // 收集性能相关的测试数据
    if (test.title.includes('性能') || test.title.includes('performance') || 
        test.location?.file.includes('performance')) {
      this.performanceData.push({
        ...testData,
        isPerformanceTest: true,
        thresholds: this.extractThresholds(test.title)
      })
    } else {
      this.performanceData.push({
        ...testData,
        isPerformanceTest: false
      })
    }

    // 输出实时性能信息
    if (testData.isPerformanceTest) {
      const status = result.status === 'passed' ? '✅' : '❌'
      console.log(`${status} ${test.title}: ${result.duration}ms`)
    }
  }

  onEnd(result: FullResult) {
    const endTime = Date.now()
    const totalDuration = endTime - this.startTime

    // 生成性能报告
    const report = this.generatePerformanceReport(result, totalDuration)
    
    // 保存详细报告
    this.saveDetailedReport(report)
    
    // 保存性能数据
    this.savePerformanceData()
    
    // 输出摘要
    this.printSummary(report)
  }

  private extractThresholds(title: string): any {
    const thresholds: any = {}
    
    // 从测试标题中提取性能阈值
    const patterns = [
      { regex: /(\d+)ms/, key: 'responseTime' },
      { regex: /(\d+)秒/, key: 'loadTime' },
      { regex: /小于(\d+)/, key: 'threshold' }
    ]

    patterns.forEach(pattern => {
      const match = title.match(pattern.regex)
      if (match) {
        thresholds[pattern.key] = parseInt(match[1])
      }
    })

    return thresholds
  }

  private generatePerformanceReport(result: FullResult, totalDuration: number) {
    const performanceTests = this.performanceData.filter(test => test.isPerformanceTest)
    const regularTests = this.performanceData.filter(test => !test.isPerformanceTest)

    // 性能统计
    const performanceStats = {
      total: performanceTests.length,
      passed: performanceTests.filter(test => test.status === 'passed').length,
      failed: performanceTests.filter(test => test.status === 'failed').length,
      avgDuration: performanceTests.length > 0 
        ? performanceTests.reduce((sum, test) => sum + test.duration, 0) / performanceTests.length 
        : 0,
      maxDuration: performanceTests.length > 0 
        ? Math.max(...performanceTests.map(test => test.duration)) 
        : 0,
      minDuration: performanceTests.length > 0 
        ? Math.min(...performanceTests.map(test => test.duration)) 
        : 0
    }

    // 功能测试统计
    const functionalStats = {
      total: regularTests.length,
      passed: regularTests.filter(test => test.status === 'passed').length,
      failed: regularTests.filter(test => test.status === 'failed').length,
      avgDuration: regularTests.length > 0 
        ? regularTests.reduce((sum, test) => sum + test.duration, 0) / regularTests.length 
        : 0
    }

    // 性能分析
    const performanceAnalysis = this.analyzePerformance(performanceTests)

    return {
      timestamp: new Date().toISOString(),
      totalDuration,
      summary: {
        totalTests: this.performanceData.length,
        passed: result.status === 'passed',
        performance: performanceStats,
        functional: functionalStats
      },
      performanceAnalysis,
      slowestTests: this.performanceData
        .sort((a, b) => b.duration - a.duration)
        .slice(0, 10),
      failedTests: this.performanceData.filter(test => test.status === 'failed')
    }
  }

  private analyzePerformance(performanceTests: any[]) {
    const analysis = {
      pageLoadTests: [],
      apiResponseTests: [],
      userInteractionTests: [],
      recommendations: []
    }

    performanceTests.forEach(test => {
      if (test.title.includes('页面加载') || test.title.includes('load')) {
        analysis.pageLoadTests.push(test)
      } else if (test.title.includes('API') || test.title.includes('响应')) {
        analysis.apiResponseTests.push(test)
      } else if (test.title.includes('交互') || test.title.includes('操作')) {
        analysis.userInteractionTests.push(test)
      }
    })

    // 生成建议
    if (analysis.pageLoadTests.some(test => test.duration > 3000)) {
      analysis.recommendations.push('页面加载时间超过3秒，建议优化资源加载')
    }
    
    if (analysis.apiResponseTests.some(test => test.duration > 1000)) {
      analysis.recommendations.push('API响应时间超过1秒，建议优化后端性能')
    }

    if (analysis.userInteractionTests.some(test => test.duration > 500)) {
      analysis.recommendations.push('用户交互响应时间较慢，建议优化前端性能')
    }

    return analysis
  }

  private saveDetailedReport(report: any) {
    const reportPath = path.join(this.outputDir, 'admin-performance-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
    
    // 生成HTML报告
    const htmlReport = this.generateHtmlReport(report)
    const htmlPath = path.join(this.outputDir, 'admin-performance-report.html')
    fs.writeFileSync(htmlPath, htmlReport)
  }

  private savePerformanceData() {
    const dataPath = path.join(this.outputDir, 'admin-performance-data.json')
    fs.writeFileSync(dataPath, JSON.stringify(this.performanceData, null, 2))
  }

  private generateHtmlReport(report: any): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>管理功能性能测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .stats { display: flex; gap: 20px; margin: 20px 0; }
        .stat-card { background: #fff; border: 1px solid #ddd; padding: 15px; border-radius: 5px; flex: 1; }
        .passed { color: #52c41a; }
        .failed { color: #ff4d4f; }
        .recommendations { background: #fff7e6; border: 1px solid #ffd591; padding: 15px; border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f5f5f5; }
    </style>
</head>
<body>
    <div class="header">
        <h1>管理功能性能测试报告</h1>
        <p>生成时间: ${report.timestamp}</p>
        <p>总耗时: ${report.totalDuration}ms</p>
    </div>

    <div class="stats">
        <div class="stat-card">
            <h3>性能测试</h3>
            <p>总数: ${report.summary.performance.total}</p>
            <p class="passed">通过: ${report.summary.performance.passed}</p>
            <p class="failed">失败: ${report.summary.performance.failed}</p>
            <p>平均耗时: ${Math.round(report.summary.performance.avgDuration)}ms</p>
        </div>
        <div class="stat-card">
            <h3>功能测试</h3>
            <p>总数: ${report.summary.functional.total}</p>
            <p class="passed">通过: ${report.summary.functional.passed}</p>
            <p class="failed">失败: ${report.summary.functional.failed}</p>
            <p>平均耗时: ${Math.round(report.summary.functional.avgDuration)}ms</p>
        </div>
    </div>

    ${report.performanceAnalysis.recommendations.length > 0 ? `
    <div class="recommendations">
        <h3>性能优化建议</h3>
        <ul>
            ${report.performanceAnalysis.recommendations.map(rec => `<li>${rec}</li>`).join('')}
        </ul>
    </div>
    ` : ''}

    <h3>最慢的测试</h3>
    <table>
        <tr>
            <th>测试名称</th>
            <th>耗时(ms)</th>
            <th>状态</th>
            <th>文件</th>
        </tr>
        ${report.slowestTests.map(test => `
        <tr>
            <td>${test.title}</td>
            <td>${test.duration}</td>
            <td class="${test.status}">${test.status}</td>
            <td>${path.basename(test.file)}</td>
        </tr>
        `).join('')}
    </table>

    ${report.failedTests.length > 0 ? `
    <h3>失败的测试</h3>
    <table>
        <tr>
            <th>测试名称</th>
            <th>错误信息</th>
            <th>文件</th>
        </tr>
        ${report.failedTests.map(test => `
        <tr>
            <td>${test.title}</td>
            <td>${test.errors.map(e => e.message).join(', ')}</td>
            <td>${path.basename(test.file)}</td>
        </tr>
        `).join('')}
    </table>
    ` : ''}
</body>
</html>
    `
  }

  private printSummary(report: any) {
    console.log('\n📊 管理功能测试摘要:')
    console.log(`   总测试数: ${report.summary.totalTests}`)
    console.log(`   性能测试: ${report.summary.performance.passed}/${report.summary.performance.total} 通过`)
    console.log(`   功能测试: ${report.summary.functional.passed}/${report.summary.functional.total} 通过`)
    console.log(`   总耗时: ${report.totalDuration}ms`)
    
    if (report.performanceAnalysis.recommendations.length > 0) {
      console.log('\n💡 性能优化建议:')
      report.performanceAnalysis.recommendations.forEach((rec: string) => {
        console.log(`   • ${rec}`)
      })
    }
    
    console.log(`\n📄 详细报告: test-results/admin-performance-report.html`)
  }
}

export default PerformanceReporter
