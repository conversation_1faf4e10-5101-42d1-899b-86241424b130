import { test, expect } from '@playwright/test'
import { createTestHelpers } from './utils/test-helpers'
import { testDataManager } from './utils/test-data'

/**
 * 学科列表浏览测试
 * 测试学科列表页面的加载、展示和导航功能
 */

test.describe('学科列表浏览', () => {
  test.beforeEach(async ({ page }) => {
    // 重置测试数据
    await testDataManager.resetTestData()
    
    // 导航到学科列表页
    await page.goto('/subjects')
  })

  test('应该正确加载学科列表页面', async ({ page }) => {
    const helpers = createTestHelpers(page)
    
    // 等待页面加载完成
    await helpers.waitForPageLoad()
    
    // 检查页面标题
    await helpers.checkPageTitle('学科列表 - 期末复习平台')
    
    // 检查页面URL
    await helpers.checkPageUrl(/\/subjects$/)
    
    // 检查页面标题元素
    await helpers.checkElementVisible('h1')
    await helpers.checkElementText('h1', '学科列表')
    
    // 检查页面描述
    await helpers.checkElementText('p', '选择一个学科开始浏览复习资料')
  })

  test('应该正确显示学科卡片', async ({ page }) => {
    const helpers = createTestHelpers(page)
    
    // 等待API响应
    await helpers.waitForApiResponse('/subjects')
    await helpers.waitForPageLoad()
    
    // 检查学科卡片数量
    await helpers.checkElementCount('.subject-card', 3)
    
    // 检查第一个学科卡片 - 测试数学
    const mathCard = page.locator('.subject-card').first()
    await expect(mathCard).toBeVisible()
    await expect(mathCard.locator('h3')).toHaveText('测试数学')
    await expect(mathCard.locator('p')).toHaveText('数学测试学科')
    await expect(mathCard.locator('.feature-icon')).toHaveText('📐')
    
    // 检查第二个学科卡片 - 测试计算机
    const csCard = page.locator('.subject-card').nth(1)
    await expect(csCard).toBeVisible()
    await expect(csCard.locator('h3')).toHaveText('测试计算机')
    await expect(csCard.locator('p')).toHaveText('计算机测试学科')
    await expect(csCard.locator('.feature-icon')).toHaveText('💻')
    
    // 检查第三个学科卡片 - 测试英语
    const englishCard = page.locator('.subject-card').nth(2)
    await expect(englishCard).toBeVisible()
    await expect(englishCard.locator('h3')).toHaveText('测试英语')
    await expect(englishCard.locator('p')).toHaveText('英语测试学科')
    await expect(englishCard.locator('.feature-icon')).toHaveText('🇬🇧')
  })

  test('应该支持点击学科卡片导航', async ({ page }) => {
    const helpers = createTestHelpers(page)
    
    // 等待页面加载
    await helpers.waitForApiResponse('/subjects')
    await helpers.waitForPageLoad()
    
    // 点击第一个学科卡片
    const mathCard = page.locator('.subject-card').first()
    await mathCard.click()
    
    // 检查导航到学科详情页
    await helpers.checkPageUrl(/\/subjects\/1$/)
    await page.waitForLoadState('networkidle')
    
    // 检查学科详情页标题
    await helpers.checkElementVisible('h1')
    await helpers.checkElementText('h1', '测试数学')
  })

  test('应该正确处理加载状态', async ({ page }) => {
    const helpers = createTestHelpers(page)
    
    // 模拟慢网络
    await helpers.simulateSlowNetwork()
    
    // 重新加载页面
    await page.reload()
    
    // 检查加载状态
    await helpers.checkLoadingState(true)
    
    // 等待加载完成
    await helpers.waitForApiResponse('/subjects')
    await helpers.waitForPageLoad()
    
    // 检查加载状态消失
    await helpers.checkLoadingState(false)
    
    // 检查内容正确显示
    await helpers.checkElementCount('.subject-card', 3)
  })

  test('应该正确处理网络错误', async ({ page }) => {
    const helpers = createTestHelpers(page)
    
    // 模拟网络错误
    await helpers.simulateNetworkError(/\/subjects/)
    
    // 重新加载页面
    await page.reload()
    
    // 等待错误状态
    await page.waitForTimeout(2000)
    
    // 检查错误提示
    await helpers.checkElementVisible('.ant-empty')
    await helpers.checkElementText('.ant-empty-description', /加载失败|网络错误/)
  })

  test('应该支持响应式设计', async ({ page }) => {
    const helpers = createTestHelpers(page)
    
    // 等待页面加载
    await helpers.waitForApiResponse('/subjects')
    await helpers.waitForPageLoad()
    
    // 测试响应式设计
    await helpers.checkResponsiveDesign()
  })

  test('应该通过性能测试', async ({ page }) => {
    const helpers = createTestHelpers(page)
    
    // 等待页面加载
    await helpers.waitForPageLoad()
    
    // 测量性能
    const metrics = await helpers.measurePerformance()
    
    // 检查性能指标
    expect(metrics.loadTime).toBeLessThan(3000) // 加载时间小于3秒
    expect(metrics.domContentLoaded).toBeLessThan(2000) // DOM加载小于2秒
    expect(metrics.firstContentfulPaint).toBeLessThan(1500) // 首次内容绘制小于1.5秒
  })

  test('应该没有控制台错误', async ({ page }) => {
    const helpers = createTestHelpers(page)
    
    // 等待页面加载
    await helpers.waitForApiResponse('/subjects')
    await helpers.waitForPageLoad()
    
    // 检查控制台错误
    await helpers.checkConsoleErrors()
  })

  test('应该支持视觉回归测试', async ({ page }) => {
    const helpers = createTestHelpers(page)
    
    // 等待页面加载
    await helpers.waitForApiResponse('/subjects')
    await helpers.waitForPageLoad()
    
    // 截图对比
    await helpers.compareScreenshot('subject-list-page', { fullPage: true })
  })
})
