/**
 * 管理后台认证中间件
 * 基于URL路径和请求头验证管理后台访问权限
 */

import { Context, Next } from 'koa';
import { config } from '../config';

/**
 * 管理后台认证中间件
 * 检查请求路径和x-admin-path请求头，验证管理后台访问权限
 */
export function adminAuthMiddleware() {
  return async (ctx: Context, next: Next) => {
    const { admin } = config;
    
    // 如果管理后台功能被禁用，直接继续
    if (!admin.enabled) {
      return await next();
    }
    
    const requestPath = ctx.path;
    const adminHeader = ctx.get(admin.accessHeader);
    
    // 检查是否是管理后台相关的请求
    const isAdminRequest = requestPath.startsWith('/api/admin') || 
                          requestPath === admin.panelPath ||
                          requestPath.startsWith(admin.panelPath + '/');
    
    if (isAdminRequest) {
      // 验证管理后台访问权限
      if (!adminHeader || adminHeader !== admin.panelPath) {
        ctx.status = 403;
        ctx.body = {
          success: false,
          error: 'ADMIN_ACCESS_DENIED',
          message: '管理后台访问被拒绝'
        };
        return;
      }
      
      // 设置管理员标识到上下文
      ctx.state.isAdmin = true;
      ctx.state.adminPath = admin.panelPath;
    }
    
    await next();
  };
}

/**
 * 检查是否为管理员请求的工具函数
 */
export function isAdminRequest(ctx: Context): boolean {
  return ctx.state.isAdmin === true;
}

/**
 * 获取管理后台路径的工具函数
 */
export function getAdminPanelPath(): string {
  return config.admin.panelPath;
}

/**
 * 检查管理后台是否启用的工具函数
 */
export function isAdminEnabled(): boolean {
  return config.admin.enabled;
}
