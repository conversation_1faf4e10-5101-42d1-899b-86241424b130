/**
 * 中间件配置模块
 * 统一管理所有Koa中间件的配置和注册
 */

import Koa from 'koa';
import cors from 'koa-cors';
import bodyParser from 'koa-bodyparser';
import logger from 'koa-logger';
import { config } from '../config';
import { responseMiddleware } from '../utils/response';
import { errorHandler } from './error';
import { requestLogger } from './logger';
import { rateLimiter } from './rateLimit';
import { adminAuthMiddleware } from './adminAuth';

/**
 * 注册所有中间件
 */
export function setupMiddleware(app: Koa): void {
  // 1. 错误处理中间件（最先注册，捕获所有错误）
  app.use(errorHandler());

  // 2. 请求日志中间件
  if (config.app.env === 'development') {
    app.use(logger()); // koa-logger用于开发环境
  }
  app.use(requestLogger()); // 自定义日志中间件

  // 3. 跨域处理中间件
  app.use(cors({
    origin: config.security.cors.origin,
    credentials: config.security.cors.credentials,
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowHeaders: ['Content-Type', 'Authorization', 'Accept', 'X-Requested-With', 'x-admin-path'],
    exposeHeaders: ['X-Request-Id', 'X-Response-Time']
  }));

  // 3.5. 管理后台认证中间件
  app.use(adminAuthMiddleware());

  // 4. 限流中间件
  app.use(rateLimiter());

  // 5. 请求体解析中间件
  app.use(bodyParser({
    enableTypes: ['json', 'form', 'text'],
    jsonLimit: '10mb',
    formLimit: '10mb',
    textLimit: '10mb',
    strict: true,
    onerror: (err, ctx) => {
      ctx.throw(400, '请求体解析失败: ' + err.message);
    }
  }));

  // 6. 响应工具中间件
  app.use(responseMiddleware());

  // 7. 请求时间统计中间件
  app.use(async (ctx, next) => {
    const start = Date.now();
    await next();
    const ms = Date.now() - start;
    ctx.set('X-Response-Time', `${ms}ms`);
    ctx.set('X-Request-Id', ctx.state.requestId);
  });

  console.log('✅ 中间件配置完成');
}

// 导出各个中间件模块
export { errorHandler } from './error';
export { requestLogger } from './logger';
export { rateLimiter } from './rateLimit';
export { adminAuthMiddleware, isAdminRequest, getAdminPanelPath, isAdminEnabled } from './adminAuth';