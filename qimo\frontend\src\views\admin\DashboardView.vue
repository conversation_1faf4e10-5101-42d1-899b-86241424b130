<template>
  <div class="admin-dashboard">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <a-card :bordered="false" class="welcome-card">
        <div class="welcome-content">
          <div class="welcome-left">
            <h1 class="welcome-title">欢迎回来！</h1>
            <p class="welcome-subtitle">期末复习平台管理后台 - 让管理更简单高效</p>
            <div class="welcome-time">
              <ClockCircleOutlined />
              <span>{{ currentTime }}</span>
            </div>
          </div>
          <div class="welcome-right">
            <div class="welcome-avatar">
              <a-avatar :size="80" style="background-color: #1890ff">
                <UserOutlined />
              </a-avatar>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <a-row :gutter="16">
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card :bordered="false" class="stat-card">
            <a-statistic
              title="总学科数"
              :value="statsData.totalSubjects"
              :loading="statsLoading"
            >
              <template #prefix>
                <BookOutlined class="stat-icon" style="color: #1890ff" />
              </template>
            </a-statistic>
            <div class="stat-trend">
              <span class="trend-text">较昨日</span>
              <span class="trend-value positive">+2</span>
            </div>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card :bordered="false" class="stat-card">
            <a-statistic
              title="总文件数"
              :value="statsData.totalFiles"
              :loading="statsLoading"
            >
              <template #prefix>
                <FileOutlined class="stat-icon" style="color: #52c41a" />
              </template>
            </a-statistic>
            <div class="stat-trend">
              <span class="trend-text">较昨日</span>
              <span class="trend-value positive">+15</span>
            </div>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card :bordered="false" class="stat-card">
            <a-statistic
              title="存储空间"
              :value="formatFileSize(statsData.totalSize)"
              :loading="statsLoading"
            >
              <template #prefix>
                <DatabaseOutlined class="stat-icon" style="color: #faad14" />
              </template>
            </a-statistic>
            <div class="stat-trend">
              <span class="trend-text">较昨日</span>
              <span class="trend-value positive">+1.2MB</span>
            </div>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card :bordered="false" class="stat-card">
            <a-statistic
              title="活跃学科"
              :value="statsData.activeSubjects"
              :loading="statsLoading"
            >
              <template #prefix>
                <RiseOutlined class="stat-icon" style="color: #f5222d" />
              </template>
            </a-statistic>
            <div class="stat-trend">
              <span class="trend-text">启用状态</span>
              <span class="trend-value positive">{{ Math.round((statsData.activeSubjects / statsData.totalSubjects) * 100) }}%</span>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 快捷操作 -->
    <div class="quick-actions-section">
      <a-card title="快捷操作" :bordered="false" class="quick-actions-card">
        <template #extra>
          <a-button type="link" @click="handleRefreshStats">
            <ReloadOutlined />
            刷新数据
          </a-button>
        </template>
        <a-row :gutter="16">
          <a-col :xs="24" :sm="12" :lg="8">
            <div class="action-item" @click="navigateToSubjects">
              <div class="action-icon">
                <BookOutlined />
              </div>
              <div class="action-content">
                <h3>学科管理</h3>
                <p>管理系统中的学科分类，包括创建、编辑、删除等操作</p>
              </div>
              <div class="action-arrow">
                <RightOutlined />
              </div>
            </div>
          </a-col>
          <a-col :xs="24" :sm="12" :lg="8">
            <div class="action-item disabled">
              <div class="action-icon">
                <FileOutlined />
              </div>
              <div class="action-content">
                <h3>文件管理</h3>
                <p>管理学科下的文件和文件夹结构</p>
                <a-tag color="orange" size="small">即将上线</a-tag>
              </div>
              <div class="action-arrow">
                <RightOutlined />
              </div>
            </div>
          </a-col>
          <a-col :xs="24" :sm="12" :lg="8">
            <div class="action-item disabled">
              <div class="action-icon">
                <SettingOutlined />
              </div>
              <div class="action-content">
                <h3>系统设置</h3>
                <p>配置系统参数和管理员权限</p>
                <a-tag color="orange" size="small">即将上线</a-tag>
              </div>
              <div class="action-arrow">
                <RightOutlined />
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activity-section">
      <a-row :gutter="16">
        <a-col :xs="24" :lg="12">
          <a-card title="最近学科" :bordered="false" class="recent-card">
            <template #extra>
              <a-button type="link" @click="navigateToSubjects">
                查看全部
                <RightOutlined />
              </a-button>
            </template>
            <a-list
              :data-source="recentSubjects"
              :loading="statsLoading"
              size="small"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>
                      <span>{{ item.name }}</span>
                      <a-tag
                        :color="item.status === 1 ? 'success' : 'error'"
                        size="small"
                        style="margin-left: 8px"
                      >
                        {{ item.status === 1 ? '启用' : '禁用' }}
                      </a-tag>
                    </template>
                    <template #description>
                      <div class="subject-meta">
                        <span>{{ item.fileCount || 0 }} 个文件</span>
                        <a-divider type="vertical" />
                        <span>{{ formatFileSize(item.totalSize || 0) }}</span>
                        <a-divider type="vertical" />
                        <span>{{ formatRelativeTime(item.updated_at) }}</span>
                      </div>
                    </template>
                    <template #avatar>
                      <a-avatar style="background-color: #1890ff">
                        {{ item.name.charAt(0) }}
                      </a-avatar>
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>
        <a-col :xs="24" :lg="12">
          <a-card title="系统信息" :bordered="false" class="system-info-card">
            <a-descriptions :column="1" size="small">
              <a-descriptions-item label="系统版本">
                <a-tag color="blue">v1.0.0</a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="运行环境">
                <a-tag color="green">生产环境</a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="数据库">
                <span>SQLite</span>
              </a-descriptions-item>
              <a-descriptions-item label="最后备份">
                <span>{{ formatDateTime(new Date()) }}</span>
              </a-descriptions-item>
              <a-descriptions-item label="系统状态">
                <a-badge status="processing" text="运行正常" />
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  ClockCircleOutlined,
  UserOutlined,
  BookOutlined,
  FileOutlined,
  DatabaseOutlined,
  RiseOutlined,
  ReloadOutlined,
  RightOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'
import { useSubjectManagement } from '@/composables/useAdminApi'
import { formatDateTime, formatFileSize, formatRelativeTime } from '@/utils/format'
import type { Subject } from '@/types/api'

const router = useRouter()

// 组合函数
const { subjects, fetchSubjects } = useSubjectManagement()

// 响应式状态
const currentTime = ref('')
const statsLoading = ref(false)
const timeInterval = ref<NodeJS.Timeout | null>(null)

// 统计数据
const statsData = reactive({
  totalSubjects: 0,
  totalFiles: 0,
  totalSize: 0,
  activeSubjects: 0
})

// 最近学科数据
const recentSubjects = ref<Subject[]>([])

// 更新当前时间
const updateCurrentTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    weekday: 'long'
  })
}

// 获取统计数据
const fetchStats = async () => {
  try {
    statsLoading.value = true
    await fetchSubjects()

    // 计算统计数据
    statsData.totalSubjects = subjects.value.length
    statsData.activeSubjects = subjects.value.filter(s => s.status === 1).length
    statsData.totalFiles = subjects.value.reduce((sum, s) => sum + (s.fileCount || 0), 0)
    statsData.totalSize = subjects.value.reduce((sum, s) => sum + (s.totalSize || 0), 0)

    // 获取最近更新的学科（最多5个）
    recentSubjects.value = [...subjects.value]
      .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
      .slice(0, 5)

  } catch (error) {
    console.error('Failed to fetch stats:', error)
    message.error('获取统计数据失败')
  } finally {
    statsLoading.value = false
  }
}

// 刷新统计数据
const handleRefreshStats = () => {
  fetchStats()
  message.success('数据已刷新')
}

// 导航到学科管理
const navigateToSubjects = () => {
  router.push({ name: 'AdminSubjects' })
}

// 生命周期
onMounted(() => {
  updateCurrentTime()
  fetchStats()

  // 每秒更新时间
  timeInterval.value = setInterval(updateCurrentTime, 1000)
})

onUnmounted(() => {
  if (timeInterval.value) {
    clearInterval(timeInterval.value)
  }
})
</script>

<style scoped>
/* 页面整体布局 */
.admin-dashboard {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 欢迎区域 */
.welcome-section {
  flex-shrink: 0;
}

.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  overflow: hidden;
}

.welcome-card :deep(.ant-card-body) {
  padding: 32px;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-left {
  flex: 1;
  color: white;
}

.welcome-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: white;
}

.welcome-subtitle {
  font-size: 16px;
  margin: 0 0 16px 0;
  opacity: 0.9;
  color: white;
}

.welcome-time {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  opacity: 0.8;
  color: white;
}

.welcome-right {
  flex-shrink: 0;
  margin-left: 32px;
}

.welcome-avatar {
  text-align: center;
}

/* 统计卡片区域 */
.stats-section {
  flex-shrink: 0;
}

.stat-card {
  border-radius: 8px;
  transition: all 0.3s;
  cursor: pointer;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.stat-card :deep(.ant-card-body) {
  padding: 20px;
}

.stat-icon {
  font-size: 24px;
  margin-right: 8px;
}

.stat-trend {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.trend-text {
  color: rgba(0, 0, 0, 0.45);
}

.trend-value {
  font-weight: 500;
}

.trend-value.positive {
  color: #52c41a;
}

.trend-value.negative {
  color: #f5222d;
}

/* 快捷操作区域 */
.quick-actions-section {
  flex-shrink: 0;
}

.quick-actions-card {
  border-radius: 8px;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 16px;
}

.action-item:hover:not(.disabled) {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.action-item.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.action-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f2f5;
  border-radius: 8px;
  font-size: 20px;
  color: #1890ff;
  margin-right: 16px;
  flex-shrink: 0;
}

.action-content {
  flex: 1;
}

.action-content h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.88);
}

.action-content p {
  margin: 0;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  line-height: 1.4;
}

.action-arrow {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  margin-left: 16px;
  flex-shrink: 0;
}

/* 最近活动区域 */
.recent-activity-section {
  flex: 1;
  min-height: 0;
}

.recent-card,
.system-info-card {
  border-radius: 8px;
  height: 100%;
}

.recent-card :deep(.ant-card-body),
.system-info-card :deep(.ant-card-body) {
  height: calc(100% - 57px);
  overflow-y: auto;
}

.subject-meta {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .welcome-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }

  .welcome-right {
    margin-left: 0;
  }
}

@media (max-width: 768px) {
  .admin-dashboard {
    gap: 12px;
  }

  .welcome-card :deep(.ant-card-body) {
    padding: 20px;
  }

  .welcome-title {
    font-size: 24px;
  }

  .welcome-subtitle {
    font-size: 14px;
  }

  .stat-card :deep(.ant-card-body) {
    padding: 16px;
  }

  .action-item {
    padding: 12px;
    margin-bottom: 12px;
  }

  .action-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
    margin-right: 12px;
  }

  .action-content h3 {
    font-size: 14px;
  }

  .action-content p {
    font-size: 12px;
  }
}

/* 卡片动画 */
.stat-card,
.quick-actions-card,
.recent-card,
.system-info-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 列表项动画 */
.recent-card :deep(.ant-list-item) {
  transition: all 0.3s;
}

.recent-card :deep(.ant-list-item:hover) {
  background-color: #fafafa;
}

/* 描述列表样式 */
.system-info-card :deep(.ant-descriptions-item-label) {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.system-info-card :deep(.ant-descriptions-item-content) {
  color: rgba(0, 0, 0, 0.65);
}

/* 徽章样式 */
:deep(.ant-badge-status-text) {
  color: rgba(0, 0, 0, 0.65);
}

/* 统计数字样式 */
:deep(.ant-statistic-content) {
  font-size: 24px;
  font-weight: 600;
}

:deep(.ant-statistic-title) {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 4px;
}
</style>
