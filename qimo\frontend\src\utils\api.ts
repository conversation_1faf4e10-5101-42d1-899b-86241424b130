import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import type {
  ApiResponse,
  Subject,
  FileNode,
  FileWithContent,
  SearchResult,
  GetSubjectsParams,
  SearchFilesParams
} from '@/types/api'

// 重试配置
const RETRY_CONFIG = {
  maxRetries: 3,
  retryDelay: 1000,
  retryCondition: (error: any) => {
    // 网络错误或5xx服务器错误时重试
    return !error.response || (error.response.status >= 500 && error.response.status < 600)
  }
}

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加请求时间戳
    config.metadata = { startTime: new Date() }

    // 可以在这里添加认证token等
    // const token = localStorage.getItem('token')
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`
    // }

    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, config.params || config.data)
    return config
  },
  (error) => {
    console.error('❌ Request Error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const duration = new Date().getTime() - response.config.metadata?.startTime?.getTime()
    console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`, response.data)

    // 检查业务状态码
    if (!response.data.success) {
      const error = new Error(response.data.error?.message || '请求失败')
      error.name = 'BusinessError'
        ; (error as any).code = response.data.error?.code
        ; (error as any).response = response
      throw error
    }

    return response
  },
  async (error) => {
    console.error('❌ Response Error:', error)

    const config = error.config

    // 重试逻辑
    if (config && RETRY_CONFIG.retryCondition(error)) {
      config.__retryCount = config.__retryCount || 0

      if (config.__retryCount < RETRY_CONFIG.maxRetries) {
        config.__retryCount++
        console.log(`🔄 Retrying request (${config.__retryCount}/${RETRY_CONFIG.maxRetries}): ${config.method?.toUpperCase()} ${config.url}`)

        // 延迟重试
        await new Promise(resolve => setTimeout(resolve, RETRY_CONFIG.retryDelay * config.__retryCount))

        return api(config)
      }
    }

    // 处理网络错误
    if (!error.response) {
      error.message = '网络连接失败，请检查网络设置'
    } else {
      // 处理HTTP错误状态码
      const status = error.response.status
      switch (status) {
        case 400:
          error.message = '请求参数错误'
          break
        case 401:
          error.message = '未授权访问'
          break
        case 403:
          error.message = '禁止访问'
          break
        case 404:
          error.message = '请求的资源不存在'
          break
        case 500:
          error.message = '服务器内部错误'
          break
        default:
          error.message = `请求失败 (${status})`
      }
    }

    return Promise.reject(error)
  }
)

// 学科相关API
export const subjectApi = {
  // 获取所有学科
  getSubjects: (includeStats = false): Promise<AxiosResponse<ApiResponse<Subject[]>>> => {
    const params: GetSubjectsParams = {}
    if (includeStats) {
      params.include_stats = true
    }
    return api.get('/subjects', { params })
  },

  // 获取学科详情
  getSubjectById: (id: number): Promise<AxiosResponse<ApiResponse<Subject>>> => {
    return api.get(`/subjects/${id}`)
  },

  // 获取学科文件结构
  getSubjectFiles: (id: number): Promise<AxiosResponse<ApiResponse<FileNode[]>>> => {
    return api.get(`/subjects/${id}/files`)
  },
}

// 文件相关API
export const fileApi = {
  // 获取文件内容
  getFileContent: (id: number): Promise<AxiosResponse<ApiResponse<FileWithContent>>> => {
    return api.get(`/files/${id}`)
  },

  // 获取文件元数据
  getFileMetadata: (id: number): Promise<AxiosResponse<ApiResponse<FileNode>>> => {
    return api.get(`/files/${id}/metadata`)
  },

  // 搜索文件
  searchFiles: (query: string, subjectId?: number, type?: 'file' | 'folder'): Promise<AxiosResponse<ApiResponse<SearchResult[]>>> => {
    const params: SearchFilesParams = { q: query }
    if (subjectId) {
      params.subject_id = subjectId
    }
    if (type) {
      params.type = type
    }
    return api.get('/files/search', { params })
  },

  // 下载文件
  downloadFile: (id: number): Promise<AxiosResponse<Blob>> => {
    return api.get(`/files/${id}/download`, {
      responseType: 'blob'
    })
  },
}

// 通用API工具函数
export const apiUtils = {
  // 处理API错误
  handleError: (error: any): string => {
    if (error.name === 'BusinessError') {
      return error.message
    }

    if (error.response?.data?.error?.message) {
      return error.response.data.error.message
    }

    return error.message || '发生未知错误'
  },

  // 格式化文件大小
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 B'

    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  // 格式化日期
  formatDate: (dateString: string): string => {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  },
}

// 管理员API工具函数
export const adminApi = {
  // 创建管理员专用的axios实例
  createAdminRequest: (config: AxiosRequestConfig = {}) => {
    const adminConfig = {
      ...config,
      headers: {
        ...config.headers,
        'x-admin-path': '/admin-panel-abcdef'
      }
    }
    return api(adminConfig)
  },

  // 获取学科列表（管理员）
  getSubjects: (): Promise<AxiosResponse<ApiResponse<Subject[]>>> => {
    return adminApi.createAdminRequest({
      method: 'GET',
      url: '/admin/subjects'
    })
  },

  // 获取学科详情（管理员）
  getSubjectById: (id: number): Promise<AxiosResponse<ApiResponse<Subject>>> => {
    return adminApi.createAdminRequest({
      method: 'GET',
      url: `/admin/subjects/${id}`
    })
  },

  // 创建学科（管理员）
  createSubject: (data: { name: string; description?: string; status?: number; sort_order?: number }): Promise<AxiosResponse<ApiResponse<Subject>>> => {
    return adminApi.createAdminRequest({
      method: 'POST',
      url: '/admin/subjects',
      data
    })
  },

  // 更新学科（管理员）
  updateSubject: (id: number, data: { name?: string; description?: string; status?: number; sort_order?: number }): Promise<AxiosResponse<ApiResponse<Subject>>> => {
    return adminApi.createAdminRequest({
      method: 'PUT',
      url: `/admin/subjects/${id}`,
      data
    })
  },

  // 删除学科（管理员）
  deleteSubject: (id: number, cascade = false): Promise<AxiosResponse<ApiResponse<{ deleted: boolean; id: number }>>> => {
    return adminApi.createAdminRequest({
      method: 'DELETE',
      url: `/admin/subjects/${id}${cascade ? '?cascade=true' : ''}`
    })
  },

  // 获取学科统计信息（管理员）
  getSubjectStats: (id: number): Promise<AxiosResponse<ApiResponse<{ fileCount: number; folderCount: number; totalSize: number }>>> => {
    return adminApi.createAdminRequest({
      method: 'GET',
      url: `/admin/subjects/${id}/stats`
    })
  }
}

export default api
