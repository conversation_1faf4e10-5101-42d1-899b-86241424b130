import { describe, test, expect, beforeEach, afterEach } from '@jest/globals'
import { SubjectModel } from '../../src/models/Subject.js'
import { SubjectService } from '../../src/services/SubjectService.js'

describe('Subject Model and Service Unit Tests', () => {
  beforeEach(() => {
    // 清理测试数据
    const subjects = SubjectModel.findAll()
    for (const subject of subjects) {
      SubjectModel.delete(subject.id)
    }
  })

  afterEach(() => {
    // 清理测试数据
    const subjects = SubjectModel.findAll()
    for (const subject of subjects) {
      SubjectModel.delete(subject.id)
    }
  })

  describe('SubjectModel', () => {
    test('应能创建学科', () => {
      const result = SubjectModel.create({ name: '计算机科学' })

      expect(result.id).toBeDefined()
      expect(result.name).toBe('计算机科学')
      expect(result.fileCount).toBe(0)
      expect(result.createdAt).toBeInstanceOf(Date)
      expect(result.updatedAt).toBeInstanceOf(Date)
    })

    test('应拒绝重复的学科名称', () => {
      SubjectModel.create({ name: '数学' })

      expect(() => {
        SubjectModel.create({ name: '数学' })
      }).toThrow('学科名称已存在')
    })

    test('应能获取所有学科', () => {
      SubjectModel.create({ name: '物理' })
      SubjectModel.create({ name: '化学' })

      const subjects = SubjectModel.findAll()
      expect(subjects.length).toBe(2)
      expect(subjects[0].name).toBe('化学') // 后创建的在前
      expect(subjects[1].name).toBe('物理')
    })

    test('应能根据ID查找学科', () => {
      const created = SubjectModel.create({ name: '生物' })
      const found = SubjectModel.findById(created.id)

      expect(found).not.toBeNull()
      expect(found!.name).toBe('生物')
    })

    test('应能根据名称查找学科', () => {
      SubjectModel.create({ name: '地理' })
      const found = SubjectModel.findByName('地理')

      expect(found).not.toBeNull()
      expect(found!.name).toBe('地理')
    })

    test('应能更新学科', () => {
      const created = SubjectModel.create({ name: '历史' })
      const updated = SubjectModel.update(created.id, { name: '中国历史' })

      expect(updated).not.toBeNull()
      expect(updated!.name).toBe('中国历史')
    })

    test('应能删除学科', () => {
      const created = SubjectModel.create({ name: '音乐' })
      const deleted = SubjectModel.delete(created.id)

      expect(deleted).toBe(true)

      const found = SubjectModel.findById(created.id)
      expect(found).toBeNull()
    })

    test('应能统计学科数量', () => {
      expect(SubjectModel.count()).toBe(0)

      SubjectModel.create({ name: '体育' })
      SubjectModel.create({ name: '美术' })

      expect(SubjectModel.count()).toBe(2)
    })
  })

  describe('SubjectService', () => {
    test('应能创建学科', async () => {
      const result = await SubjectService.createSubject({ name: '英语' })

      expect(result.name).toBe('英语')
      expect(result.id).toBeDefined()
    })

    test('应拒绝空的学科名称', async () => {
      await expect(SubjectService.createSubject({ name: '' }))
        .rejects.toThrow('学科名称不能为空')
    })

    test('应拒绝过长的学科名称', async () => {
      const longName = 'a'.repeat(51)
      await expect(SubjectService.createSubject({ name: longName }))
        .rejects.toThrow('学科名称长度不能超过50个字符')
    })

    test('应拒绝包含特殊字符的学科名称', async () => {
      await expect(SubjectService.createSubject({ name: '语文<>' }))
        .rejects.toThrow('学科名称不能包含特殊字符')
    })

    test('应拒绝重复的学科名称', async () => {
      await SubjectService.createSubject({ name: '政治' })

      await expect(SubjectService.createSubject({ name: '政治' }))
        .rejects.toThrow('学科名称已存在')
    })

    test('应能获取学科列表', async () => {
      await SubjectService.createSubject({ name: '法语' })
      await SubjectService.createSubject({ name: '德语' })

      const result = await SubjectService.getSubjects()

      expect(result.subjects.length).toBe(2)
      expect(result.total).toBe(2)
    })

    test('应能获取单个学科', async () => {
      const created = await SubjectService.createSubject({ name: '日语' })
      const found = await SubjectService.getSubject(created.id)

      expect(found.name).toBe('日语')
      expect(found.id).toBe(created.id)
    })

    test('获取不存在的学科应抛出错误', async () => {
      const fakeId = '550e8400-e29b-41d4-a716-446655440000'

      await expect(SubjectService.getSubject(fakeId))
        .rejects.toThrow('学科不存在')
    })

    test('应能更新学科', async () => {
      const created = await SubjectService.createSubject({ name: '韩语' })
      const updated = await SubjectService.updateSubject(created.id, { name: '朝鲜语' })

      expect(updated.name).toBe('朝鲜语')
    })

    test('应能删除学科', async () => {
      const created = await SubjectService.createSubject({ name: '俄语' })

      await expect(SubjectService.deleteSubject(created.id))
        .resolves.not.toThrow()

      await expect(SubjectService.getSubject(created.id))
        .rejects.toThrow('学科不存在')
    })
  })
})