import { test, expect } from '@playwright/test'
import { request } from '@playwright/test'

/**
 * 管理员API自动化测试
 * 测试所有管理API接口的正常和异常情况
 */

const API_BASE_URL = 'http://localhost:3000/api'
const ADMIN_HEADERS = {
  'Content-Type': 'application/json',
  'x-admin-path': '/admin-panel-abcdef'
}

test.describe('管理员API测试', () => {
  let apiContext: any

  test.beforeAll(async ({ playwright }) => {
    apiContext = await playwright.request.newContext({
      baseURL: API_BASE_URL
    })
  })

  test.afterAll(async () => {
    await apiContext.dispose()
  })

  test.describe('权限验证测试', () => {
    test('无权限头访问应返回403', async () => {
      const response = await apiContext.get('/admin/subjects')
      expect(response.status()).toBe(403)
      
      const body = await response.json()
      expect(body.success).toBe(false)
      expect(body.error.code).toBe('FORBIDDEN')
    })

    test('错误权限头访问应返回403', async () => {
      const response = await apiContext.get('/admin/subjects', {
        headers: {
          'x-admin-path': '/wrong-path'
        }
      })
      expect(response.status()).toBe(403)
    })

    test('正确权限头访问应成功', async () => {
      const response = await apiContext.get('/admin/subjects', {
        headers: ADMIN_HEADERS
      })
      expect(response.status()).toBe(200)
      
      const body = await response.json()
      expect(body.success).toBe(true)
      expect(Array.isArray(body.data)).toBe(true)
    })
  })

  test.describe('学科管理API测试', () => {
    let createdSubjectId: number

    test('GET /admin/subjects - 获取学科列表', async () => {
      const response = await apiContext.get('/admin/subjects', {
        headers: ADMIN_HEADERS
      })

      expect(response.status()).toBe(200)
      
      const body = await response.json()
      expect(body.success).toBe(true)
      expect(Array.isArray(body.data)).toBe(true)
      expect(body.timestamp).toBeDefined()
      expect(body.requestId).toBeDefined()

      // 验证数据结构
      if (body.data.length > 0) {
        const subject = body.data[0]
        expect(subject).toHaveProperty('id')
        expect(subject).toHaveProperty('name')
        expect(subject).toHaveProperty('description')
        expect(subject).toHaveProperty('status')
        expect(subject).toHaveProperty('sort_order')
        expect(subject).toHaveProperty('file_count')
        expect(subject).toHaveProperty('folder_count')
        expect(subject).toHaveProperty('total_size')
        expect(subject).toHaveProperty('created_at')
        expect(subject).toHaveProperty('updated_at')
      }
    })

    test('POST /admin/subjects - 创建学科', async () => {
      const testSubject = {
        name: `API测试学科_${Date.now()}`,
        description: '这是一个API自动化测试创建的学科',
        status: 1,
        sort_order: 999
      }

      const response = await apiContext.post('/admin/subjects', {
        headers: ADMIN_HEADERS,
        data: testSubject
      })

      expect(response.status()).toBe(201)
      
      const body = await response.json()
      expect(body.success).toBe(true)
      expect(body.data).toHaveProperty('id')
      expect(body.data.name).toBe(testSubject.name)
      expect(body.data.description).toBe(testSubject.description)
      expect(body.data.status).toBe(testSubject.status)
      expect(body.data.sort_order).toBe(testSubject.sort_order)

      createdSubjectId = body.data.id
    })

    test('POST /admin/subjects - 创建学科参数验证', async () => {
      // 测试缺少必填字段
      const response1 = await apiContext.post('/admin/subjects', {
        headers: ADMIN_HEADERS,
        data: {
          description: '缺少名称的学科'
        }
      })
      expect(response1.status()).toBe(400)

      // 测试空名称
      const response2 = await apiContext.post('/admin/subjects', {
        headers: ADMIN_HEADERS,
        data: {
          name: '',
          description: '空名称学科'
        }
      })
      expect(response2.status()).toBe(400)

      // 测试重复名称
      const response3 = await apiContext.post('/admin/subjects', {
        headers: ADMIN_HEADERS,
        data: {
          name: '数学', // 假设已存在
          description: '重复名称测试'
        }
      })
      expect(response3.status()).toBe(409)
    })

    test('PUT /admin/subjects/:id - 更新学科', async () => {
      if (!createdSubjectId) {
        test.skip('需要先创建学科')
      }

      const updateData = {
        name: `更新后的API测试学科_${Date.now()}`,
        description: '这是更新后的描述',
        status: 0,
        sort_order: 888
      }

      const response = await apiContext.put(`/admin/subjects/${createdSubjectId}`, {
        headers: ADMIN_HEADERS,
        data: updateData
      })

      expect(response.status()).toBe(200)
      
      const body = await response.json()
      expect(body.success).toBe(true)
      expect(body.data.name).toBe(updateData.name)
      expect(body.data.description).toBe(updateData.description)
      expect(body.data.status).toBe(updateData.status)
      expect(body.data.sort_order).toBe(updateData.sort_order)
    })

    test('PUT /admin/subjects/:id - 更新不存在的学科', async () => {
      const response = await apiContext.put('/admin/subjects/99999', {
        headers: ADMIN_HEADERS,
        data: {
          name: '不存在的学科'
        }
      })

      expect(response.status()).toBe(404)
      
      const body = await response.json()
      expect(body.success).toBe(false)
      expect(body.error.code).toBe('NOT_FOUND')
    })

    test('DELETE /admin/subjects/:id - 删除学科', async () => {
      if (!createdSubjectId) {
        test.skip('需要先创建学科')
      }

      const response = await apiContext.delete(`/admin/subjects/${createdSubjectId}`, {
        headers: ADMIN_HEADERS
      })

      expect(response.status()).toBe(200)
      
      const body = await response.json()
      expect(body.success).toBe(true)

      // 验证学科已被删除
      const getResponse = await apiContext.get(`/admin/subjects/${createdSubjectId}`, {
        headers: ADMIN_HEADERS
      })
      expect(getResponse.status()).toBe(404)
    })

    test('DELETE /admin/subjects/:id - 删除不存在的学科', async () => {
      const response = await apiContext.delete('/admin/subjects/99999', {
        headers: ADMIN_HEADERS
      })

      expect(response.status()).toBe(404)
      
      const body = await response.json()
      expect(body.success).toBe(false)
      expect(body.error.code).toBe('NOT_FOUND')
    })
  })

  test.describe('API性能测试', () => {
    test('获取学科列表响应时间应小于1秒', async () => {
      const startTime = Date.now()
      
      const response = await apiContext.get('/admin/subjects', {
        headers: ADMIN_HEADERS
      })
      
      const endTime = Date.now()
      const responseTime = endTime - startTime

      expect(response.status()).toBe(200)
      expect(responseTime).toBeLessThan(1000) // 小于1秒
    })

    test('创建学科响应时间应小于2秒', async () => {
      const testSubject = {
        name: `性能测试学科_${Date.now()}`,
        description: '性能测试用学科'
      }

      const startTime = Date.now()
      
      const response = await apiContext.post('/admin/subjects', {
        headers: ADMIN_HEADERS,
        data: testSubject
      })
      
      const endTime = Date.now()
      const responseTime = endTime - startTime

      expect(response.status()).toBe(201)
      expect(responseTime).toBeLessThan(2000) // 小于2秒

      // 清理测试数据
      if (response.status() === 201) {
        const body = await response.json()
        await apiContext.delete(`/admin/subjects/${body.data.id}`, {
          headers: ADMIN_HEADERS
        })
      }
    })
  })

  test.describe('错误处理测试', () => {
    test('服务器错误应返回正确格式', async () => {
      // 模拟服务器错误情况
      const response = await apiContext.post('/admin/subjects', {
        headers: ADMIN_HEADERS,
        data: {
          name: null // 故意传入null值触发错误
        }
      })

      expect(response.status()).toBeGreaterThanOrEqual(400)
      
      const body = await response.json()
      expect(body.success).toBe(false)
      expect(body.error).toBeDefined()
      expect(body.error.code).toBeDefined()
      expect(body.error.message).toBeDefined()
      expect(body.timestamp).toBeDefined()
      expect(body.requestId).toBeDefined()
    })

    test('无效JSON格式应返回400错误', async () => {
      const response = await apiContext.post('/admin/subjects', {
        headers: {
          ...ADMIN_HEADERS,
          'Content-Type': 'application/json'
        },
        data: 'invalid json string'
      })

      expect(response.status()).toBe(400)
    })
  })
})
