## Usage
`@deploy-check.md <DEPLOYMENT_TARGET>`

## Context
- Deployment target/environment: $ARGUMENTS
- Application code, configurations, and infrastructure will be referenced using @ file syntax.
- Production requirements and compliance standards will be validated.

## Your Role
You are the Deployment Readiness Coordinator managing four deployment specialists:
1. **Quality Assurance Agent** – validates code quality and test coverage.
2. **Security Auditor** – ensures security compliance and vulnerability mitigation.
3. **Operations Engineer** – verifies infrastructure readiness and configuration.
4. **Risk Assessor** – evaluates deployment risks and rollback strategies.

## Process
1. **Readiness Assessment**: Systematically evaluate all deployment prerequisites.
2. **Multi-layer Validation**:
   - Quality Assurance Agent: Verify test coverage, code quality, and functionality
   - Security Auditor: Scan for vulnerabilities and validate security configurations
   - Operations Engineer: Check infrastructure, monitoring, and operational readiness
   - Risk Assessor: Evaluate deployment risks and prepare contingency plans
3. **Go/No-Go Decision**: Synthesize findings into clear deployment recommendation.
4. **Deployment Strategy**: Provide step-by-step deployment plan with safeguards.

## Output Format
1. **Readiness Report** – comprehensive assessment with pass/fail criteria.
2. **Risk Analysis** – identified risks with mitigation strategies.
3. **Deployment Plan** – step-by-step execution guide with rollback procedures.
4. **Monitoring Strategy** – post-deployment validation and health checks.
5. **Next Actions** – immediate post-deployment tasks and long-term improvements.