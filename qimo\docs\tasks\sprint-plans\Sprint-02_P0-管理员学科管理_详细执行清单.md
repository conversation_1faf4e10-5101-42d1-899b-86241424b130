# Sprint-02: P0-管理员学科管理 - 详细执行清单

## 文档信息
- **Sprint编号**: Sprint-02
- **切片名称**: P0-管理员学科管理
- **任务ID**: `a77bedcc-db09-4b2f-9939-6c551f6b5528`
- **创建时间**: 2025-01-27
- **规划人员**: <PERSON> (架构师) + <PERSON> (团队领袖)
- **执行人员**: Alex (工程师)
- **基于文档**: PRD_Final-Review-Platform_v1.0.md, 任务规划_期末复习平台_垂直切片.md, Overall_Architecture_期末复习平台.md
- **版权归属**: 随影

## 1. Sprint概述

### 1.1 用户价值
管理员能够通过专用管理后台创建和删除学科分类，为平台内容管理提供基础功能。

### 1.2 技术范围
- **访问控制层**: 特定URL路径访问控制（/admin-panel-abcdef）
- **后端API层**: 实现POST /api/subjects, DELETE /api/subjects/{id}, PUT /api/subjects/{id}三个管理接口
- **前端界面层**: 基于Vben Admin框架的管理后台，学科管理页面
- **集成测试层**: 管理功能的端到端验证和与访客功能的集成测试

### 1.3 验收标准
管理员通过特定URL（/admin-panel-abcdef）访问管理后台，能够创建新学科并在前台看到更新，能够安全删除学科（包括级联删除相关文件），能够编辑学科名称。整个管理流程响应时间符合性能要求（操作响应≤1秒）。

### 1.4 总体工期
**预估工期**: 4-5个工作日
**里程碑**: 完成管理员学科管理功能，为后续文件上传功能奠定管理基础

### 1.5 依赖关系
- **前置依赖**: 任务1（P0-访客基础浏览功能）必须完成
- **数据库依赖**: 基于现有的subjects表和file_nodes表结构
- **技术依赖**: 现有的Vue3前端项目和Node.js后端服务

## 2. 子任务详细分解

### 任务2.1: 管理后台访问控制实现

#### 基本信息
- **任务编号**: 2.1
- **任务名称**: 管理后台访问控制实现
- **负责人**: Alex (工程师)
- **预估工期**: 1个工作日
- **优先级**: P0 (最高优先级)
- **依赖关系**: 依赖任务1（访客基础浏览功能）

#### 技术实现要求

**2.1.1 环境配置和路径管理**
```javascript
// 环境变量配置 (.env)
ADMIN_PANEL_PATH=admin-panel-abcdef
ADMIN_PANEL_ENABLED=true
NODE_ENV=development

// 配置管理模块 (src/config/admin.js)
const adminConfig = {
  panelPath: process.env.ADMIN_PANEL_PATH || 'admin-panel-abcdef',
  enabled: process.env.ADMIN_PANEL_ENABLED === 'true',
  maxLoginAttempts: 5,
  sessionTimeout: 24 * 60 * 60 * 1000 // 24小时
}

module.exports = adminConfig
```

**2.1.2 后端路由保护中间件**
```javascript
// 中间件 (src/middleware/adminAuth.js)
const adminConfig = require('../config/admin')

const adminAuthMiddleware = async (ctx, next) => {
  const requestPath = ctx.path
  const adminPath = `/${adminConfig.panelPath}`
  
  // 检查是否为管理后台路径
  if (requestPath.startsWith('/api/admin') || requestPath.startsWith(adminPath)) {
    if (!adminConfig.enabled) {
      ctx.status = 404
      ctx.body = { success: false, error: { message: 'Not Found' } }
      return
    }
    
    // 简单的路径验证（后续可扩展为更复杂的认证）
    const adminPathHeader = ctx.headers['x-admin-path']
    if (requestPath.startsWith('/api/admin') && adminPathHeader !== adminConfig.panelPath) {
      ctx.status = 403
      ctx.body = { 
        success: false, 
        error: { 
          code: 'ADMIN_ACCESS_DENIED',
          message: '管理后台访问被拒绝' 
        } 
      }
      return
    }
  }
  
  await next()
}

module.exports = adminAuthMiddleware
```

**2.1.3 前端路由配置**
```typescript
// 前端路由配置 (src/router/admin.ts)
import { createRouter, createWebHistory } from 'vue-router'
import AdminLayout from '@/layouts/AdminLayout.vue'
import SubjectManagement from '@/views/admin/SubjectManagement.vue'

const adminRoutes = [
  {
    path: `/${import.meta.env.VITE_ADMIN_PANEL_PATH || 'admin-panel-abcdef'}`,
    component: AdminLayout,
    meta: { 
      title: '管理后台',
      requiresAdmin: true 
    },
    children: [
      {
        path: '',
        redirect: 'subjects'
      },
      {
        path: 'subjects',
        name: 'SubjectManagement',
        component: SubjectManagement,
        meta: { title: '学科管理' }
      }
    ]
  }
]

// 路由守卫
const router = createRouter({
  history: createWebHistory(),
  routes: [...publicRoutes, ...adminRoutes]
})

router.beforeEach((to, from, next) => {
  if (to.meta.requiresAdmin) {
    // 验证管理后台访问权限
    const adminPath = import.meta.env.VITE_ADMIN_PANEL_PATH || 'admin-panel-abcdef'
    if (to.path.includes(adminPath)) {
      // 设置管理后台标识
      localStorage.setItem('admin-access', adminPath)
      next()
    } else {
      next('/')
    }
  } else {
    next()
  }
})

export default router
```

#### 交付物清单
1. **环境配置**: `.env`文件和配置管理模块
2. **后端中间件**: `src/middleware/adminAuth.js`
3. **前端路由**: `src/router/admin.ts`
4. **API拦截器**: `src/utils/adminApi.ts`
5. **管理布局**: `src/layouts/AdminLayout.vue`
6. **访问控制文档**: `docs/admin/access-control.md`

#### 验收标准
- [ ] 通过正确的URL路径能够访问管理后台
- [ ] 错误的路径访问返回404或重定向到首页
- [ ] API请求包含正确的管理后台标识头
- [ ] 权限验证失败时有适当的错误处理
- [ ] 管理后台可以通过环境变量启用/禁用
- [ ] 前端路由守卫正常工作，保护管理页面

#### 技术规范
- 使用环境变量管理敏感配置
- 前后端统一的权限验证机制
- 清晰的错误处理和用户反馈
- 支持开发和生产环境的不同配置
- 遵循最小权限原则

---

### 任务2.2: 学科管理后端API开发

#### 基本信息
- **任务编号**: 2.2
- **任务名称**: 学科管理后端API开发
- **负责人**: Alex (工程师)
- **预估工期**: 1.5个工作日
- **优先级**: P0 (最高优先级)
- **依赖关系**: 依赖任务2.1（管理后台访问控制实现）

#### 技术实现要求

**2.2.1 数据库扩展和优化**
```sql
-- 扩展subjects表结构 (如果需要)
ALTER TABLE subjects ADD COLUMN description TEXT DEFAULT '';
ALTER TABLE subjects ADD COLUMN file_count INTEGER DEFAULT 0;
ALTER TABLE subjects ADD COLUMN total_size INTEGER DEFAULT 0;

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_subjects_created_at ON subjects(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_subjects_name_unique ON subjects(name);

-- 创建触发器自动更新统计信息
CREATE TRIGGER IF NOT EXISTS update_subject_stats_on_insert
AFTER INSERT ON file_nodes
BEGIN
  UPDATE subjects
  SET file_count = (
    SELECT COUNT(*) FROM file_nodes
    WHERE subject_id = NEW.subject_id AND type = 'file'
  ),
  total_size = (
    SELECT COALESCE(SUM(size), 0) FROM file_nodes
    WHERE subject_id = NEW.subject_id AND type = 'file'
  )
  WHERE id = NEW.subject_id;
END;

CREATE TRIGGER IF NOT EXISTS update_subject_stats_on_delete
AFTER DELETE ON file_nodes
BEGIN
  UPDATE subjects
  SET file_count = (
    SELECT COUNT(*) FROM file_nodes
    WHERE subject_id = OLD.subject_id AND type = 'file'
  ),
  total_size = (
    SELECT COALESCE(SUM(size), 0) FROM file_nodes
    WHERE subject_id = OLD.subject_id AND type = 'file'
  )
  WHERE id = OLD.subject_id;
END;
```

**2.2.2 管理API路由定义**
```javascript
// 管理API路由 (src/routes/admin.js)
const Router = require('koa-router')
const SubjectController = require('../controllers/admin/SubjectController')
const { validateSubject } = require('../middleware/validation')

const router = new Router({ prefix: '/api/admin' })

// 学科管理路由
router.get('/subjects', SubjectController.list)           // 获取学科列表（含统计信息）
router.post('/subjects', validateSubject, SubjectController.create)  // 创建学科
router.put('/subjects/:id', validateSubject, SubjectController.update) // 更新学科
router.delete('/subjects/:id', SubjectController.delete)  // 删除学科
router.get('/subjects/:id/stats', SubjectController.getStats) // 获取学科统计信息

module.exports = router
```

**2.2.3 学科管理控制器**
```javascript
// 控制器 (src/controllers/admin/SubjectController.js)
const SubjectService = require('../../services/admin/SubjectService')
const { successResponse, errorResponse } = require('../../utils/response')

class SubjectController {
  // 获取学科列表（管理员视图，包含统计信息）
  static async list(ctx) {
    try {
      const subjects = await SubjectService.getSubjectsWithStats()
      ctx.body = successResponse(subjects, {
        total: subjects.length,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error('获取学科列表失败:', error)
      ctx.body = errorResponse('INTERNAL_ERROR', '获取学科列表失败')
      ctx.status = 500
    }
  }

  // 创建学科
  static async create(ctx) {
    try {
      const { name, description = '' } = ctx.request.body

      // 检查学科名称是否已存在
      const existingSubject = await SubjectService.getSubjectByName(name)
      if (existingSubject) {
        ctx.body = errorResponse('DUPLICATE_NAME', '学科名称已存在')
        ctx.status = 400
        return
      }

      const subject = await SubjectService.createSubject({ name, description })
      ctx.body = successResponse(subject, {
        message: '学科创建成功',
        timestamp: new Date().toISOString()
      })
      ctx.status = 201
    } catch (error) {
      console.error('创建学科失败:', error)
      ctx.body = errorResponse('INTERNAL_ERROR', '创建学科失败')
      ctx.status = 500
    }
  }

  // 更新学科
  static async update(ctx) {
    try {
      const { id } = ctx.params
      const { name, description } = ctx.request.body

      // 检查学科是否存在
      const existingSubject = await SubjectService.getSubjectById(id)
      if (!existingSubject) {
        ctx.body = errorResponse('NOT_FOUND', '学科不存在')
        ctx.status = 404
        return
      }

      // 检查新名称是否与其他学科冲突
      if (name !== existingSubject.name) {
        const duplicateSubject = await SubjectService.getSubjectByName(name)
        if (duplicateSubject && duplicateSubject.id !== parseInt(id)) {
          ctx.body = errorResponse('DUPLICATE_NAME', '学科名称已存在')
          ctx.status = 400
          return
        }
      }

      const updatedSubject = await SubjectService.updateSubject(id, { name, description })
      ctx.body = successResponse(updatedSubject, {
        message: '学科更新成功',
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error('更新学科失败:', error)
      ctx.body = errorResponse('INTERNAL_ERROR', '更新学科失败')
      ctx.status = 500
    }
  }

  // 删除学科
  static async delete(ctx) {
    try {
      const { id } = ctx.params

      // 检查学科是否存在
      const existingSubject = await SubjectService.getSubjectById(id)
      if (!existingSubject) {
        ctx.body = errorResponse('NOT_FOUND', '学科不存在')
        ctx.status = 404
        return
      }

      // 获取学科统计信息，用于删除确认
      const stats = await SubjectService.getSubjectStats(id)

      // 执行级联删除
      const result = await SubjectService.deleteSubject(id)

      ctx.body = successResponse(result, {
        message: `学科删除成功，共删除 ${stats.file_count} 个文件`,
        deletedFiles: stats.file_count,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error('删除学科失败:', error)
      ctx.body = errorResponse('INTERNAL_ERROR', '删除学科失败')
      ctx.status = 500
    }
  }

  // 获取学科统计信息
  static async getStats(ctx) {
    try {
      const { id } = ctx.params
      const stats = await SubjectService.getSubjectStats(id)

      if (!stats) {
        ctx.body = errorResponse('NOT_FOUND', '学科不存在')
        ctx.status = 404
        return
      }

      ctx.body = successResponse(stats)
    } catch (error) {
      console.error('获取学科统计失败:', error)
      ctx.body = errorResponse('INTERNAL_ERROR', '获取学科统计失败')
      ctx.status = 500
    }
  }
}

module.exports = SubjectController
```

**2.2.4 学科管理服务层**
```javascript
// 服务层 (src/services/admin/SubjectService.js)
const db = require('../../database/connection')
const fs = require('fs').promises
const path = require('path')

class SubjectService {
  // 获取带统计信息的学科列表
  static async getSubjectsWithStats() {
    const query = `
      SELECT
        s.*,
        COALESCE(s.file_count, 0) as file_count,
        COALESCE(s.total_size, 0) as total_size,
        CASE
          WHEN s.file_count > 0 THEN 'active'
          ELSE 'empty'
        END as status
      FROM subjects s
      ORDER BY s.created_at DESC
    `

    return db.prepare(query).all()
  }

  // 根据ID获取学科
  static async getSubjectById(id) {
    const query = 'SELECT * FROM subjects WHERE id = ?'
    return db.prepare(query).get(id)
  }

  // 根据名称获取学科
  static async getSubjectByName(name) {
    const query = 'SELECT * FROM subjects WHERE name = ?'
    return db.prepare(query).get(name)
  }

  // 创建学科
  static async createSubject({ name, description = '' }) {
    const query = `
      INSERT INTO subjects (name, description, created_at, updated_at)
      VALUES (?, ?, datetime('now'), datetime('now'))
    `

    const result = db.prepare(query).run(name, description)

    // 创建学科对应的存储目录
    const subjectDir = path.join(process.cwd(), 'storage', 'data', result.lastInsertRowid.toString())
    await fs.mkdir(subjectDir, { recursive: true })

    return this.getSubjectById(result.lastInsertRowid)
  }

  // 更新学科
  static async updateSubject(id, { name, description }) {
    const query = `
      UPDATE subjects
      SET name = ?, description = ?, updated_at = datetime('now')
      WHERE id = ?
    `

    db.prepare(query).run(name, description, id)
    return this.getSubjectById(id)
  }

  // 删除学科（级联删除）
  static async deleteSubject(id) {
    const transaction = db.transaction(() => {
      // 删除文件节点记录
      const deleteFilesQuery = 'DELETE FROM file_nodes WHERE subject_id = ?'
      const filesResult = db.prepare(deleteFilesQuery).run(id)

      // 删除学科记录
      const deleteSubjectQuery = 'DELETE FROM subjects WHERE id = ?'
      const subjectResult = db.prepare(deleteSubjectQuery).run(id)

      return {
        deletedFiles: filesResult.changes,
        deletedSubject: subjectResult.changes > 0
      }
    })

    const result = transaction()

    // 删除文件系统中的学科目录
    const subjectDir = path.join(process.cwd(), 'storage', 'data', id.toString())
    try {
      await fs.rm(subjectDir, { recursive: true, force: true })
    } catch (error) {
      console.warn(`删除学科目录失败: ${subjectDir}`, error)
    }

    return result
  }

  // 获取学科统计信息
  static async getSubjectStats(id) {
    const query = `
      SELECT
        s.*,
        COALESCE(s.file_count, 0) as file_count,
        COALESCE(s.total_size, 0) as total_size,
        (SELECT COUNT(*) FROM file_nodes WHERE subject_id = s.id AND type = 'folder') as folder_count
      FROM subjects s
      WHERE s.id = ?
    `

    return db.prepare(query).get(id)
  }
}

module.exports = SubjectService
```

#### 交付物清单
1. **数据库扩展**: 触发器和索引优化脚本
2. **管理API路由**: `src/routes/admin.js`
3. **控制器实现**: `src/controllers/admin/SubjectController.js`
4. **服务层逻辑**: `src/services/admin/SubjectService.js`
5. **参数验证**: `src/middleware/validation.js`扩展
6. **API文档**: `docs/api/admin-api.md`

#### 验收标准
- [ ] 所有管理API接口功能完整，响应格式符合规范
- [ ] 学科创建、更新、删除操作正常，数据一致性保证
- [ ] 级联删除功能正确，文件系统和数据库同步清理
- [ ] 参数验证严格，重复名称检查有效
- [ ] 错误处理完善，所有异常情况都有适当的错误响应
- [ ] 统计信息准确，触发器自动更新机制正常
- [ ] API响应时间符合要求（≤1秒）
- [ ] 事务处理确保数据一致性

#### 技术规范
- 使用数据库事务确保操作原子性
- 统一的错误处理和响应格式
- 参数验证使用中间件统一处理
- 文件系统操作与数据库操作保持同步
- 合理的索引设计优化查询性能
- 清晰的日志记录便于问题排查

---

### 任务2.3: Vben Admin管理界面搭建

#### 基本信息
- **任务编号**: 2.3
- **任务名称**: Vben Admin管理界面搭建
- **负责人**: Alex (工程师)
- **预估工期**: 1.5个工作日
- **优先级**: P0 (最高优先级)
- **依赖关系**: 依赖任务2.2（学科管理后端API开发）

#### 技术实现要求

**2.3.1 Vben Admin框架集成**
```bash
# 安装Vben Admin相关依赖
npm install @vben/types @vben/utils
npm install @ant-design/icons-vue
npm install @vueuse/core
npm install lodash-es
npm install dayjs
```

```typescript
// Vben Admin配置 (src/config/vben.ts)
import type { ProjectConfig } from '@vben/types'

export const projectConfig: ProjectConfig = {
  permissionCacheType: 'localStorage',
  themeColor: '#0960bd',
  grayMode: false,
  colorWeak: false,
  fullContent: false,
  contentMode: 'full',
  showLogo: true,
  showFooter: false,
  headerSetting: {
    bgColor: '#ffffff',
    fixed: true,
    show: true,
    theme: 'light',
    showFullScreen: true,
    useLockPage: false,
    showDoc: false,
    showNotice: false,
    showSearch: false
  },
  menuSetting: {
    bgColor: '#001529',
    fixed: true,
    collapsed: false,
    canDrag: false,
    show: true,
    hidden: false,
    split: false,
    menuWidth: 210,
    mode: 'vertical',
    type: 'sidebar',
    theme: 'dark',
    topMenuAlign: 'center',
    trigger: 'HEADER',
    accordion: true,
    closeMixSidebarOnChange: false,
    collapsedShowTitle: false,
    mixSideTrigger: 'click',
    mixSideFixed: false
  },
  multiTabsSetting: {
    cache: false,
    show: true,
    showQuick: true,
    canDrag: true,
    showRedo: true,
    showFold: true
  },
  transitionSetting: {
    enable: true,
    basicTransition: 'fade-slide',
    openPageLoading: true,
    openNProgress: false
  },
  openKeepAlive: true,
  lockTime: 0,
  showBreadCrumb: true,
  showBreadCrumbIcon: false,
  useErrorHandle: true,
  useOpenBackTop: true,
  canEmbedIFramePage: true,
  closeMessageOnSwitch: true,
  removeAllHttpPending: false
}
```

**2.3.2 管理后台布局组件**
```vue
<!-- 管理后台布局 (src/layouts/AdminLayout.vue) -->
<template>
  <div class="admin-layout">
    <a-layout class="layout-container">
      <!-- 侧边栏 -->
      <a-layout-sider
        v-model:collapsed="collapsed"
        :trigger="null"
        collapsible
        class="layout-sider"
        theme="dark"
        width="210"
      >
        <div class="logo">
          <img src="/logo.png" alt="Logo" v-if="!collapsed" />
          <img src="/logo-mini.png" alt="Logo" v-else />
          <span v-if="!collapsed" class="logo-text">管理后台</span>
        </div>

        <a-menu
          v-model:selectedKeys="selectedKeys"
          v-model:openKeys="openKeys"
          mode="inline"
          theme="dark"
          :inline-collapsed="collapsed"
          @click="handleMenuClick"
        >
          <a-menu-item key="subjects">
            <template #icon>
              <book-outlined />
            </template>
            <span>学科管理</span>
          </a-menu-item>

          <a-menu-item key="files" disabled>
            <template #icon>
              <file-outlined />
            </template>
            <span>文件管理</span>
          </a-menu-item>

          <a-menu-item key="settings" disabled>
            <template #icon>
              <setting-outlined />
            </template>
            <span>系统设置</span>
          </a-menu-item>
        </a-menu>
      </a-layout-sider>

      <!-- 主内容区 -->
      <a-layout class="layout-content">
        <!-- 顶部导航 -->
        <a-layout-header class="layout-header">
          <div class="header-left">
            <menu-unfold-outlined
              v-if="collapsed"
              class="trigger"
              @click="() => (collapsed = !collapsed)"
            />
            <menu-fold-outlined
              v-else
              class="trigger"
              @click="() => (collapsed = !collapsed)"
            />

            <a-breadcrumb class="breadcrumb">
              <a-breadcrumb-item>
                <home-outlined />
                <span>管理后台</span>
              </a-breadcrumb-item>
              <a-breadcrumb-item v-if="currentPageTitle">
                {{ currentPageTitle }}
              </a-breadcrumb-item>
            </a-breadcrumb>
          </div>

          <div class="header-right">
            <a-space>
              <a-button type="link" @click="goToFrontend">
                <template #icon>
                  <eye-outlined />
                </template>
                查看前台
              </a-button>

              <a-dropdown>
                <a-button type="link">
                  <template #icon>
                    <user-outlined />
                  </template>
                  管理员
                  <down-outlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="logout" @click="handleLogout">
                      <logout-outlined />
                      退出登录
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </div>
        </a-layout-header>

        <!-- 内容区域 -->
        <a-layout-content class="main-content">
          <div class="content-wrapper">
            <router-view />
          </div>
        </a-layout-content>

        <!-- 底部 -->
        <a-layout-footer class="layout-footer">
          期末复习平台管理后台 ©2025 Created by 随影
        </a-layout-footer>
      </a-layout>
    </a-layout>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BookOutlined,
  FileOutlined,
  SettingOutlined,
  HomeOutlined,
  EyeOutlined,
  UserOutlined,
  DownOutlined,
  LogoutOutlined
} from '@ant-design/icons-vue'

const route = useRoute()
const router = useRouter()

const collapsed = ref(false)
const selectedKeys = ref(['subjects'])
const openKeys = ref([])

const currentPageTitle = computed(() => {
  const routeMeta = route.meta
  return routeMeta?.title || ''
})

// 监听路由变化更新选中菜单
watch(
  () => route.path,
  (newPath) => {
    if (newPath.includes('subjects')) {
      selectedKeys.value = ['subjects']
    }
  },
  { immediate: true }
)

const handleMenuClick = ({ key }: { key: string }) => {
  switch (key) {
    case 'subjects':
      router.push('subjects')
      break
    default:
      break
  }
}

const goToFrontend = () => {
  window.open('/', '_blank')
}

const handleLogout = () => {
  // 清除管理后台访问标识
  localStorage.removeItem('admin-access')
  // 重定向到首页
  window.location.href = '/'
}
</script>

<style scoped>
.admin-layout {
  height: 100vh;
}

.layout-container {
  height: 100%;
}

.layout-sider {
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 100;
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  background: rgba(255, 255, 255, 0.1);
}

.logo img {
  height: 32px;
  width: auto;
}

.logo-text {
  color: white;
  font-size: 16px;
  font-weight: bold;
  margin-left: 8px;
}

.layout-content {
  margin-left: 210px;
  transition: margin-left 0.2s;
}

.layout-content.collapsed {
  margin-left: 80px;
}

.layout-header {
  background: #fff;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  position: relative;
  z-index: 10;
}

.header-left {
  display: flex;
  align-items: center;
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}

.trigger:hover {
  color: #1890ff;
}

.breadcrumb {
  margin-left: 16px;
}

.main-content {
  margin: 16px;
  padding: 24px;
  background: #fff;
  min-height: calc(100vh - 64px - 70px - 32px);
  border-radius: 6px;
}

.content-wrapper {
  min-height: 100%;
}

.layout-footer {
  text-align: center;
  background: #f0f2f5;
  padding: 12px 0;
  color: rgba(0, 0, 0, 0.65);
}
</style>
```

**2.3.3 学科管理页面组件**
```vue
<!-- 学科管理页面 (src/views/admin/SubjectManagement.vue) -->
<template>
  <div class="subject-management">
    <div class="page-header">
      <div class="header-content">
        <h2>学科管理</h2>
        <p>管理平台的学科分类，创建、编辑和删除学科</p>
      </div>
      <div class="header-actions">
        <a-button type="primary" @click="showCreateModal">
          <template #icon>
            <plus-outlined />
          </template>
          创建学科
        </a-button>
      </div>
    </div>

    <!-- 学科列表 -->
    <a-card class="subject-list-card">
      <a-table
        :columns="columns"
        :data-source="subjects"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'name'">
            <div class="subject-name">
              <book-outlined class="subject-icon" />
              <span class="name-text">{{ record.name }}</span>
            </div>
          </template>

          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'stats'">
            <div class="stats-info">
              <div class="stat-item">
                <file-outlined />
                <span>{{ record.file_count }} 个文件</span>
              </div>
              <div class="stat-item">
                <cloud-outlined />
                <span>{{ formatFileSize(record.total_size) }}</span>
              </div>
            </div>
          </template>

          <template v-else-if="column.key === 'created_at'">
            {{ formatDate(record.created_at) }}
          </template>

          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-button type="link" size="small" @click="editSubject(record)">
                <template #icon>
                  <edit-outlined />
                </template>
                编辑
              </a-button>

              <a-button
                type="link"
                size="small"
                danger
                @click="deleteSubject(record)"
                :disabled="record.file_count > 0"
              >
                <template #icon>
                  <delete-outlined />
                </template>
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑学科模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEditing ? '编辑学科' : '创建学科'"
      :confirm-loading="modalLoading"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-form-item label="学科名称" name="name">
          <a-input
            v-model:value="formData.name"
            placeholder="请输入学科名称"
            :maxlength="50"
            show-count
          />
        </a-form-item>

        <a-form-item label="学科描述" name="description">
          <a-textarea
            v-model:value="formData.description"
            placeholder="请输入学科描述（可选）"
            :rows="3"
            :maxlength="200"
            show-count
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  BookOutlined,
  FileOutlined,
  CloudOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import { useAdminApi } from '@/composables/useAdminApi'
import { formatDate, formatFileSize } from '@/utils/format'

interface Subject {
  id: number
  name: string
  description: string
  file_count: number
  total_size: number
  status: 'active' | 'empty'
  created_at: string
  updated_at: string
}

const { adminApi } = useAdminApi()

// 表格配置
const columns = [
  {
    title: '学科名称',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '统计信息',
    key: 'stats',
    width: 200
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 150
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right'
  }
]

// 数据状态
const subjects = ref<Subject[]>([])
const loading = ref(false)
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 模态框状态
const modalVisible = ref(false)
const modalLoading = ref(false)
const isEditing = ref(false)
const currentSubject = ref<Subject | null>(null)

// 表单数据
const formRef = ref()
const formData = reactive({
  name: '',
  description: ''
})

const formRules = {
  name: [
    { required: true, message: '请输入学科名称', trigger: 'blur' },
    { min: 1, max: 50, message: '学科名称长度为1-50个字符', trigger: 'blur' }
  ]
}

// 获取学科列表
const fetchSubjects = async () => {
  loading.value = true
  try {
    const response = await adminApi.get('/subjects')
    subjects.value = response.data.data
    pagination.total = response.data.meta.total
  } catch (error) {
    message.error('获取学科列表失败')
  } finally {
    loading.value = false
  }
}

// 状态相关方法
const getStatusColor = (status: string) => {
  return status === 'active' ? 'green' : 'default'
}

const getStatusText = (status: string) => {
  return status === 'active' ? '有内容' : '空学科'
}

// 表格事件处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  fetchSubjects()
}

// 模态框相关方法
const showCreateModal = () => {
  isEditing.value = false
  currentSubject.value = null
  formData.name = ''
  formData.description = ''
  modalVisible.value = true
}

const editSubject = (subject: Subject) => {
  isEditing.value = true
  currentSubject.value = subject
  formData.name = subject.name
  formData.description = subject.description || ''
  modalVisible.value = true
}

const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    modalLoading.value = true

    if (isEditing.value && currentSubject.value) {
      // 编辑学科
      await adminApi.put(`/subjects/${currentSubject.value.id}`, formData)
      message.success('学科更新成功')
    } else {
      // 创建学科
      await adminApi.post('/subjects', formData)
      message.success('学科创建成功')
    }

    modalVisible.value = false
    await fetchSubjects()
  } catch (error: any) {
    if (error.response?.data?.error?.code === 'DUPLICATE_NAME') {
      message.error('学科名称已存在')
    } else {
      message.error(isEditing.value ? '学科更新失败' : '学科创建失败')
    }
  } finally {
    modalLoading.value = false
  }
}

const handleModalCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
}

// 删除学科
const deleteSubject = (subject: Subject) => {
  if (subject.file_count > 0) {
    message.warning('该学科下还有文件，无法删除')
    return
  }

  Modal.confirm({
    title: '确认删除',
    content: `确定要删除学科"${subject.name}"吗？此操作不可恢复。`,
    okText: '确认删除',
    okType: 'danger',
    cancelText: '取消',
    onOk: async () => {
      try {
        await adminApi.delete(`/subjects/${subject.id}`)
        message.success('学科删除成功')
        await fetchSubjects()
      } catch (error) {
        message.error('学科删除失败')
      }
    }
  })
}

// 初始化
onMounted(() => {
  fetchSubjects()
})
</script>

<style scoped>
.subject-management {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-content h2 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #262626;
}

.header-content p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.subject-list-card {
  border-radius: 8px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
}

.subject-name {
  display: flex;
  align-items: center;
}

.subject-icon {
  color: #1890ff;
  margin-right: 8px;
}

.name-text {
  font-weight: 500;
}

.stats-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #8c8c8c;
}

.stat-item .anticon {
  margin-right: 4px;
}
</style>
```

#### 交付物清单
1. **Vben Admin配置**: `src/config/vben.ts`
2. **管理布局组件**: `src/layouts/AdminLayout.vue`
3. **学科管理页面**: `src/views/admin/SubjectManagement.vue`
4. **管理API组合函数**: `src/composables/useAdminApi.ts`
5. **工具函数**: `src/utils/format.ts`
6. **样式文件**: 管理后台专用样式
7. **界面文档**: `docs/admin/ui-guide.md`

#### 验收标准
- [ ] Vben Admin框架集成成功，管理后台界面美观
- [ ] 学科管理页面功能完整，支持创建、编辑、删除操作
- [ ] 表格显示学科列表，包含统计信息和状态
- [ ] 模态框表单验证正确，用户体验良好
- [ ] 响应式布局适配不同屏幕尺寸
- [ ] 错误处理完善，用户操作有清晰反馈
- [ ] 界面风格统一，符合现代管理后台设计规范
- [ ] 操作流程顺畅，无明显性能问题

#### 技术规范
- 使用Vue3 Composition API和TypeScript
- 遵循Vben Admin设计规范和最佳实践
- 组件化开发，代码结构清晰
- 统一的错误处理和用户反馈机制
- 响应式设计，支持多种设备
- 合理的加载状态和空状态处理

---

### 任务2.4: 前后端集成联调

#### 基本信息
- **任务编号**: 2.4
- **任务名称**: 前后端集成联调
- **负责人**: Alex (工程师)
- **预估工期**: 0.5个工作日
- **优先级**: P0 (最高优先级)
- **依赖关系**: 依赖任务2.3（Vben Admin管理界面搭建）

#### 技术实现要求

**2.4.1 管理API组合函数**
```typescript
// 管理API组合函数 (src/composables/useAdminApi.ts)
import { ref } from 'vue'
import adminApi from '@/utils/adminApi'
import { message } from 'ant-design-vue'

export interface Subject {
  id: number
  name: string
  description: string
  file_count: number
  total_size: number
  status: 'active' | 'empty'
  created_at: string
  updated_at: string
}

export function useAdminApi() {
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 获取学科列表
  const fetchSubjects = async (): Promise<Subject[]> => {
    loading.value = true
    error.value = null
    try {
      const response = await adminApi.get('/subjects')
      return response.data.data
    } catch (err: any) {
      error.value = err.response?.data?.error?.message || '获取学科列表失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 创建学科
  const createSubject = async (data: { name: string; description?: string }): Promise<Subject> => {
    loading.value = true
    error.value = null
    try {
      const response = await adminApi.post('/subjects', data)
      return response.data.data
    } catch (err: any) {
      error.value = err.response?.data?.error?.message || '创建学科失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 更新学科
  const updateSubject = async (id: number, data: { name: string; description?: string }): Promise<Subject> => {
    loading.value = true
    error.value = null
    try {
      const response = await adminApi.put(`/subjects/${id}`, data)
      return response.data.data
    } catch (err: any) {
      error.value = err.response?.data?.error?.message || '更新学科失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 删除学科
  const deleteSubject = async (id: number): Promise<void> => {
    loading.value = true
    error.value = null
    try {
      await adminApi.delete(`/subjects/${id}`)
    } catch (err: any) {
      error.value = err.response?.data?.error?.message || '删除学科失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 获取学科统计信息
  const getSubjectStats = async (id: number) => {
    loading.value = true
    error.value = null
    try {
      const response = await adminApi.get(`/subjects/${id}/stats`)
      return response.data.data
    } catch (err: any) {
      error.value = err.response?.data?.error?.message || '获取学科统计失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    error,
    adminApi,
    fetchSubjects,
    createSubject,
    updateSubject,
    deleteSubject,
    getSubjectStats
  }
}
```

**2.4.2 工具函数实现**
```typescript
// 格式化工具函数 (src/utils/format.ts)
import dayjs from 'dayjs'

// 格式化日期
export function formatDate(date: string | Date, format = 'YYYY-MM-DD HH:mm'): string {
  if (!date) return '-'
  return dayjs(date).format(format)
}

// 格式化文件大小
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化数字
export function formatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 截断文本
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}
```

**2.4.3 集成测试用例**
```typescript
// 集成测试 (tests/integration/admin-integration.spec.ts)
import { test, expect } from '@playwright/test'

test.describe('管理后台集成测试', () => {
  const adminPath = process.env.ADMIN_PANEL_PATH || 'admin-panel-abcdef'
  const adminUrl = `/${adminPath}`

  test.beforeEach(async ({ page }) => {
    // 启动后端服务和前端服务
    await page.goto(adminUrl)
    await page.waitForSelector('.admin-layout', { timeout: 10000 })
  })

  test('管理后台访问和布局测试', async ({ page }) => {
    // 验证管理后台布局加载
    await expect(page.locator('.admin-layout')).toBeVisible()
    await expect(page.locator('.layout-sider')).toBeVisible()
    await expect(page.locator('.layout-header')).toBeVisible()

    // 验证侧边栏菜单
    await expect(page.locator('text=学科管理')).toBeVisible()

    // 验证顶部导航
    await expect(page.locator('text=管理后台')).toBeVisible()
    await expect(page.locator('text=查看前台')).toBeVisible()
  })

  test('学科管理完整流程测试', async ({ page }) => {
    // 1. 进入学科管理页面
    await page.click('text=学科管理')
    await page.waitForSelector('.subject-management')

    // 验证页面标题和创建按钮
    await expect(page.locator('h2:has-text("学科管理")')).toBeVisible()
    await expect(page.locator('button:has-text("创建学科")')).toBeVisible()

    // 2. 创建新学科
    await page.click('button:has-text("创建学科")')
    await page.waitForSelector('.ant-modal')

    // 填写学科信息
    const testSubjectName = `测试学科_${Date.now()}`
    await page.fill('input[placeholder="请输入学科名称"]', testSubjectName)
    await page.fill('textarea[placeholder="请输入学科描述（可选）"]', '这是一个测试学科')

    // 提交创建
    await page.click('.ant-modal button:has-text("确定")')
    await page.waitForSelector('.ant-message-success')

    // 验证学科创建成功
    await expect(page.locator(`text=${testSubjectName}`)).toBeVisible()

    // 3. 编辑学科
    const editButton = page.locator(`tr:has-text("${testSubjectName}") button:has-text("编辑")`)
    await editButton.click()
    await page.waitForSelector('.ant-modal')

    // 修改学科名称
    const updatedName = `${testSubjectName}_更新`
    await page.fill('input[placeholder="请输入学科名称"]', updatedName)
    await page.click('.ant-modal button:has-text("确定")')
    await page.waitForSelector('.ant-message-success')

    // 验证学科更新成功
    await expect(page.locator(`text=${updatedName}`)).toBeVisible()

    // 4. 删除学科
    const deleteButton = page.locator(`tr:has-text("${updatedName}") button:has-text("删除")`)
    await deleteButton.click()
    await page.waitForSelector('.ant-modal')
    await page.click('.ant-modal button:has-text("确认删除")')
    await page.waitForSelector('.ant-message-success')

    // 验证学科删除成功
    await expect(page.locator(`text=${updatedName}`)).not.toBeVisible()
  })

  test('学科管理错误处理测试', async ({ page }) => {
    await page.click('text=学科管理')
    await page.waitForSelector('.subject-management')

    // 测试创建重复学科名称
    await page.click('button:has-text("创建学科")')
    await page.waitForSelector('.ant-modal')

    // 使用已存在的学科名称
    await page.fill('input[placeholder="请输入学科名称"]', '数据结构')
    await page.click('.ant-modal button:has-text("确定")')

    // 验证错误提示
    await expect(page.locator('.ant-message-error')).toBeVisible()

    // 关闭模态框
    await page.click('.ant-modal button:has-text("取消")')
  })

  test('管理后台与前台集成测试', async ({ page, context }) => {
    // 在管理后台创建学科
    await page.click('text=学科管理')
    await page.waitForSelector('.subject-management')

    await page.click('button:has-text("创建学科")')
    await page.waitForSelector('.ant-modal')

    const testSubjectName = `集成测试学科_${Date.now()}`
    await page.fill('input[placeholder="请输入学科名称"]', testSubjectName)
    await page.click('.ant-modal button:has-text("确定")')
    await page.waitForSelector('.ant-message-success')

    // 点击"查看前台"按钮
    const [frontendPage] = await Promise.all([
      context.waitForEvent('page'),
      page.click('button:has-text("查看前台")')
    ])

    await frontendPage.waitForLoadState()

    // 验证新创建的学科在前台可见
    await expect(frontendPage.locator(`text=${testSubjectName}`)).toBeVisible()

    // 清理：删除测试学科
    await page.bringToFront()
    const deleteButton = page.locator(`tr:has-text("${testSubjectName}") button:has-text("删除")`)
    await deleteButton.click()
    await page.waitForSelector('.ant-modal')
    await page.click('.ant-modal button:has-text("确认删除")')
    await page.waitForSelector('.ant-message-success')
  })
})
```

#### 交付物清单
1. **管理API组合函数**: `src/composables/useAdminApi.ts`
2. **工具函数**: `src/utils/format.ts`
3. **集成测试**: `tests/integration/admin-integration.spec.ts`
4. **错误处理**: 统一的错误处理机制
5. **性能优化**: API请求缓存和防抖
6. **联调文档**: `docs/admin/integration-guide.md`

#### 验收标准
- [ ] 前端能够成功调用所有管理API接口
- [ ] 学科的创建、编辑、删除操作在前端正确显示
- [ ] 错误处理机制完善，用户能够得到清晰的错误提示
- [ ] 管理后台与访客前台数据同步正常
- [ ] 所有集成测试用例通过
- [ ] 性能符合要求（操作响应时间≤1秒）
- [ ] 浏览器控制台无错误信息
- [ ] 用户体验流畅，操作反馈及时

#### 技术规范
- 使用TypeScript进行类型检查
- 统一的API调用和错误处理机制
- 合理的加载状态管理
- 清晰的用户操作反馈
- 符合RESTful API调用规范
- 完整的集成测试覆盖

---

### 任务2.5: 自动化测试用例编写

#### 基本信息
- **任务编号**: 2.5
- **任务名称**: 自动化测试用例编写
- **负责人**: Alex (工程师)
- **预估工期**: 0.5个工作日
- **优先级**: P0 (最高优先级)
- **依赖关系**: 依赖任务2.4（前后端集成联调）

#### 技术实现要求

**2.5.1 管理API测试用例**
```javascript
// API测试 (tests/api/admin-api.spec.js)
const { test, expect } = require('@playwright/test')

test.describe('管理API测试', () => {
  const baseURL = 'http://localhost:3000/api/admin'
  const adminPath = process.env.ADMIN_PANEL_PATH || 'admin-panel-abcdef'

  test('POST /api/admin/subjects - 创建学科', async ({ request }) => {
    const response = await request.post(`${baseURL}/subjects`, {
      headers: {
        'x-admin-path': adminPath,
        'Content-Type': 'application/json'
      },
      data: {
        name: `测试学科_${Date.now()}`,
        description: '这是一个测试学科'
      }
    })

    expect(response.status()).toBe(201)

    const data = await response.json()
    expect(data.success).toBe(true)
    expect(data.data.name).toBeDefined()
    expect(data.data.id).toBeDefined()
    expect(data.meta.message).toBe('学科创建成功')
  })

  test('GET /api/admin/subjects - 获取学科列表', async ({ request }) => {
    const response = await request.get(`${baseURL}/subjects`, {
      headers: {
        'x-admin-path': adminPath
      }
    })

    expect(response.status()).toBe(200)

    const data = await response.json()
    expect(data.success).toBe(true)
    expect(Array.isArray(data.data)).toBe(true)
    expect(data.meta.total).toBeDefined()

    // 验证学科数据结构
    if (data.data.length > 0) {
      const subject = data.data[0]
      expect(subject.id).toBeDefined()
      expect(subject.name).toBeDefined()
      expect(subject.file_count).toBeDefined()
      expect(subject.total_size).toBeDefined()
      expect(subject.status).toBeDefined()
    }
  })

  test('PUT /api/admin/subjects/:id - 更新学科', async ({ request }) => {
    // 先创建一个学科
    const createResponse = await request.post(`${baseURL}/subjects`, {
      headers: {
        'x-admin-path': adminPath,
        'Content-Type': 'application/json'
      },
      data: {
        name: `更新测试学科_${Date.now()}`,
        description: '待更新的学科'
      }
    })

    const createData = await createResponse.json()
    const subjectId = createData.data.id

    // 更新学科
    const updateResponse = await request.put(`${baseURL}/subjects/${subjectId}`, {
      headers: {
        'x-admin-path': adminPath,
        'Content-Type': 'application/json'
      },
      data: {
        name: `已更新学科_${Date.now()}`,
        description: '已更新的学科描述'
      }
    })

    expect(updateResponse.status()).toBe(200)

    const updateData = await updateResponse.json()
    expect(updateData.success).toBe(true)
    expect(updateData.data.name).toContain('已更新学科')
    expect(updateData.meta.message).toBe('学科更新成功')
  })

  test('DELETE /api/admin/subjects/:id - 删除学科', async ({ request }) => {
    // 先创建一个学科
    const createResponse = await request.post(`${baseURL}/subjects`, {
      headers: {
        'x-admin-path': adminPath,
        'Content-Type': 'application/json'
      },
      data: {
        name: `删除测试学科_${Date.now()}`,
        description: '待删除的学科'
      }
    })

    const createData = await createResponse.json()
    const subjectId = createData.data.id

    // 删除学科
    const deleteResponse = await request.delete(`${baseURL}/subjects/${subjectId}`, {
      headers: {
        'x-admin-path': adminPath
      }
    })

    expect(deleteResponse.status()).toBe(200)

    const deleteData = await deleteResponse.json()
    expect(deleteData.success).toBe(true)
    expect(deleteData.meta.message).toContain('学科删除成功')
  })

  test('API错误处理测试', async ({ request }) => {
    // 测试重复学科名称
    const duplicateName = `重复测试_${Date.now()}`

    // 创建第一个学科
    await request.post(`${baseURL}/subjects`, {
      headers: {
        'x-admin-path': adminPath,
        'Content-Type': 'application/json'
      },
      data: { name: duplicateName }
    })

    // 尝试创建同名学科
    const duplicateResponse = await request.post(`${baseURL}/subjects`, {
      headers: {
        'x-admin-path': adminPath,
        'Content-Type': 'application/json'
      },
      data: { name: duplicateName }
    })

    expect(duplicateResponse.status()).toBe(400)

    const duplicateData = await duplicateResponse.json()
    expect(duplicateData.success).toBe(false)
    expect(duplicateData.error.code).toBe('DUPLICATE_NAME')

    // 测试访问不存在的学科
    const notFoundResponse = await request.get(`${baseURL}/subjects/99999`, {
      headers: {
        'x-admin-path': adminPath
      }
    })

    expect(notFoundResponse.status()).toBe(404)

    // 测试无效的管理路径
    const unauthorizedResponse = await request.get(`${baseURL}/subjects`, {
      headers: {
        'x-admin-path': 'invalid-path'
      }
    })

    expect(unauthorizedResponse.status()).toBe(403)
  })
})
```

**2.5.2 端到端测试用例**
```javascript
// E2E测试 (tests/e2e/admin-e2e.spec.js)
const { test, expect } = require('@playwright/test')

test.describe('管理后台E2E测试', () => {
  const adminPath = process.env.ADMIN_PANEL_PATH || 'admin-panel-abcdef'
  const adminUrl = `/${adminPath}`

  test('完整的学科管理流程', async ({ page }) => {
    // 1. 访问管理后台
    await page.goto(adminUrl)
    await page.waitForSelector('.admin-layout')

    // 验证管理后台加载
    await expect(page.locator('h2:has-text("学科管理")')).toBeVisible()

    // 2. 创建新学科
    await page.click('button:has-text("创建学科")')
    await page.waitForSelector('.ant-modal')

    const testSubjectName = `E2E测试学科_${Date.now()}`
    await page.fill('input[placeholder="请输入学科名称"]', testSubjectName)
    await page.fill('textarea[placeholder="请输入学科描述（可选）"]', 'E2E测试学科描述')

    await page.click('.ant-modal button:has-text("确定")')
    await page.waitForSelector('.ant-message-success')

    // 验证学科出现在列表中
    await expect(page.locator(`text=${testSubjectName}`)).toBeVisible()

    // 3. 验证学科状态显示
    const subjectRow = page.locator(`tr:has-text("${testSubjectName}")`)
    await expect(subjectRow.locator('.ant-tag:has-text("空学科")')).toBeVisible()
    await expect(subjectRow.locator('text=0 个文件')).toBeVisible()

    // 4. 编辑学科
    await subjectRow.locator('button:has-text("编辑")').click()
    await page.waitForSelector('.ant-modal')

    const updatedName = `${testSubjectName}_已编辑`
    await page.fill('input[placeholder="请输入学科名称"]', updatedName)
    await page.click('.ant-modal button:has-text("确定")')
    await page.waitForSelector('.ant-message-success')

    // 验证学科名称更新
    await expect(page.locator(`text=${updatedName}`)).toBeVisible()

    // 5. 删除学科
    const updatedRow = page.locator(`tr:has-text("${updatedName}")`)
    await updatedRow.locator('button:has-text("删除")').click()
    await page.waitForSelector('.ant-modal')
    await page.click('.ant-modal button:has-text("确认删除")')
    await page.waitForSelector('.ant-message-success')

    // 验证学科从列表中消失
    await expect(page.locator(`text=${updatedName}`)).not.toBeVisible()
  })

  test('表单验证测试', async ({ page }) => {
    await page.goto(adminUrl)
    await page.waitForSelector('.admin-layout')

    // 测试空学科名称
    await page.click('button:has-text("创建学科")')
    await page.waitForSelector('.ant-modal')

    await page.click('.ant-modal button:has-text("确定")')

    // 验证表单验证错误
    await expect(page.locator('.ant-form-item-explain-error')).toBeVisible()

    // 测试学科名称长度限制
    await page.fill('input[placeholder="请输入学科名称"]', 'a'.repeat(60))
    await page.click('.ant-modal button:has-text("确定")')

    // 验证长度验证错误
    await expect(page.locator('.ant-form-item-explain-error')).toBeVisible()

    await page.click('.ant-modal button:has-text("取消")')
  })

  test('响应式布局测试', async ({ page }) => {
    await page.goto(adminUrl)
    await page.waitForSelector('.admin-layout')

    // 测试桌面端布局
    await page.setViewportSize({ width: 1200, height: 800 })
    await expect(page.locator('.layout-sider')).toBeVisible()
    await expect(page.locator('.layout-sider')).toHaveCSS('width', '210px')

    // 测试平板端布局
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.waitForTimeout(500) // 等待布局调整

    // 验证侧边栏响应式行为
    const sider = page.locator('.layout-sider')
    await expect(sider).toBeVisible()

    // 测试移动端布局
    await page.setViewportSize({ width: 375, height: 667 })
    await page.waitForTimeout(500)

    // 验证移动端表格响应式
    await expect(page.locator('.ant-table-wrapper')).toBeVisible()
  })

  test('性能测试', async ({ page }) => {
    await page.goto(adminUrl)

    // 测试页面加载时间
    const startTime = Date.now()
    await page.waitForSelector('.subject-management')
    const loadTime = Date.now() - startTime

    console.log(`管理后台加载时间: ${loadTime}ms`)
    expect(loadTime).toBeLessThan(3000) // 3秒内加载完成

    // 测试学科创建响应时间
    await page.click('button:has-text("创建学科")')
    await page.waitForSelector('.ant-modal')

    const createStartTime = Date.now()
    await page.fill('input[placeholder="请输入学科名称"]', `性能测试学科_${Date.now()}`)
    await page.click('.ant-modal button:has-text("确定")')
    await page.waitForSelector('.ant-message-success')
    const createTime = Date.now() - createStartTime

    console.log(`学科创建响应时间: ${createTime}ms`)
    expect(createTime).toBeLessThan(2000) // 2秒内完成创建
  })
})
```

#### 交付物清单
1. **管理API测试**: `tests/api/admin-api.spec.js`
2. **端到端测试**: `tests/e2e/admin-e2e.spec.js`
3. **集成测试**: `tests/integration/admin-integration.spec.js`
4. **性能测试**: 页面加载和操作响应时间测试
5. **测试配置**: Playwright配置更新
6. **测试报告**: HTML格式的测试结果报告
7. **测试文档**: `docs/testing/admin-testing-guide.md`

#### 验收标准
- [ ] 所有管理API测试用例通过，覆盖正常和异常情况
- [ ] 端到端测试验证完整的用户操作流程
- [ ] 表单验证测试确保用户输入的正确性
- [ ] 响应式布局测试在不同设备上通过
- [ ] 性能测试验证页面加载和操作响应时间符合要求
- [ ] 错误处理测试验证异常情况的处理
- [ ] 测试覆盖率达到85%以上
- [ ] 测试可以在CI/CD环境中自动执行

#### 技术规范
- 使用Playwright进行全栈测试
- 测试用例命名清晰，描述准确
- 测试数据独立，避免测试间相互影响
- 测试断言明确，错误信息清晰
- 测试执行稳定，减少偶发性失败
- 测试报告包含详细的执行信息

---

## 3. Sprint执行计划

### 3.1 任务依赖关系
```
任务2.1 (管理后台访问控制实现) [1天]
    ↓
任务2.2 (学科管理后端API开发) [1.5天]
    ↓
任务2.3 (Vben Admin管理界面搭建) [1.5天]
    ↓
任务2.4 (前后端集成联调) [0.5天]
    ↓
任务2.5 (自动化测试用例编写) [0.5天]
```

### 3.2 时间安排
- **第1天**: 任务2.1 - 管理后台访问控制实现
- **第2-2.5天**: 任务2.2 - 学科管理后端API开发
- **第3-4天**: 任务2.3 - Vben Admin管理界面搭建
- **第4.5天**: 任务2.4 - 前后端集成联调
- **第5天**: 任务2.5 - 自动化测试用例编写

### 3.3 质量检查点
- **每日代码审查**: 确保代码质量和规范性
- **中期集成测试**: 第3天进行中期集成验证
- **最终验收测试**: 第5天进行完整功能验收

### 3.4 风险缓解
- **技术风险**: Vben Admin集成可能遇到兼容性问题，预留调试时间
- **集成风险**: 管理后台与访客前台的数据同步，提前进行接口联调
- **性能风险**: 在开发过程中持续监控响应时间

## 4. 验收标准总结

### 4.1 功能验收
- [ ] 管理员能够通过特定URL访问管理后台
- [ ] 管理员能够创建、编辑、删除学科
- [ ] 学科管理操作在前台实时生效
- [ ] 管理界面美观易用，符合现代设计规范
- [ ] 错误处理完善，用户操作有清晰反馈

### 4.2 技术验收
- [ ] 访问控制机制安全可靠
- [ ] 管理API接口功能完整，响应格式统一
- [ ] Vben Admin集成成功，界面组件正常工作
- [ ] 前后端数据同步无问题
- [ ] 自动化测试覆盖率达标，测试通过

### 4.3 性能验收
- [ ] 管理后台页面加载时间 ≤ 3秒
- [ ] 学科管理操作响应时间 ≤ 1秒
- [ ] API接口响应时间 ≤ 500ms
- [ ] 支持多用户并发访问
- [ ] 内存使用稳定，无内存泄漏

### 4.4 用户体验验收
- [ ] 管理界面直观易用，操作流程顺畅
- [ ] 表单验证及时准确，错误提示清晰
- [ ] 响应式布局适配不同设备
- [ ] 加载状态明确，用户不会困惑

## 5. 交付清单

### 5.1 代码交付物
- [ ] 完整的管理后台访问控制代码
- [ ] 完整的学科管理API服务代码
- [ ] 完整的Vben Admin管理界面代码
- [ ] 完整的自动化测试套件

### 5.2 文档交付物
- [ ] 管理API接口文档
- [ ] 管理后台使用文档
- [ ] 访问控制配置文档
- [ ] 测试用例文档

### 5.3 配置交付物
- [ ] 管理后台环境配置文件
- [ ] Vben Admin构建配置
- [ ] 测试环境配置
- [ ] 部署配置更新

---

**文档状态**: ✅ 已完成
**下一步**: 等待老板审核，准备启动Sprint-02开发工作
**版权声明**: 本文档版权归属于【随影】

*详细执行清单完成时间: 2025-01-27*
*规划人员: Bob (架构师) + Mike (团队领袖)*
