// Mock数据库实现，用于测试环境
export class MockDatabase {
  private data: Map<string, any[]> = new Map()
  private statements: Map<string, any> = new Map()

  constructor() {
    this.reset()
  }

  // 重置数据库状态
  reset() {
    this.data.clear()
    this.data.set('subjects', [])
    this.data.set('file_nodes', [])
    console.log('🔄 Mock database reset')
  }

  // 模拟pragma方法
  pragma(statement: string) {
    console.log(`Mock pragma: ${statement}`)
    return this
  }

  // 模拟prepare方法
  prepare(sql: string) {
    const mockStatement = {
      run: (...params: any[]) => {
        console.log(`Mock run: ${sql}`, params)

        // 模拟INSERT操作
        if (sql.includes('INSERT INTO subjects')) {
          const [id, name, created_at, updated_at, file_count] = params
          const subjects = this.data.get('subjects') || []

          // 检查重复名称
          if (subjects.find((s: any) => s.name === name)) {
            const error = new Error('UNIQUE constraint failed')
              ; (error as any).code = 'SQLITE_CONSTRAINT_UNIQUE'
            throw error
          }

          subjects.push({ id, name, created_at, updated_at, file_count })
          this.data.set('subjects', subjects)
          return { changes: 1 }
        }

        // 模拟DELETE操作
        if (sql.includes('DELETE FROM subjects')) {
          const [id] = params
          const subjects = this.data.get('subjects') || []
          const index = subjects.findIndex((s: any) => s.id === id)
          if (index >= 0) {
            subjects.splice(index, 1)
            this.data.set('subjects', subjects)
            return { changes: 1 }
          }
          return { changes: 0 }
        }

        // 模拟UPDATE操作
        if (sql.includes('UPDATE subjects')) {
          const subjects = this.data.get('subjects') || []
          // 简单的UPDATE实现，实际应该解析SQL
          if (params.length >= 2) {
            const id = params[params.length - 1] // ID通常是最后一个参数
            const index = subjects.findIndex((s: any) => s.id === id)
            if (index >= 0) {
              // 更新name字段（简化实现）
              if (params.length >= 3) {
                subjects[index].name = params[0]
                subjects[index].updated_at = params[1]
              }
              this.data.set('subjects', subjects)
              return { changes: 1 }
            }
          }
          return { changes: 0 }
        }

        return { changes: 0 }
      },

      get: (...params: any[]) => {
        console.log(`Mock get: ${sql}`, params)

        const sqlLower = sql.toLowerCase()

        // 模拟SELECT单条记录 - 更宽松的匹配
        if (sqlLower.includes('select') && sqlLower.includes('from subjects') && sqlLower.includes('where id = ?')) {
          const [id] = params
          const subjects = this.data.get('subjects') || []
          const found = subjects.find((s: any) => s.id === id)
          console.log(`🔍 Looking for subject with id ${id}, found:`, found ? 'YES' : 'NO')
          console.log(`📊 Available subjects:`, subjects.map(s => ({ id: s.id, name: s.name })))
          return found || null
        }

        if (sqlLower.includes('select') && sqlLower.includes('from subjects') && sqlLower.includes('where name = ?')) {
          const [name] = params
          const subjects = this.data.get('subjects') || []
          const found = subjects.find((s: any) => s.name === name)
          console.log(`🔍 Looking for subject with name ${name}, found:`, found ? 'YES' : 'NO')
          console.log(`📊 Available subjects:`, subjects.map(s => ({ id: s.id, name: s.name })))
          return found || null
        }

        if (sqlLower.includes('select count(*) as count')) {
          const subjects = this.data.get('subjects') || []
          console.log(`📊 Counting subjects: ${subjects.length}`)
          return { count: subjects.length }
        }

        if (sqlLower.includes('select 1 from subjects where id = ?')) {
          const [id] = params
          const subjects = this.data.get('subjects') || []
          const found = subjects.find((s: any) => s.id === id)
          return found ? { 1: 1 } : null
        }

        return null
      },

      all: (...params: any[]) => {
        console.log(`Mock all: ${sql}`, params)

        // 模拟SELECT多条记录 - 更宽松的匹配
        if (sql.toLowerCase().includes('select') && sql.toLowerCase().includes('from subjects')) {
          const subjects = this.data.get('subjects') || []
          console.log(`📊 Found ${subjects.length} subjects in mock database`)
          console.log(`📋 Subjects data:`, subjects.map(s => ({ id: s.id, name: s.name })))

          if (sql.toLowerCase().includes('order by created_at desc')) {
            const sorted = [...subjects].sort((a: any, b: any) => b.created_at - a.created_at)
            console.log(`🔄 Returning sorted subjects:`, sorted.map(s => ({ id: s.id, name: s.name })))
            return sorted
          }
          console.log(`📤 Returning unsorted subjects:`, subjects.map(s => ({ id: s.id, name: s.name })))
          return [...subjects]
        }

        return []
      }
    }

    return mockStatement
  }

  // 模拟exec方法
  exec(sql: string) {
    console.log(`Mock exec: ${sql}`)
    return this
  }

  // 模拟close方法
  close() {
    console.log('Mock database closed')
    this.data.clear()
  }
}

// 创建mock数据库实例
export const mockDb = new MockDatabase()
