import { test, expect } from '@playwright/test'

test.describe('基础功能测试', () => {
  test('应该能够访问首页', async ({ page }) => {
    await page.goto('/')
    await expect(page).toHaveTitle(/期末复习平台/)
  })

  test('应该能够看到学科列表', async ({ page }) => {
    await page.goto('/')

    // 等待页面加载
    await page.waitForLoadState('networkidle')

    // 等待API响应
    await page.waitForResponse(response =>
      response.url().includes('/api/subjects') && response.status() === 200
    )

    // 等待一下让Vue渲染完成
    await page.waitForTimeout(2000)

    // 调试：打印页面内容
    const pageContent = await page.content()
    console.log('页面内容:', pageContent.substring(0, 1000))

    // 尝试不同的选择器
    const antCards = page.locator('.ant-card')
    const subjectItems = page.locator('[data-testid="subject-item"]')
    const anyCards = page.locator('div[class*="card"]')

    console.log('ant-card 数量:', await antCards.count())
    console.log('subject-item 数量:', await subjectItems.count())
    console.log('任何包含card的div数量:', await anyCards.count())

    // 检查是否有学科卡片 - 使用更宽泛的选择器
    const subjectCards = page.locator('.ant-card, .subject-card, [data-testid="subject-item"]')
    await expect(subjectCards).toHaveCount(3)
  })
})
