# API框架技术文档

## 概述

期末复习平台后端API基于Node.js + Koa + TypeScript构建，提供RESTful API服务，支持学科管理、文件管理和用户浏览功能。

## 技术栈

- **运行时**: Node.js 18+
- **框架**: Koa 2.x
- **语言**: TypeScript 5.x
- **数据库**: SQLite 3.x + better-sqlite3
- **包管理**: npm

## 项目结构

```
src/
├── app.ts                 # 应用入口文件
├── config/
│   └── index.ts          # 环境配置管理
├── middleware/
│   ├── index.ts          # 中间件注册
│   ├── error.ts          # 错误处理中间件
│   ├── logger.ts         # 日志中间件
│   └── rateLimit.ts      # 限流中间件
├── routes/
│   ├── index.ts          # 主路由
│   ├── subjects.ts       # 学科路由
│   └── files.ts          # 文件路由
├── utils/
│   └── response.ts       # 统一响应格式
├── types/
│   ├── index.ts          # 类型定义
│   └── koa-extensions.ts # Koa扩展类型
└── database/             # 数据库模块（任务1完成）
```

## 核心功能

### 1. 中间件系统

#### 错误处理中间件
- 全局错误捕获和处理
- 标准化错误响应格式
- 开发/生产环境错误信息控制
- 特定错误类型识别和处理

#### 日志中间件
- 请求/响应日志记录
- 彩色控制台输出（开发环境）
- JSON格式日志支持（生产环境）
- 性能监控和响应时间统计

#### 限流中间件
- 基于IP的请求频率限制
- 内存存储（可扩展为Redis）
- 自动清理过期记录
- 标准HTTP限流响应头

#### CORS中间件
- 跨域请求支持
- 可配置的源地址白名单
- 预检请求处理
- 凭证传递支持

### 2. 路由系统

#### 主路由 (`/api`)
- `/health` - 健康检查端点
- `/info` - API信息端点
- `/subjects` - 学科相关路由
- `/files` - 文件相关路由

#### 学科路由 (`/api/subjects`)
- `GET /` - 获取所有学科列表
- `GET /:id` - 获取单个学科详情
- `GET /:id/files` - 获取学科文件结构
- `POST /` - 创建学科（P1功能）
- `PUT /:id` - 更新学科（P1功能）
- `DELETE /:id` - 删除学科（P1功能）

#### 文件路由 (`/api/files`)
- `GET /:id` - 获取文件内容
- `GET /:id/meta` - 获取文件元信息
- `GET /search` - 搜索文件
- `GET /:id/download` - 下载文件
- `POST /` - 上传文件（P0功能）
- `PUT /:id` - 更新文件（P1功能）
- `DELETE /:id` - 删除文件（P1功能）

### 3. 响应格式

#### 成功响应
```json
{
  "success": true,
  "data": {...},
  "timestamp": "2025-01-27T00:00:00.000Z",
  "requestId": "req_1234567890_abc123"
}
```

#### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {...}
  },
  "timestamp": "2025-01-27T00:00:00.000Z",
  "requestId": "req_1234567890_abc123"
}
```

#### 分页响应
```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 100,
      "totalPages": 5,
      "hasNext": true,
      "hasPrev": false
    }
  },
  "timestamp": "2025-01-27T00:00:00.000Z",
  "requestId": "req_1234567890_abc123"
}
```

### 4. 错误处理

#### 错误代码
- `INTERNAL_ERROR` - 服务器内部错误
- `INVALID_REQUEST` - 请求参数错误
- `VALIDATION_ERROR` - 数据验证失败
- `UNAUTHORIZED` - 未授权访问
- `FORBIDDEN` - 禁止访问
- `NOT_FOUND` - 资源不存在
- `RESOURCE_EXISTS` - 资源已存在
- `DATABASE_ERROR` - 数据库错误
- `CONSTRAINT_VIOLATION` - 约束违反
- `BUSINESS_ERROR` - 业务逻辑错误

#### HTTP状态码映射
- 200: 成功
- 400: 客户端错误
- 401: 未授权
- 403: 禁止访问
- 404: 资源不存在
- 409: 资源冲突
- 422: 业务逻辑错误
- 429: 请求过于频繁
- 500: 服务器内部错误
- 502: 外部服务错误

### 5. 配置管理

#### 环境变量
- `NODE_ENV` - 运行环境
- `PORT` - 服务端口
- `HOST` - 监听地址
- `DATABASE_PATH` - 数据库路径
- `LOG_LEVEL` - 日志级别
- `CORS_ORIGIN` - CORS源地址

#### 配置文件
- 默认配置 + 环境变量覆盖
- 类型安全的配置接口
- 配置验证和错误提示
- 开发环境配置打印

## 开发指南

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
npm start
```

### 运行测试
```bash
npm test
```

### 代码检查
```bash
npm run lint
```

### 数据库迁移
```bash
npm run migrate
```

## API测试

### 健康检查
```bash
curl http://localhost:3000/api/health
```

### 获取API信息
```bash
curl http://localhost:3000/api/info
```

### 获取学科列表
```bash
curl http://localhost:3000/api/subjects
```

### 获取文件内容
```bash
curl http://localhost:3000/api/files/1
```

## 性能优化

### 中间件顺序
1. 错误处理（最先）
2. 日志记录
3. CORS处理
4. 限流控制
5. 请求体解析
6. 响应工具
7. 时间统计（最后）

### 内存管理
- 限流记录自动清理
- 数据库连接池管理
- 请求体大小限制
- 响应压缩（可选）

### 安全措施
- 请求频率限制
- 输入参数验证
- SQL注入防护
- XSS防护
- CSRF防护（可选）

## 扩展计划

### P1阶段
- 用户认证和授权
- 管理员功能
- 文件上传功能
- 权限控制

### P2阶段
- 缓存系统
- 搜索优化
- 性能监控
- 日志聚合

## 故障排除

### 常见问题
1. **端口占用**: 修改PORT环境变量
2. **数据库连接失败**: 检查DATABASE_PATH配置
3. **CORS错误**: 检查CORS_ORIGIN配置
4. **限流触发**: 检查请求频率或调整限流配置

### 调试技巧
1. 开启详细日志: `LOG_LEVEL=debug`
2. 查看请求ID: 响应头中的`X-Request-Id`
3. 检查响应时间: 响应头中的`X-Response-Time`
4. 错误堆栈: 开发环境下的`X-Error-Stack`响应头

## 版本历史

- v1.0.0: 初始版本，基础API框架搭建完成