# 期末复习平台

一个专注于学术资料分享的Web应用平台，支持Markdown文件在线渲染和文件夹结构保留。

## ✨ 核心功能

### 📚 学科管理
- 创建和管理学科分类
- 支持学科的增删改查操作
- 学科文件统计和概览

### 📁 文件浏览 🆕
- **层级浏览**: 支持文件夹层级结构浏览
- **智能搜索**: 文件名关键词搜索，支持类型过滤
- **面包屑导航**: 清晰的路径导航，快速定位
- **分页加载**: 大量文件的高效分页显示
- **性能优化**: 内存缓存机制，响应时间<500ms

### 🚀 性能特性
- **缓存系统**: 智能TTL缓存，命中率>80%
- **数据库优化**: CTE查询优化，复合索引加速
- **实时监控**: 性能监控和慢查询检测
- **并发支持**: 支持多用户并发访问

## 🚀 快速开始

### 环境要求

- Node.js >= 20.0.0
- npm >= 10.0.0

### 安装依赖

```bash
# 安装所有依赖（根目录、前端、后端）
npm run install:all
```

### 初始化数据库

```bash
# 创建SQLite数据库和表结构
npm run db:init
```

### 开发模式

```bash
# 同时启动前端和后端开发服务器
npm run dev

# 或者分别启动
npm run dev:backend   # 后端服务器：http://localhost:3000
npm run dev:frontend  # 前端服务器：http://localhost:5173
```

### 生产构建

```bash
# 构建前端和后端
npm run build

# 启动生产服务器
npm start
```

## 📁 项目结构

```
Term Review/
├── frontend/              # Vue3 前端应用
│   ├── src/
│   │   ├── components/    # Vue组件
│   │   ├── views/        # 页面视图
│   │   ├── stores/       # Pinia状态管理
│   │   ├── api/          # API调用
│   │   ├── types/        # TypeScript类型
│   │   └── utils/        # 工具函数
│   └── package.json
├── backend/               # Node.js 后端服务
│   ├── src/
│   │   ├── controllers/   # 控制器
│   │   ├── services/     # 业务逻辑
│   │   ├── models/       # 数据模型
│   │   ├── routes/       # 路由定义
│   │   ├── middleware/   # 中间件
│   │   ├── database/     # 数据库配置
│   │   └── types/        # TypeScript类型
│   └── package.json
├── storage/               # 文件存储
│   ├── subjects/         # 学科文件存储
│   ├── temp/            # 临时文件
│   └── cache/           # 缓存文件
├── docs/                 # 项目文档
└── package.json          # 根目录配置
```

## 🛠️ 技术栈

### 前端
- **Vue 3** - 渐进式JavaScript框架
- **TypeScript** - 静态类型检查
- **Vite** - 快速构建工具
- **Ant Design Vue** - UI组件库
- **UnoCSS** - 原子化CSS框架
- **Pinia** - 状态管理
- **Vue Router** - 前端路由

### 后端
- **Node.js** - JavaScript运行环境
- **Koa2** - Web框架
- **TypeScript** - 静态类型检查
- **SQLite** - 嵌入式数据库
- **better-sqlite3** - SQLite驱动
- **Joi** - 数据验证

## 📝 开发指南

### 代码规范

项目使用ESLint和Prettier进行代码规范检查：

```bash
# 检查代码规范
npm run lint

# 自动修复代码格式问题
npm run lint:backend
npm run lint:frontend
```

### API文档

后端API遵循RESTful设计：

#### 学科管理
- `GET /api/health` - 健康检查
- `POST /api/subjects` - 创建学科
- `GET /api/subjects` - 获取学科列表
- `GET /api/subjects/:id` - 获取学科详情
- `PUT /api/subjects/:id` - 更新学科
- `DELETE /api/subjects/:id` - 删除学科

#### 文件浏览 🆕
- `GET /api/subjects/:id/files` - 获取文件列表（支持分页、搜索）
- `GET /api/files/:nodeId/breadcrumb` - 获取面包屑导航路径
- `GET /api/subjects/:id/search` - 搜索文件（支持类型过滤、分页）

详细API文档请查看：[API Reference](docs/architecture/API_Reference.md)

## 🧪 测试

```bash
# 运行后端测试
cd backend && npm test

# 运行前端测试（计划中）
cd frontend && npm test
```

## 📦 部署

### Docker部署（计划中）

```bash
# 构建Docker镜像
docker build -t term-review .

# 运行容器
docker run -p 3000:3000 term-review
```

## 🤝 贡献指南

1. Fork本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看[LICENSE](LICENSE)文件了解详情

## 📞 联系我们

如有问题或建议，请创建Issue或联系项目维护者。

---

**期末复习平台** - 让学习资料分享更简单 ✨