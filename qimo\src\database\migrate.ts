/**
 * 数据库迁移管理模块
 * 负责执行数据库结构迁移和版本管理
 */

import fs from 'fs';
import path from 'path';
import { dbConnection } from './connection';

// 迁移记录接口
interface MigrationRecord {
  id: number;
  filename: string;
  executed_at: string;
  checksum: string;
}

// 迁移管理类
class MigrationManager {
  private migrationsDir: string;

  constructor(migrationsDir?: string) {
    this.migrationsDir = migrationsDir || path.join(__dirname, 'migrations');
  }

  /**
   * 初始化迁移表
   */
  private initializeMigrationTable(): void {
    const db = dbConnection.getDatabase();
    
    // 创建迁移记录表
    db.exec(`
      CREATE TABLE IF NOT EXISTS schema_migrations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        filename TEXT NOT NULL UNIQUE,
        executed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        checksum TEXT NOT NULL
      );
      
      CREATE INDEX IF NOT EXISTS idx_schema_migrations_filename 
      ON schema_migrations(filename);
    `);
  }

  /**
   * 计算文件校验和
   */
  private calculateChecksum(content: string): string {
    const crypto = require('crypto');
    return crypto.createHash('md5').update(content).digest('hex');
  }

  /**
   * 获取所有迁移文件
   */
  private getMigrationFiles(): string[] {
    if (!fs.existsSync(this.migrationsDir)) {
      console.warn(`⚠️ 迁移目录不存在: ${this.migrationsDir}`);
      return [];
    }

    return fs.readdirSync(this.migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort(); // 按文件名排序确保执行顺序
  }

  /**
   * 获取已执行的迁移记录
   */
  private getExecutedMigrations(): MigrationRecord[] {
    const db = dbConnection.getDatabase();
    const stmt = db.prepare('SELECT * FROM schema_migrations ORDER BY filename');
    return stmt.all() as MigrationRecord[];
  }

  /**
   * 记录迁移执行
   */
  private recordMigration(db: any, filename: string, checksum: string): void {
    const stmt = db.prepare(`
      INSERT INTO schema_migrations (filename, checksum) 
      VALUES (?, ?)
    `);
    stmt.run(filename, checksum);
  }

  /**
   * 执行单个迁移文件
   */
  private executeMigration(filename: string): void {
    const filePath = path.join(this.migrationsDir, filename);
    const content = fs.readFileSync(filePath, 'utf-8');
    const checksum = this.calculateChecksum(content);

    console.log(`🔄 执行迁移: ${filename}`);

    try {
      const db = dbConnection.getDatabase();
      
      // 在事务中执行迁移
      dbConnection.transaction((db) => {
        // 执行SQL语句
        db.exec(content);
        
        // 记录迁移
        this.recordMigration(db, filename, checksum);
      });

      console.log(`✅ 迁移完成: ${filename}`);
    } catch (error) {
      console.error(`❌ 迁移失败: ${filename}`, error);
      throw error;
    }
  }

  /**
   * 验证迁移文件完整性
   */
  private validateMigrations(): void {
    const migrationFiles = this.getMigrationFiles();
    const executedMigrations = this.getExecutedMigrations();

    // 检查已执行的迁移文件是否仍然存在
    for (const executed of executedMigrations) {
      if (!migrationFiles.includes(executed.filename)) {
        console.warn(`⚠️ 已执行的迁移文件不存在: ${executed.filename}`);
      } else {
        // 验证文件内容是否被修改
        const filePath = path.join(this.migrationsDir, executed.filename);
        const content = fs.readFileSync(filePath, 'utf-8');
        const currentChecksum = this.calculateChecksum(content);
        
        if (currentChecksum !== executed.checksum) {
          throw new Error(`迁移文件已被修改: ${executed.filename}`);
        }
      }
    }
  }

  /**
   * 执行所有待执行的迁移
   */
  public async runMigrations(): Promise<void> {
    try {
      // 确保数据库连接已初始化
      await dbConnection.initialize();

      // 初始化迁移表
      this.initializeMigrationTable();

      // 验证现有迁移
      this.validateMigrations();

      // 获取迁移文件和已执行记录
      const migrationFiles = this.getMigrationFiles();
      const executedMigrations = this.getExecutedMigrations();
      const executedFilenames = new Set(executedMigrations.map(m => m.filename));

      // 找出待执行的迁移
      const pendingMigrations = migrationFiles.filter(
        filename => !executedFilenames.has(filename)
      );

      if (pendingMigrations.length === 0) {
        console.log('✅ 所有迁移都已执行，数据库结构是最新的');
        return;
      }

      console.log(`🚀 发现 ${pendingMigrations.length} 个待执行的迁移`);

      // 执行待执行的迁移
      for (const filename of pendingMigrations) {
        this.executeMigration(filename);
      }

      console.log('🎉 所有迁移执行完成');
    } catch (error) {
      console.error('❌ 迁移执行失败:', error);
      throw error;
    }
  }

  /**
   * 获取迁移状态
   */
  public getMigrationStatus(): {
    total: number;
    executed: number;
    pending: string[];
    lastMigration?: MigrationRecord;
  } {
    const migrationFiles = this.getMigrationFiles();
    const executedMigrations = this.getExecutedMigrations();
    const executedFilenames = new Set(executedMigrations.map(m => m.filename));
    
    const pendingMigrations = migrationFiles.filter(
      filename => !executedFilenames.has(filename)
    );

    return {
      total: migrationFiles.length,
      executed: executedMigrations.length,
      pending: pendingMigrations,
      lastMigration: executedMigrations[executedMigrations.length - 1]
    };
  }
}

// 创建迁移管理器实例
const migrationManager = new MigrationManager();

// 导出迁移管理器
export { MigrationManager, migrationManager };

// 如果直接运行此文件，执行迁移
if (require.main === module) {
  migrationManager.runMigrations()
    .then(() => {
      console.log('迁移完成，程序退出');
      process.exit(0);
    })
    .catch((error) => {
      console.error('迁移失败:', error);
      process.exit(1);
    });
}