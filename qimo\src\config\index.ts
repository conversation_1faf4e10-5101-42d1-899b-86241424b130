/**
 * 环境配置管理模块
 * 统一管理应用的所有配置项
 */

import path from 'path';

// 配置接口定义
export interface AppConfig {
  // 应用基础配置
  app: {
    name: string;
    version: string;
    port: number;
    env: 'development' | 'production' | 'test';
    host: string;
  };

  // 数据库配置
  database: {
    path: string;
    timeout: number;
    verbose: boolean;
  };

  // 日志配置
  logging: {
    level: 'debug' | 'info' | 'warn' | 'error';
    format: 'json' | 'simple';
  };

  // 安全配置
  security: {
    cors: {
      origin: string | string[];
      credentials: boolean;
    };
    rateLimit: {
      windowMs: number;
      max: number;
    };
  };

  // 文件上传配置（预留）
  upload: {
    maxSize: number;
    maxFiles: number;
    allowedTypes: string[];
  };

  // 管理后台配置
  admin: {
    enabled: boolean;
    panelPath: string;
    accessHeader: string;
  };
}

// 默认配置
const defaultConfig: AppConfig = {
  app: {
    name: 'final-review-platform',
    version: '1.0.0',
    port: 3000,
    env: 'development',
    host: '0.0.0.0'
  },

  database: {
    path: path.join(process.cwd(), 'data', 'platform.db'),
    timeout: 10000,
    verbose: false
  },

  logging: {
    level: 'info',
    format: 'simple'
  },

  security: {
    cors: {
      origin: ['http://localhost:3000', 'http://localhost:5173'],
      credentials: true
    },
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 1000 // 每个IP最多1000次请求
    }
  },

  upload: {
    maxSize: 50 * 1024 * 1024, // 50MB
    maxFiles: 1000,
    allowedTypes: ['.md', '.txt', '.pdf', '.doc', '.docx', '.jpg', '.png', '.gif']
  },

  admin: {
    enabled: true,
    panelPath: '/admin-panel-abcdef',
    accessHeader: 'x-admin-path'
  }
};

// 从环境变量加载配置
function loadConfigFromEnv(): Partial<AppConfig> {
  return {
    app: {
      name: process.env.APP_NAME || defaultConfig.app.name,
      version: process.env.APP_VERSION || defaultConfig.app.version,
      port: parseInt(process.env.PORT || String(defaultConfig.app.port), 10),
      env: (process.env.NODE_ENV as any) || defaultConfig.app.env,
      host: process.env.HOST || defaultConfig.app.host
    },

    database: {
      path: process.env.DATABASE_PATH || defaultConfig.database.path,
      timeout: parseInt(process.env.DATABASE_TIMEOUT || String(defaultConfig.database.timeout), 10),
      verbose: process.env.DEV_VERBOSE_SQL === 'true' || defaultConfig.database.verbose
    },

    logging: {
      level: (process.env.LOG_LEVEL as any) || defaultConfig.logging.level,
      format: (process.env.LOG_FORMAT as any) || defaultConfig.logging.format
    },

    upload: {
      maxSize: parseInt(process.env.UPLOAD_MAX_SIZE || String(defaultConfig.upload.maxSize), 10),
      maxFiles: parseInt(process.env.UPLOAD_MAX_FILES || String(defaultConfig.upload.maxFiles), 10),
      allowedTypes: process.env.UPLOAD_ALLOWED_TYPES?.split(',') || defaultConfig.upload.allowedTypes
    },

    admin: {
      enabled: process.env.ADMIN_PANEL_ENABLED !== 'false',
      panelPath: process.env.ADMIN_PANEL_PATH || defaultConfig.admin.panelPath,
      accessHeader: process.env.ADMIN_ACCESS_HEADER || defaultConfig.admin.accessHeader
    }
  };
}

// 合并配置
function mergeConfig(base: AppConfig, override: Partial<AppConfig>): AppConfig {
  return {
    app: { ...base.app, ...override.app },
    database: { ...base.database, ...override.database },
    logging: { ...base.logging, ...override.logging },
    security: {
      cors: { ...base.security.cors, ...override.security?.cors },
      rateLimit: { ...base.security.rateLimit, ...override.security?.rateLimit }
    },
    upload: { ...base.upload, ...override.upload },
    admin: { ...base.admin, ...override.admin }
  };
}

// 验证配置
function validateConfig(config: AppConfig): void {
  // 验证端口号
  if (config.app.port < 1 || config.app.port > 65535) {
    throw new Error(`无效的端口号: ${config.app.port}`);
  }

  // 验证环境
  if (!['development', 'production', 'test'].includes(config.app.env)) {
    throw new Error(`无效的环境: ${config.app.env}`);
  }

  // 验证日志级别
  if (!['debug', 'info', 'warn', 'error'].includes(config.logging.level)) {
    throw new Error(`无效的日志级别: ${config.logging.level}`);
  }

  // 验证数据库路径
  if (!config.database.path) {
    throw new Error('数据库路径不能为空');
  }
}

// 创建最终配置
const envConfig = loadConfigFromEnv();
const finalConfig = mergeConfig(defaultConfig, envConfig);

// 验证配置
validateConfig(finalConfig);

// 导出配置
export const config = finalConfig;

// 便捷访问器
export const isDevelopment = config.app.env === 'development';
export const isProduction = config.app.env === 'production';
export const isTest = config.app.env === 'test';

// 配置信息打印（开发环境）
if (isDevelopment) {
  console.log('🔧 应用配置加载完成:');
  console.log(`   - 环境: ${config.app.env}`);
  console.log(`   - 端口: ${config.app.port}`);
  console.log(`   - 数据库: ${config.database.path}`);
  console.log(`   - 日志级别: ${config.logging.level}`);
}