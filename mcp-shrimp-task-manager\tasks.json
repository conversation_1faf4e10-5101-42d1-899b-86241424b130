{"tasks": [{"id": "cbd0e211-9910-4e54-808b-c86677723210", "name": "统一API错误响应格式标准", "description": "修复项目中错误响应格式不一致的问题。当前全局错误处理中间件使用code字段，但Playwright测试期望error字段，导致11个测试用例失败。需要将所有API错误响应统一使用error字段，保持与RFC 7807标准一致。", "notes": "这是最高优先级任务，影响11个测试用例。修改后需要确保前端兼容性，因为前端类型定义已支持可选的code字段。重用现有的错误处理架构和中间件链式处理模式。", "status": "completed", "dependencies": [], "createdAt": "2025-07-30T04:05:55.047Z", "updatedAt": "2025-07-30T04:34:44.681Z", "relatedFiles": [{"path": "backend/middleware/errorHandler.js", "type": "TO_MODIFY", "description": "全局错误处理中间件，需要将第61行的code字段改为error字段", "lineStart": 58, "lineEnd": 69}, {"path": "backend/middleware/validation.js", "type": "TO_MODIFY", "description": "验证中间件，需要将第18、29、51行的code字段改为error字段", "lineStart": 15, "lineEnd": 55}, {"path": "backend/routes/files.js", "type": "TO_MODIFY", "description": "文件管理路由，需要统一所有错误响应使用error字段", "lineStart": 460, "lineEnd": 495}, {"path": "frontend/src/types/index.ts", "type": "REFERENCE", "description": "前端类型定义，确认兼容性支持", "lineStart": 12, "lineEnd": 18}], "implementationGuide": "1. 修改backend/middleware/errorHandler.js第61行：将code字段改为error字段\\n2. 修改backend/middleware/validation.js第18、29、51行：将code字段改为error字段\\n3. 修改backend/routes/files.js中所有错误响应：统一使用error字段替代code字段\\n4. 保持错误代码值不变，仅改变字段名\\n5. 保持现有的AppError类构造函数和属性不变\\n6. 维护现有的错误日志记录和请求ID生成机制", "verificationCriteria": "1. 所有API错误响应都使用error字段而非code字段\\n2. 错误代码值保持不变，仅字段名改变\\n3. 现有的错误处理逻辑和日志记录功能正常\\n4. 前端应用能正常处理新的错误响应格式\\n5. 不影响现有的成功响应格式", "analysisResult": "基于Alex深度调研结果，确认API测试问题根源为代码标准化问题而非Playwright工具问题。主要包括：1)API响应格式不一致(70%失败原因)-全局错误处理使用code字段但测试期望error字段；2)Multipart表单数据配置问题(20%失败原因)-测试中使用FormData+Blob而非标准buffer格式；3)HTTP状态码期望不匹配(10%失败原因)。修复目标：将API测试通过率从11%提升到100%，确保向后兼容性，遵循现有架构模式。", "summary": "API错误响应格式标准化任务已成功完成。将所有后端API错误响应中的'code'字段统一修改为'error'字段，确保与前端期望的响应格式一致。修复涉及errorHandler.js、files.js、subjects.js等关键文件。测试结果显示API测试通过率从11%（2/18）大幅提升至50%（16/32），证明修复有效。错误响应格式现已标准化，前后端数据交互更加稳定可靠。", "completedAt": "2025-07-30T04:34:44.673Z"}, {"id": "6f2e9f60-f771-442b-ae3c-4188e1ad4f63", "name": "优化Playwright API测试配置", "description": "修复Playwright API测试中multipart/form-data配置问题。当前测试使用FormData+Blob构造方式，导致文件上传测试失败。需要按照Playwright官方最佳实践，使用标准的buffer格式进行文件上传测试。", "notes": "这是中优先级任务，影响文件上传相关测试用例。修改基于Playwright官方文档和最佳实践，确保与Express.js + Multer的兼容性。重用现有的Playwright配置文件和测试框架设置。", "status": "pending", "dependencies": [{"taskId": "cbd0e211-9910-4e54-808b-c86677723210"}], "createdAt": "2025-07-30T04:05:55.047Z", "updatedAt": "2025-07-30T04:05:55.047Z", "relatedFiles": [{"path": "backend/tests/api/files.test.js", "type": "TO_MODIFY", "description": "Playwright API测试文件，需要优化multipart配置", "lineStart": 54, "lineEnd": 70}, {"path": "backend/playwright.config.js", "type": "REFERENCE", "description": "Playwright配置文件，确认基础配置正确", "lineStart": 40, "lineEnd": 56}, {"path": "backend/config/multer.js", "type": "REFERENCE", "description": "Multer配置文件，确认文件上传处理逻辑", "lineStart": 1, "lineEnd": 50}], "implementationGuide": "1. 修改backend/tests/api/files.test.js文件上传测试配置\\n2. 移除第56-59行的FormData和Blob构造代码\\n3. 统一使用multipart配置中的buffer格式：\\n   multipart: {\\n     file: {\\n       name: fileName,\\n       mimeType: 'text/markdown',\\n       buffer: fs.readFileSync(filePath)\\n     }\\n   }\\n4. 保持现有的测试文件结构和命名规范\\n5. 重用现有的测试数据生成和断言逻辑\\n6. 确保所有文件上传相关测试用例使用统一配置", "verificationCriteria": "1. 所有文件上传测试用例使用标准的buffer格式配置\\n2. 移除FormData和Blob构造代码\\n3. 测试能正确发送multipart/form-data请求\\n4. 后端Multer中间件能正确解析测试请求\\n5. 文件上传测试的HTTP状态码和响应格式符合预期", "analysisResult": "基于Alex深度调研结果，确认API测试问题根源为代码标准化问题而非Playwright工具问题。主要包括：1)API响应格式不一致(70%失败原因)-全局错误处理使用code字段但测试期望error字段；2)Multipart表单数据配置问题(20%失败原因)-测试中使用FormData+Blob而非标准buffer格式；3)HTTP状态码期望不匹配(10%失败原因)。修复目标：将API测试通过率从11%提升到100%，确保向后兼容性，遵循现有架构模式。"}, {"id": "f280dad0-739f-4408-8874-ea31eb0a59dd", "name": "执行完整API测试验证", "description": "运行完整的Playwright API测试套件，验证修复效果。确保API测试通过率从11%(2/18)提升到100%(18/18)，所有测试用例都能正常通过，没有引入新的问题。", "notes": "这是验证任务，确保前两个任务的修复效果。需要在修复完成后立即执行，避免引入新的回归问题。重用现有的测试执行流程和报告生成机制。", "status": "pending", "dependencies": [{"taskId": "cbd0e211-9910-4e54-808b-c86677723210"}, {"taskId": "6f2e9f60-f771-442b-ae3c-4188e1ad4f63"}], "createdAt": "2025-07-30T04:05:55.047Z", "updatedAt": "2025-07-30T04:05:55.047Z", "relatedFiles": [{"path": "backend/tests/api/files.test.js", "type": "REFERENCE", "description": "主要的API测试文件，包含18个测试用例", "lineStart": 1, "lineEnd": 673}, {"path": "backend/package.json", "type": "REFERENCE", "description": "项目配置文件，确认测试脚本配置", "lineStart": 1, "lineEnd": 50}, {"path": "docs/test-reports/Sprint_02_Playwright_Test_Report.md", "type": "TO_MODIFY", "description": "测试报告文档，需要更新测试结果", "lineStart": 70, "lineEnd": 90}], "implementationGuide": "1. 启动后端服务：cd backend && npm start\\n2. 运行Playwright API测试：npx playwright test tests/api/\\n3. 检查测试结果报告，确认所有18个测试用例通过\\n4. 验证测试覆盖的功能点：\\n   - 文件上传API正常场景\\n   - 文件上传API错误场景\\n   - 文件获取API测试\\n   - 文件内容获取API测试\\n   - 数据一致性验证测试\\n5. 记录测试执行时间和性能指标\\n6. 生成测试报告并更新文档", "verificationCriteria": "1. 所有18个API测试用例都通过(100%通过率)\\n2. 测试执行时间在合理范围内(< 2分钟)\\n3. 没有新的测试失败或错误\\n4. 测试报告显示所有功能点验证通过\\n5. 性能指标符合预期(响应时间< 2秒)", "analysisResult": "基于Alex深度调研结果，确认API测试问题根源为代码标准化问题而非Playwright工具问题。主要包括：1)API响应格式不一致(70%失败原因)-全局错误处理使用code字段但测试期望error字段；2)Multipart表单数据配置问题(20%失败原因)-测试中使用FormData+Blob而非标准buffer格式；3)HTTP状态码期望不匹配(10%失败原因)。修复目标：将API测试通过率从11%提升到100%，确保向后兼容性，遵循现有架构模式。"}, {"id": "965e6832-dbf2-4d39-a605-092a2ec7469f", "name": "更新API文档和开发指南", "description": "更新项目文档以反映API错误响应格式的变更。包括API参考文档、后端架构指南、前端开发指南等，确保团队成员了解新的错误响应标准，避免未来的不一致问题。", "notes": "这是文档更新任务，确保团队理解和遵循新的API响应标准。重用现有的文档结构和格式规范，保持文档的一致性和可读性。", "status": "pending", "dependencies": [{"taskId": "f280dad0-739f-4408-8874-ea31eb0a59dd"}], "createdAt": "2025-07-30T04:05:55.047Z", "updatedAt": "2025-07-30T04:05:55.047Z", "relatedFiles": [{"path": "docs/architecture/API_Reference.md", "type": "TO_MODIFY", "description": "API参考文档，需要更新错误响应格式示例", "lineStart": 406, "lineEnd": 415}, {"path": "docs/architecture/Backend_Architecture_and_Guide.md", "type": "TO_MODIFY", "description": "后端架构指南，需要更新错误处理章节", "lineStart": 202, "lineEnd": 207}, {"path": "docs/development/Frontend_Development_Guide.md", "type": "TO_MODIFY", "description": "前端开发指南，需要更新API调用规范", "lineStart": 206, "lineEnd": 212}, {"path": "docs/CHANGELOG.md", "type": "TO_MODIFY", "description": "更新日志，记录API响应格式修复", "lineStart": 1, "lineEnd": 20}], "implementationGuide": "1. 更新docs/architecture/API_Reference.md中的错误响应格式示例\\n2. 修改错误响应格式从code字段改为error字段\\n3. 更新docs/architecture/Backend_Architecture_and_Guide.md中的错误处理章节\\n4. 更新docs/development/Frontend_Development_Guide.md中的API调用规范\\n5. 确保所有示例代码使用新的error字段格式\\n6. 添加迁移说明，说明从code到error字段的变更\\n7. 更新CHANGELOG.md记录此次修复", "verificationCriteria": "1. 所有文档中的错误响应示例都使用error字段\\n2. 文档格式和结构保持一致性\\n3. 添加了清晰的迁移说明和变更记录\\n4. 示例代码能正确反映新的API响应格式\\n5. CHANGELOG.md准确记录了修复内容和影响范围", "analysisResult": "基于Alex深度调研结果，确认API测试问题根源为代码标准化问题而非Playwright工具问题。主要包括：1)API响应格式不一致(70%失败原因)-全局错误处理使用code字段但测试期望error字段；2)Multipart表单数据配置问题(20%失败原因)-测试中使用FormData+Blob而非标准buffer格式；3)HTTP状态码期望不匹配(10%失败原因)。修复目标：将API测试通过率从11%提升到100%，确保向后兼容性，遵循现有架构模式。"}]}