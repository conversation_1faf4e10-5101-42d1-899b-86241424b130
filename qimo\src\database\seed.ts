/**
 * 数据库种子数据
 * 用于初始化测试数据
 * 
 * <AUTHOR> (Engineer)
 * @date 2025-01-27
 */

import { getDatabase, initDatabase } from './connection';
import { subjectDao } from '../dao/subjectDao';
import { fileDao } from '../dao/fileDao';

export async function seedDatabase() {
  // 初始化数据库连接
  initDatabase();
  const db = getDatabase();
  
  try {
    console.log('🌱 开始初始化种子数据...');
    
    // 清空现有数据（仅在开发环境）
    if (process.env.NODE_ENV === 'development') {
      db.exec('DELETE FROM file_nodes');
      db.exec('DELETE FROM subjects');
      console.log('✅ 清空现有数据');
    }
    
    // 创建学科数据
    const mathSubjectId = subjectDao.create({
      name: '数学',
      description: '高等数学、线性代数、概率论等数学相关课程',
      status: 1,
      sort_order: 1
    });
    
    const csSubjectId = subjectDao.create({
      name: '计算机科学',
      description: '数据结构、算法、操作系统等计算机课程',
      status: 1,
      sort_order: 2
    });
    
    const englishSubjectId = subjectDao.create({
      name: '英语',
      description: '英语语法、词汇、阅读理解等英语学习资料',
      status: 1,
      sort_order: 3
    });
    
    console.log('✅ 创建学科数据完成');
    
    // 创建数学学科的文件结构
    const mathChapter1Id = fileDao.create({
      subject_id: mathSubjectId,
      parent_id: null,
      name: '第一章 函数与极限',
      type: 'folder',
      path: '/第一章 函数与极限',
      size: 0
    });
    
    const mathChapter2Id = fileDao.create({
      subject_id: mathSubjectId,
      parent_id: null,
      name: '第二章 导数与微分',
      type: 'folder',
      path: '/第二章 导数与微分',
      size: 0
    });
    
    // 第一章的文件
    fileDao.create({
      subject_id: mathSubjectId,
      parent_id: mathChapter1Id,
      name: '函数的概念.md',
      type: 'file',
      path: '/第一章 函数与极限/函数的概念.md',
      size: 2048,
      mime_type: 'text/markdown'
    });
    
    fileDao.create({
      subject_id: mathSubjectId,
      parent_id: mathChapter1Id,
      name: '极限的定义.md',
      type: 'file',
      path: '/第一章 函数与极限/极限的定义.md',
      size: 3072,
      mime_type: 'text/markdown'
    });
    
    fileDao.create({
      subject_id: mathSubjectId,
      parent_id: mathChapter1Id,
      name: '练习题.md',
      type: 'file',
      path: '/第一章 函数与极限/练习题.md',
      size: 1536,
      mime_type: 'text/markdown'
    });
    
    // 第二章的文件
    fileDao.create({
      subject_id: mathSubjectId,
      parent_id: mathChapter2Id,
      name: '导数的概念.md',
      type: 'file',
      path: '/第二章 导数与微分/导数的概念.md',
      size: 2560,
      mime_type: 'text/markdown'
    });
    
    fileDao.create({
      subject_id: mathSubjectId,
      parent_id: mathChapter2Id,
      name: '求导法则.md',
      type: 'file',
      path: '/第二章 导数与微分/求导法则.md',
      size: 4096,
      mime_type: 'text/markdown'
    });
    
    console.log('✅ 创建数学学科文件结构完成');
    
    // 创建计算机科学的文件结构
    const csDataStructureId = fileDao.create({
      subject_id: csSubjectId,
      parent_id: null,
      name: '数据结构',
      type: 'folder',
      path: '/数据结构',
      size: 0
    });
    
    const csAlgorithmId = fileDao.create({
      subject_id: csSubjectId,
      parent_id: null,
      name: '算法',
      type: 'folder',
      path: '/算法',
      size: 0
    });
    
    // 数据结构文件
    fileDao.create({
      subject_id: csSubjectId,
      parent_id: csDataStructureId,
      name: '线性表.md',
      type: 'file',
      path: '/数据结构/线性表.md',
      size: 3584,
      mime_type: 'text/markdown'
    });
    
    fileDao.create({
      subject_id: csSubjectId,
      parent_id: csDataStructureId,
      name: '栈和队列.md',
      type: 'file',
      path: '/数据结构/栈和队列.md',
      size: 2816,
      mime_type: 'text/markdown'
    });
    
    fileDao.create({
      subject_id: csSubjectId,
      parent_id: csDataStructureId,
      name: '树和二叉树.md',
      type: 'file',
      path: '/数据结构/树和二叉树.md',
      size: 4608,
      mime_type: 'text/markdown'
    });
    
    // 算法文件
    fileDao.create({
      subject_id: csSubjectId,
      parent_id: csAlgorithmId,
      name: '排序算法.md',
      type: 'file',
      path: '/算法/排序算法.md',
      size: 5120,
      mime_type: 'text/markdown'
    });
    
    fileDao.create({
      subject_id: csSubjectId,
      parent_id: csAlgorithmId,
      name: '查找算法.md',
      type: 'file',
      path: '/算法/查找算法.md',
      size: 3840,
      mime_type: 'text/markdown'
    });
    
    console.log('✅ 创建计算机科学文件结构完成');
    
    // 创建英语学科的文件结构
    const englishGrammarId = fileDao.create({
      subject_id: englishSubjectId,
      parent_id: null,
      name: '语法',
      type: 'folder',
      path: '/语法',
      size: 0
    });
    
    const englishVocabularyId = fileDao.create({
      subject_id: englishSubjectId,
      parent_id: null,
      name: '词汇',
      type: 'folder',
      path: '/词汇',
      size: 0
    });
    
    // 语法文件
    fileDao.create({
      subject_id: englishSubjectId,
      parent_id: englishGrammarId,
      name: '时态.md',
      type: 'file',
      path: '/语法/时态.md',
      size: 2304,
      mime_type: 'text/markdown'
    });
    
    fileDao.create({
      subject_id: englishSubjectId,
      parent_id: englishGrammarId,
      name: '从句.md',
      type: 'file',
      path: '/语法/从句.md',
      size: 3328,
      mime_type: 'text/markdown'
    });
    
    // 词汇文件
    fileDao.create({
      subject_id: englishSubjectId,
      parent_id: englishVocabularyId,
      name: '四级词汇.md',
      type: 'file',
      path: '/词汇/四级词汇.md',
      size: 6144,
      mime_type: 'text/markdown'
    });
    
    fileDao.create({
      subject_id: englishSubjectId,
      parent_id: englishVocabularyId,
      name: '六级词汇.md',
      type: 'file',
      path: '/词汇/六级词汇.md',
      size: 8192,
      mime_type: 'text/markdown'
    });
    
    console.log('✅ 创建英语学科文件结构完成');
    
    console.log('🎉 种子数据初始化完成！');
    console.log(`📊 统计信息:`);
    console.log(`   - 学科数量: 3`);
    console.log(`   - 文件夹数量: 6`);
    console.log(`   - 文件数量: 13`);
    
  } catch (error) {
    console.error('❌ 种子数据初始化失败:', error);
    throw error;
  }
}

// 如果直接运行此文件，则执行种子数据初始化
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('✅ 种子数据初始化成功');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 种子数据初始化失败:', error);
      process.exit(1);
    });
}