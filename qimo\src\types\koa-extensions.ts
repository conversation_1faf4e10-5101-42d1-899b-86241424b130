/**
 * Koa框架扩展类型定义
 * 扩展Koa的Context和State类型，添加自定义属性
 */

import { ResponseHelper } from '../utils/response';

// 扩展Koa Context类型
declare module 'koa' {
  interface Context {
    // 响应工具
    apiResponse: ResponseHelper;
  }
  
  interface DefaultState {
    // 请求ID
    requestId: string;
    
    // 用户信息（预留，P1阶段使用）
    user?: {
      id: number;
      username: string;
      role: 'admin' | 'user';
    };
    
    // 请求开始时间
    startTime?: number;
    
    // 自定义数据
    [key: string]: any;
  }
}

// 中间件函数类型
export type MiddlewareFunction = (ctx: import('koa').Context, next: () => Promise<void>) => Promise<void>;

// 路由处理函数类型
export type RouteHandler = (ctx: import('koa').Context) => Promise<void>;

// 错误处理函数类型
export type ErrorHandler = (error: Error, ctx: import('koa').Context) => void;