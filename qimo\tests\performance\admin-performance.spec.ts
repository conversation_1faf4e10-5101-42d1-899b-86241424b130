import { test, expect } from '@playwright/test'

/**
 * 管理功能性能测试
 * 验证页面加载时间和操作响应时间符合要求
 */

const ADMIN_URL = 'http://localhost:5173/admin-panel-abcdef'
const PERFORMANCE_THRESHOLDS = {
  PAGE_LOAD: 3000,      // 页面加载时间 < 3秒
  API_RESPONSE: 1000,   // API响应时间 < 1秒
  SEARCH_RESPONSE: 500, // 搜索响应时间 < 500ms
  OPERATION: 2000       // 操作响应时间 < 2秒
}

test.describe('管理功能性能测试', () => {
  test.describe('页面加载性能', () => {
    test('管理后台首页加载性能', async ({ page }) => {
      const startTime = Date.now()
      
      await page.goto(ADMIN_URL)
      await page.waitForLoadState('networkidle')
      
      const loadTime = Date.now() - startTime
      
      console.log(`Admin dashboard load time: ${loadTime}ms`)
      expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.PAGE_LOAD)
      
      // 验证关键元素已加载
      await expect(page.locator('.admin-layout')).toBeVisible()
      await expect(page.locator('.admin-sider')).toBeVisible()
      await expect(page.locator('.admin-header')).toBeVisible()
    })

    test('学科管理页面加载性能', async ({ page }) => {
      const startTime = Date.now()
      
      await page.goto(`${ADMIN_URL}/subjects`)
      await page.waitForLoadState('networkidle')
      
      const loadTime = Date.now() - startTime
      
      console.log(`Subject management page load time: ${loadTime}ms`)
      expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.PAGE_LOAD)
      
      // 验证关键元素已加载
      await expect(page.locator('h1:has-text("学科管理")')).toBeVisible()
      await expect(page.locator('.ant-table')).toBeVisible()
    })

    test('页面资源加载性能', async ({ page }) => {
      // 监听网络请求
      const requests: any[] = []
      page.on('request', request => {
        requests.push({
          url: request.url(),
          method: request.method(),
          startTime: Date.now()
        })
      })

      const responses: any[] = []
      page.on('response', response => {
        responses.push({
          url: response.url(),
          status: response.status(),
          endTime: Date.now()
        })
      })

      await page.goto(`${ADMIN_URL}/subjects`)
      await page.waitForLoadState('networkidle')

      // 分析关键资源加载时间
      const apiRequests = responses.filter(r => r.url.includes('/api/'))
      const staticRequests = responses.filter(r => 
        r.url.includes('.js') || r.url.includes('.css') || r.url.includes('.ico')
      )

      console.log(`API requests: ${apiRequests.length}`)
      console.log(`Static resource requests: ${staticRequests.length}`)

      // 验证没有失败的请求
      const failedRequests = responses.filter(r => r.status >= 400)
      expect(failedRequests.length).toBe(0)
    })
  })

  test.describe('API响应性能', () => {
    test('获取学科列表API性能', async ({ page }) => {
      await page.goto(`${ADMIN_URL}/subjects`)
      
      // 监听API请求
      const apiResponsePromise = page.waitForResponse(response => 
        response.url().includes('/api/admin/subjects') && 
        response.request().method() === 'GET'
      )

      const startTime = Date.now()
      await page.reload()
      
      const response = await apiResponsePromise
      const responseTime = Date.now() - startTime

      console.log(`Get subjects API response time: ${responseTime}ms`)
      expect(responseTime).toBeLessThan(PERFORMANCE_THRESHOLDS.API_RESPONSE)
      expect(response.status()).toBe(200)
    })

    test('创建学科API性能', async ({ page }) => {
      await page.goto(`${ADMIN_URL}/subjects`)
      await page.waitForLoadState('networkidle')

      // 打开创建表单
      await page.click('text=新建学科')
      await page.waitForSelector('.ant-modal')

      // 填写表单
      const testName = `性能测试学科_${Date.now()}`
      await page.fill('input[placeholder="请输入学科名称"]', testName)
      await page.fill('textarea[placeholder="请输入学科描述"]', '性能测试用学科')

      // 监听创建API请求
      const createApiPromise = page.waitForResponse(response => 
        response.url().includes('/api/admin/subjects') && 
        response.request().method() === 'POST'
      )

      const startTime = Date.now()
      await page.click('.ant-modal .ant-btn-primary')
      
      const response = await createApiPromise
      const responseTime = Date.now() - startTime

      console.log(`Create subject API response time: ${responseTime}ms`)
      expect(responseTime).toBeLessThan(PERFORMANCE_THRESHOLDS.API_RESPONSE)

      // 清理测试数据
      if (response.status() === 201) {
        const body = await response.json()
        const deletePromise = page.waitForResponse(resp => 
          resp.url().includes(`/api/admin/subjects/${body.data.id}`) && 
          resp.request().method() === 'DELETE'
        )

        const row = page.locator(`tr:has-text("${testName}")`)
        await row.locator('button:has-text("删除")').click()
        await page.waitForSelector('.ant-popconfirm')
        await page.click('.ant-popconfirm .ant-btn-primary')
        
        await deletePromise
      }
    })

    test('更新学科API性能', async ({ page }) => {
      await page.goto(`${ADMIN_URL}/subjects`)
      await page.waitForLoadState('networkidle')

      // 找到第一个学科并编辑
      const firstRow = page.locator('.ant-table tbody tr').first()
      await firstRow.locator('button:has-text("编辑")').click()
      await page.waitForSelector('.ant-modal')

      // 修改内容
      const updatedName = `性能测试更新_${Date.now()}`
      await page.fill('input[placeholder="请输入学科名称"]', updatedName)

      // 监听更新API请求
      const updateApiPromise = page.waitForResponse(response => 
        response.url().includes('/api/admin/subjects/') && 
        response.request().method() === 'PUT'
      )

      const startTime = Date.now()
      await page.click('.ant-modal .ant-btn-primary')
      
      const response = await updateApiPromise
      const responseTime = Date.now() - startTime

      console.log(`Update subject API response time: ${responseTime}ms`)
      expect(responseTime).toBeLessThan(PERFORMANCE_THRESHOLDS.API_RESPONSE)
      expect(response.status()).toBe(200)
    })
  })

  test.describe('用户交互性能', () => {
    test('搜索功能性能', async ({ page }) => {
      await page.goto(`${ADMIN_URL}/subjects`)
      await page.waitForLoadState('networkidle')

      const searchInput = page.locator('input[placeholder="搜索学科名称或描述"]')
      
      // 测试搜索响应时间
      const startTime = Date.now()
      
      await searchInput.fill('数学')
      
      // 等待防抖搜索完成
      await page.waitForTimeout(400) // 防抖延迟是300ms
      
      const searchTime = Date.now() - startTime
      
      console.log(`Search response time: ${searchTime}ms`)
      expect(searchTime).toBeLessThan(PERFORMANCE_THRESHOLDS.SEARCH_RESPONSE)

      // 验证搜索结果
      const tableRows = page.locator('.ant-table tbody tr')
      const rowCount = await tableRows.count()
      expect(rowCount).toBeGreaterThanOrEqual(0)
    })

    test('表格排序性能', async ({ page }) => {
      await page.goto(`${ADMIN_URL}/subjects`)
      await page.waitForLoadState('networkidle')

      // 点击排序列
      const startTime = Date.now()
      
      await page.click('.ant-table-column-sorters')
      await page.waitForTimeout(100) // 等待排序完成
      
      const sortTime = Date.now() - startTime
      
      console.log(`Table sort time: ${sortTime}ms`)
      expect(sortTime).toBeLessThan(PERFORMANCE_THRESHOLDS.OPERATION)

      // 验证表格仍然可见
      await expect(page.locator('.ant-table')).toBeVisible()
    })

    test('状态切换性能', async ({ page }) => {
      await page.goto(`${ADMIN_URL}/subjects`)
      await page.waitForLoadState('networkidle')

      // 找到第一个状态开关
      const firstSwitch = page.locator('.ant-table tbody .ant-switch').first()
      
      if (await firstSwitch.count() > 0) {
        const startTime = Date.now()
        
        await firstSwitch.click()
        await page.waitForTimeout(500) // 等待状态更新
        
        const switchTime = Date.now() - startTime
        
        console.log(`Status switch time: ${switchTime}ms`)
        expect(switchTime).toBeLessThan(PERFORMANCE_THRESHOLDS.OPERATION)
      }
    })

    test('批量操作性能', async ({ page }) => {
      await page.goto(`${ADMIN_URL}/subjects`)
      await page.waitForLoadState('networkidle')

      // 选择多个项目
      const checkboxes = page.locator('.ant-table tbody .ant-checkbox-input')
      const checkboxCount = await checkboxes.count()
      
      if (checkboxCount > 1) {
        const startTime = Date.now()
        
        // 选择前两个项目
        await checkboxes.nth(0).check()
        await checkboxes.nth(1).check()
        
        // 执行批量操作
        await page.click('button:has-text("批量启用")')
        await page.waitForTimeout(1000) // 等待批量操作完成
        
        const batchTime = Date.now() - startTime
        
        console.log(`Batch operation time: ${batchTime}ms`)
        expect(batchTime).toBeLessThan(PERFORMANCE_THRESHOLDS.OPERATION)
      }
    })
  })

  test.describe('内存和资源使用', () => {
    test('页面内存使用情况', async ({ page }) => {
      await page.goto(`${ADMIN_URL}/subjects`)
      await page.waitForLoadState('networkidle')

      // 获取页面性能指标
      const metrics = await page.evaluate(() => {
        const performance = window.performance
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        
        return {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
          firstPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-paint')?.startTime || 0,
          firstContentfulPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-contentful-paint')?.startTime || 0
        }
      })

      console.log('Performance metrics:', metrics)
      
      // 验证关键性能指标
      expect(metrics.domContentLoaded).toBeLessThan(2000) // DOM加载 < 2秒
      expect(metrics.firstPaint).toBeLessThan(1500) // 首次绘制 < 1.5秒
      expect(metrics.firstContentfulPaint).toBeLessThan(2000) // 首次内容绘制 < 2秒
    })

    test('长时间使用稳定性', async ({ page }) => {
      await page.goto(`${ADMIN_URL}/subjects`)
      await page.waitForLoadState('networkidle')

      // 模拟长时间使用
      for (let i = 0; i < 5; i++) {
        // 刷新数据
        await page.click('button:has-text("刷新")')
        await page.waitForLoadState('networkidle')
        
        // 搜索操作
        await page.fill('input[placeholder="搜索学科名称或描述"]', `测试${i}`)
        await page.waitForTimeout(400)
        
        // 清除搜索
        await page.click('.ant-input-clear-icon')
        await page.waitForTimeout(200)
        
        console.log(`Iteration ${i + 1} completed`)
      }

      // 验证页面仍然正常工作
      await expect(page.locator('h1:has-text("学科管理")')).toBeVisible()
      await expect(page.locator('.ant-table')).toBeVisible()
    })
  })
})
