<template>
  <div class="subject-detail-view">
    <div class="page-header">
      <a-button @click="goBack" class="back-button">
        <ArrowLeftOutlined />
        返回
      </a-button>
      <div v-if="subjectStore.currentSubject" class="subject-info">
        <h1>{{ subjectStore.currentSubject.name }}</h1>
        <p>{{ subjectStore.currentSubject.description }}</p>
      </div>
    </div>
    
    <div class="content-container">
      <!-- 加载状态 -->
      <div v-if="subjectStore.isLoading" class="loading-container">
        <a-spin size="large" />
        <p>正在加载文件结构...</p>
      </div>
      
      <!-- 错误状态 -->
      <a-alert
        v-else-if="subjectStore.hasError"
        type="error"
        :message="subjectStore.error"
        show-icon
        closable
        @close="subjectStore.clearError"
      />
      
      <!-- 文件树 -->
      <div v-else class="file-tree-container">
        <h2>文件结构</h2>
        <a-tree
          v-if="treeData.length > 0"
          :tree-data="treeData"
          :show-icon="true"
          :selectable="true"
          @select="onFileSelect"
        >
          <template #icon="{ dataRef }">
            <FolderOutlined v-if="dataRef.type === 'folder'" />
            <FileOutlined v-else />
          </template>
        </a-tree>
        <a-empty v-else description="该学科暂无文件" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowLeftOutlined, FolderOutlined, FileOutlined } from '@ant-design/icons-vue'
import { useSubjectStore } from '@/stores/subject'
import type { FileNode } from '@/types/api'

const router = useRouter()
const route = useRoute()
const subjectStore = useSubjectStore()

const subjectId = computed(() => Number(route.params.id))

// 构建树形数据
const treeData = computed(() => {
  return buildTreeData(subjectStore.subjectFiles)
})

// 构建树形结构 - 后端已返回树形结构，只需转换为Ant Design Tree组件格式
const buildTreeData = (files: any[]) => {
  const convertNode = (node: any): any => {
    return {
      key: node.id,
      title: node.name,
      type: node.type,
      children: node.children ? node.children.map(convertNode) : [],
      ...node
    }
  }

  return files.map(convertNode)
}

// 文件选择处理
const onFileSelect = (selectedKeys: number[], info: any) => {
  if (selectedKeys.length > 0) {
    const selectedNode = info.node.dataRef
    if (selectedNode.type === 'file') {
      router.push(`/files/${selectedNode.id}`)
    }
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 加载数据
const loadData = async () => {
  try {
    await subjectStore.fetchSubjectDetail(subjectId.value)
    await subjectStore.fetchSubjectFiles(subjectId.value)
  } catch (error) {
    console.error('加载学科详情失败:', error)
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.subject-detail-view {
  min-height: 100vh;
  background: #f8fafc;
}

.page-header {
  background: white;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.subject-info h1 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #2d3748;
}

.subject-info p {
  color: #718096;
  margin: 0;
}

.content-container {
  max-width: 95%;
  margin: 0 auto;
  padding: 2rem;
  width: 100%;
}

@media (min-width: 768px) {
  .content-container {
    max-width: 90%;
    padding: 2rem 3rem;
  }
}

@media (min-width: 1200px) {
  .content-container {
    max-width: 85%;
    padding: 2rem 3rem;
  }
}

@media (min-width: 1600px) {
  .content-container {
    max-width: 80%;
    padding: 2rem 4rem;
  }
}

.loading-container {
  text-align: center;
  padding: 4rem;
}

.loading-container p {
  margin-top: 1rem;
  color: #718096;
}

.file-tree-container {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.file-tree-container h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #2d3748;
}

:deep(.ant-tree) {
  background: transparent;
}

:deep(.ant-tree-node-content-wrapper) {
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

:deep(.ant-tree-node-content-wrapper:hover) {
  background: #f7fafc;
}

:deep(.ant-tree-node-selected) {
  background: #ebf8ff !important;
}

@media (max-width: 768px) {
  .page-header {
    padding: 1rem;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .content-container {
    padding: 1rem;
  }
}
</style>
