/**
 * 全局错误处理中间件
 * 统一处理应用中的所有错误，提供友好的错误响应
 */

import { Context, Next } from 'koa';
import { ErrorCode } from '../utils/response';
import { config, isDevelopment } from '../config';

// 错误类型定义
export interface AppError extends Error {
  status?: number;
  code?: ErrorCode;
  details?: any;
  expose?: boolean;
}

/**
 * 创建应用错误
 */
export function createError(
  message: string,
  code: ErrorCode = ErrorCode.INTERNAL_ERROR,
  status: number = 500,
  details?: any
): AppError {
  const error = new Error(message) as AppError;
  error.code = code;
  error.status = status;
  error.details = details;
  error.expose = status < 500; // 4xx错误可以暴露给客户端
  return error;
}

/**
 * 错误处理中间件
 */
export function errorHandler() {
  return async (ctx: Context, next: Next) => {
    try {
      await next();
    } catch (err: any) {
      // 记录错误日志
      console.error('❌ 请求处理错误:', {
        requestId: ctx.state.requestId,
        method: ctx.method,
        url: ctx.url,
        error: err.message,
        stack: isDevelopment ? err.stack : undefined,
        userAgent: ctx.get('User-Agent'),
        ip: ctx.ip
      });
      
      // 确定错误状态码
      let status = err.status || err.statusCode || 500;
      
      // 确定错误代码
      let code = err.code || ErrorCode.INTERNAL_ERROR;
      
      // 确定错误消息
      let message = err.message || '服务器内部错误';
      
      // 处理特定类型的错误
      if (err.name === 'ValidationError') {
        status = 400;
        code = ErrorCode.VALIDATION_ERROR;
        message = '请求参数验证失败';
      } else if (err.name === 'UnauthorizedError') {
        status = 401;
        code = ErrorCode.UNAUTHORIZED;
        message = '未授权访问';
      } else if (err.name === 'ForbiddenError') {
        status = 403;
        code = ErrorCode.FORBIDDEN;
        message = '禁止访问';
      } else if (err.name === 'NotFoundError') {
        status = 404;
        code = ErrorCode.NOT_FOUND;
        message = '资源不存在';
      } else if (err.message && err.message.includes('FOREIGN KEY constraint failed')) {
        status = 400;
        code = ErrorCode.CONSTRAINT_VIOLATION;
        message = '数据约束违反';
      } else if (err.message && err.message.includes('UNIQUE constraint failed')) {
        status = 409;
        code = ErrorCode.RESOURCE_EXISTS;
        message = '资源已存在';
      }
      
      // 生产环境下隐藏敏感错误信息
      if (!isDevelopment && status >= 500) {
        message = '服务器内部错误';
      }
      
      // 构建错误响应
      const errorResponse = {
        success: false,
        error: {
          code,
          message,
          details: isDevelopment ? err.details || err.stack : undefined
        },
        timestamp: new Date().toISOString(),
        requestId: ctx.state.requestId
      };
      
      // 设置响应
      ctx.status = status;
      ctx.body = errorResponse;
      
      // 设置错误响应头
      ctx.set('Content-Type', 'application/json');
      
      // 如果是开发环境，添加调试信息
      if (isDevelopment) {
        ctx.set('X-Error-Stack', encodeURIComponent(err.stack || ''));
      }
    }
  };
}

/**
 * 404处理中间件
 */
export function notFoundHandler() {
  return async (ctx: Context, next: Next) => {
    await next();
    
    if (ctx.status === 404 && !ctx.body) {
      const errorResponse = {
        success: false,
        error: {
          code: ErrorCode.NOT_FOUND,
          message: `路径 ${ctx.path} 不存在`,
          details: {
            method: ctx.method,
            path: ctx.path
          }
        },
        timestamp: new Date().toISOString(),
        requestId: ctx.state.requestId
      };
      
      ctx.status = 404;
      ctx.body = errorResponse;
    }
  };
}

/**
 * 异步错误包装器
 * 用于包装异步路由处理函数，确保错误能被正确捕获
 */
export function asyncHandler(fn: (ctx: Context, next: Next) => Promise<any>) {
  return async (ctx: Context, next: Next) => {
    try {
      await fn(ctx, next);
    } catch (error) {
      throw error; // 重新抛出错误，让错误处理中间件处理
    }
  };
}

// 常用错误创建函数
export const errors = {
  notFound: (message: string = '资源不存在') => 
    createError(message, ErrorCode.NOT_FOUND, 404),
    
  badRequest: (message: string = '请求参数错误') => 
    createError(message, ErrorCode.INVALID_REQUEST, 400),
    
  unauthorized: (message: string = '未授权访问') => 
    createError(message, ErrorCode.UNAUTHORIZED, 401),
    
  forbidden: (message: string = '禁止访问') => 
    createError(message, ErrorCode.FORBIDDEN, 403),
    
  conflict: (message: string = '资源冲突') => 
    createError(message, ErrorCode.RESOURCE_EXISTS, 409),
    
  validation: (message: string = '数据验证失败', details?: any) => 
    createError(message, ErrorCode.VALIDATION_ERROR, 400, details),
    
  internal: (message: string = '服务器内部错误') => 
    createError(message, ErrorCode.INTERNAL_ERROR, 500),
    
  database: (message: string = '数据库操作失败') => 
    createError(message, ErrorCode.DATABASE_ERROR, 500)
};