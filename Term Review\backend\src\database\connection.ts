import Database from 'better-sqlite3'
import { join } from 'path'
import { existsSync, mkdirSync } from 'fs'
import { mockDb } from './mockDatabase'

// 数据库配置
const DB_DIR = join(process.cwd(), 'storage')
const isTestEnv = process.env.NODE_ENV === 'test'
const DB_NAME = isTestEnv ? 'test-term-review.db' : 'term-review.db'
const DB_PATH = join(DB_DIR, DB_NAME)

// 在测试环境下使用mock数据库，避免better-sqlite3绑定问题
let db: any

if (isTestEnv) {
  console.log('🧪 Using mock database for testing')
  db = mockDb
} else {
  // 确保存储目录存在
  if (!existsSync(DB_DIR)) {
    mkdirSync(DB_DIR, { recursive: true })
  }

  // 创建真实数据库连接
  db = new Database(DB_PATH)
}

export { db }

// 设置SQLite优化配置
db.pragma('journal_mode = WAL')        // 写入优化
db.pragma('synchronous = NORMAL')      // 平衡性能和安全
db.pragma('cache_size = 10000')        // 增加缓存
db.pragma('temp_store = MEMORY')       // 临时表存储在内存

// 创建学科表
const createSubjectsTable = () => {
  const sql = `
    CREATE TABLE IF NOT EXISTS subjects (
      id TEXT PRIMARY KEY,          -- UUID
      name TEXT UNIQUE NOT NULL,    -- 学科名称
      created_at INTEGER NOT NULL,  -- 创建时间戳
      updated_at INTEGER NOT NULL,  -- 更新时间戳
      file_count INTEGER DEFAULT 0  -- 文件数量缓存
    )
  `

  db.exec(sql)

  // 创建索引
  db.exec('CREATE INDEX IF NOT EXISTS idx_subjects_name ON subjects(name)')
  db.exec('CREATE INDEX IF NOT EXISTS idx_subjects_created_at ON subjects(created_at DESC)')

  console.log('✅ subjects table created successfully')
}

// 创建文件节点表（为后续功能预留）
const createFileNodesTable = () => {
  const sql = `
    CREATE TABLE IF NOT EXISTS file_nodes (
      id TEXT PRIMARY KEY,          -- UUID
      subject_id TEXT NOT NULL,     -- 所属学科ID
      name TEXT NOT NULL,           -- 文件/文件夹名称
      type TEXT NOT NULL,           -- 'file' 或 'folder'
      path TEXT NOT NULL,           -- 完整相对路径
      parent_id TEXT,               -- 父节点ID
      size INTEGER,                 -- 文件大小（字节）
      created_at INTEGER NOT NULL,  -- 创建时间戳
      
      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
      FOREIGN KEY (parent_id) REFERENCES file_nodes(id) ON DELETE CASCADE
    )
  `

  db.exec(sql)

  // 创建索引（性能优化）
  db.exec('CREATE INDEX IF NOT EXISTS idx_file_nodes_subject_id ON file_nodes(subject_id)')
  db.exec('CREATE INDEX IF NOT EXISTS idx_file_nodes_parent_id ON file_nodes(parent_id)')
  db.exec('CREATE INDEX IF NOT EXISTS idx_file_nodes_type ON file_nodes(type)')
  db.exec('CREATE INDEX IF NOT EXISTS idx_file_nodes_path ON file_nodes(path)')

  // 复合索引优化常见查询
  db.exec('CREATE INDEX IF NOT EXISTS idx_file_nodes_subject_parent ON file_nodes(subject_id, parent_id)')
  db.exec('CREATE INDEX IF NOT EXISTS idx_file_nodes_subject_type ON file_nodes(subject_id, type)')
  db.exec('CREATE INDEX IF NOT EXISTS idx_file_nodes_name_search ON file_nodes(subject_id, name)')

  // 排序优化索引
  db.exec('CREATE INDEX IF NOT EXISTS idx_file_nodes_type_name ON file_nodes(type, name)')
  db.exec('CREATE INDEX IF NOT EXISTS idx_file_nodes_created_at ON file_nodes(created_at DESC)')

  console.log('✅ file_nodes table created successfully')
}

// 初始化数据库
export const initDatabase = () => {
  try {
    createSubjectsTable()
    createFileNodesTable()
    console.log('🚀 Database initialized successfully')
  } catch (error) {
    console.error('❌ Database initialization failed:', error)
    throw error
  }
}

// 优雅关闭数据库连接
export const closeDatabase = () => {
  try {
    db.close()
    console.log('✅ Database connection closed')
  } catch (error) {
    console.error('❌ Error closing database:', error)
  }
}

// 进程退出时关闭数据库
process.on('exit', closeDatabase)
process.on('SIGINT', () => {
  closeDatabase()
  process.exit(0)
})
process.on('SIGTERM', () => {
  closeDatabase()
  process.exit(0)
})

export default db