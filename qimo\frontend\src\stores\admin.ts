import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { adminApi, apiUtils } from '@/utils/api'
import { useSubjectStore } from './subject'
import type { Subject } from '@/types/api'

/**
 * 管理员状态管理Store
 * 负责管理后台的状态管理，包括学科管理、数据同步等
 */
export const useAdminStore = defineStore('admin', () => {
  // 状态
  const adminSubjects = ref<Subject[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const operationLoading = ref(false)

  // 缓存状态
  const adminSubjectsCache = ref<{ data: Subject[], timestamp: number } | null>(null)
  const CACHE_DURATION = 2 * 60 * 1000 // 2分钟缓存（管理数据更新频繁）

  // 获取普通用户store实例，用于数据同步
  const subjectStore = useSubjectStore()

  // 计算属性
  const adminSubjectCount = computed(() => adminSubjects.value.length)
  const hasAdminSubjects = computed(() => adminSubjects.value.length > 0)
  const isLoading = computed(() => loading.value)
  const isOperationLoading = computed(() => operationLoading.value)
  const hasError = computed(() => !!error.value)

  // 根据ID获取管理员学科
  const getAdminSubjectById = computed(() => {
    return (id: number) => adminSubjects.value.find(subject => subject.id === id)
  })

  // 方法
  const setLoading = (value: boolean) => {
    loading.value = value
  }

  const setOperationLoading = (value: boolean) => {
    operationLoading.value = value
  }

  const setError = (message: string | null) => {
    error.value = message
  }

  const clearError = () => {
    error.value = null
  }

  // 检查缓存是否有效
  const isCacheValid = (timestamp: number) => {
    return Date.now() - timestamp < CACHE_DURATION
  }

  // 获取管理员学科列表
  const fetchAdminSubjects = async (forceRefresh = false) => {
    try {
      // 检查缓存
      if (!forceRefresh && adminSubjectsCache.value && isCacheValid(adminSubjectsCache.value.timestamp)) {
        console.log('📦 Using cached admin subjects data')
        adminSubjects.value = adminSubjectsCache.value.data
        return adminSubjectsCache.value.data
      }

      setLoading(true)
      clearError()

      const response = await adminApi.getSubjects()
      const data = response.data.data

      // 更新状态和缓存
      adminSubjects.value = data
      adminSubjectsCache.value = {
        data,
        timestamp: Date.now()
      }

      console.log('🔄 Fetched fresh admin subjects data')
      return data
    } catch (err: any) {
      const message = apiUtils.handleError(err)
      setError(message)
      console.error('❌ Failed to fetch admin subjects:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }

  // 创建学科
  const createSubject = async (data: { name: string; description?: string; status?: number; sort_order?: number }) => {
    try {
      setOperationLoading(true)
      clearError()

      const response = await adminApi.createSubject(data)
      const newSubject = response.data.data

      // 更新本地状态
      adminSubjects.value.unshift(newSubject)
      
      // 清除缓存，强制下次刷新
      adminSubjectsCache.value = null
      
      // 同步到普通用户store
      await syncToUserStore()

      console.log('✅ Subject created successfully:', newSubject)
      return newSubject
    } catch (err: any) {
      const message = apiUtils.handleError(err)
      setError(message)
      console.error('❌ Failed to create subject:', err)
      throw err
    } finally {
      setOperationLoading(false)
    }
  }

  // 更新学科
  const updateSubject = async (id: number, data: { name?: string; description?: string; status?: number; sort_order?: number }) => {
    try {
      setOperationLoading(true)
      clearError()

      const response = await adminApi.updateSubject(id, data)
      const updatedSubject = response.data.data

      // 更新本地状态
      const index = adminSubjects.value.findIndex(s => s.id === id)
      if (index !== -1) {
        adminSubjects.value[index] = updatedSubject
      }

      // 清除缓存
      adminSubjectsCache.value = null
      
      // 同步到普通用户store
      await syncToUserStore()

      console.log('✅ Subject updated successfully:', updatedSubject)
      return updatedSubject
    } catch (err: any) {
      const message = apiUtils.handleError(err)
      setError(message)
      console.error('❌ Failed to update subject:', err)
      throw err
    } finally {
      setOperationLoading(false)
    }
  }

  // 删除学科
  const deleteSubject = async (id: number, cascade = false) => {
    try {
      setOperationLoading(true)
      clearError()

      await adminApi.deleteSubject(id, cascade)

      // 更新本地状态
      adminSubjects.value = adminSubjects.value.filter(s => s.id !== id)

      // 清除缓存
      adminSubjectsCache.value = null
      
      // 同步到普通用户store
      await syncToUserStore()

      console.log('✅ Subject deleted successfully:', id)
      return true
    } catch (err: any) {
      const message = apiUtils.handleError(err)
      setError(message)
      console.error('❌ Failed to delete subject:', err)
      throw err
    } finally {
      setOperationLoading(false)
    }
  }

  // 获取学科统计信息
  const getSubjectStats = async (id: number) => {
    try {
      const response = await adminApi.getSubjectStats(id)
      return response.data.data
    } catch (err: any) {
      const message = apiUtils.handleError(err)
      console.error('❌ Failed to get subject stats:', err)
      throw err
    }
  }

  // 同步数据到普通用户store
  const syncToUserStore = async () => {
    try {
      // 清除普通用户store的缓存，强制下次获取最新数据
      subjectStore.clearCache()
      
      // 如果普通用户store有数据，则刷新
      if (subjectStore.hasSubjects) {
        await subjectStore.fetchSubjects(false, true) // 强制刷新
      }
      
      console.log('🔄 Synced data to user store')
    } catch (err) {
      console.warn('⚠️ Failed to sync to user store:', err)
      // 同步失败不影响管理操作
    }
  }

  // 批量操作
  const batchUpdateStatus = async (ids: number[], status: 0 | 1) => {
    try {
      setOperationLoading(true)
      clearError()

      // 并发更新所有学科状态
      const promises = ids.map(id => adminApi.updateSubject(id, { status }))
      await Promise.all(promises)

      // 更新本地状态
      adminSubjects.value.forEach(subject => {
        if (ids.includes(subject.id)) {
          subject.status = status
        }
      })

      // 清除缓存并同步
      adminSubjectsCache.value = null
      await syncToUserStore()

      console.log('✅ Batch status update completed:', { ids, status })
      return true
    } catch (err: any) {
      const message = apiUtils.handleError(err)
      setError(message)
      console.error('❌ Failed to batch update status:', err)
      throw err
    } finally {
      setOperationLoading(false)
    }
  }

  // 清空缓存
  const clearCache = () => {
    adminSubjectsCache.value = null
    console.log('🗑️ Admin cache cleared')
  }

  // 清空数据
  const clearData = () => {
    adminSubjects.value = []
    clearError()
  }

  // 清空所有数据和缓存
  const clearAll = () => {
    clearData()
    clearCache()
  }

  return {
    // 状态
    adminSubjects,
    loading,
    error,
    operationLoading,

    // 计算属性
    adminSubjectCount,
    hasAdminSubjects,
    isLoading,
    isOperationLoading,
    hasError,
    getAdminSubjectById,

    // 方法
    setLoading,
    setOperationLoading,
    setError,
    clearError,
    fetchAdminSubjects,
    createSubject,
    updateSubject,
    deleteSubject,
    getSubjectStats,
    syncToUserStore,
    batchUpdateStatus,
    clearData,
    clearCache,
    clearAll
  }
})
