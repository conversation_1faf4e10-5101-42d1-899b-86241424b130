// 临时测试文件，用于验证FileController功能
import { FileController } from './controllers/FileController.js'

// 模拟Koa Context
function createMockContext(params: any = {}, query: any = {}, body: any = {}) {
  return {
    params,
    query,
    request: { body },
    status: 200,
    body: {},
    throw: (status: number, message: string) => {
      throw new Error(`${status}: ${message}`)
    }
  } as any
}

async function testFileController() {
  console.log('🧪 Testing FileController...')
  
  try {
    // 测试参数验证
    console.log('1. Testing parameter validation...')
    
    // 测试无效的学科ID
    const ctx1 = createMockContext({ id: 'invalid-uuid' }, {})
    await FileController.getFiles(ctx1)
    console.log('✅ getFiles validates UUID:', ctx1.status === 400 ? 'PASS' : 'FAIL')
    
    // 测试无效的查询参数
    const ctx2 = createMockContext({ id: '550e8400-e29b-41d4-a716-************' }, { page: -1 })
    await FileController.getFiles(ctx2)
    console.log('✅ getFiles validates page:', ctx2.status === 400 ? 'PASS' : 'FAIL')
    
    // 测试搜索参数验证
    const ctx3 = createMockContext({ id: '550e8400-e29b-41d4-a716-************' }, { query: '' })
    await FileController.searchFiles(ctx3)
    console.log('✅ searchFiles validates query:', ctx3.status === 400 ? 'PASS' : 'FAIL')
    
    // 测试面包屑参数验证
    const ctx4 = createMockContext({ id: 'invalid-uuid' }, {})
    await FileController.getBreadcrumb(ctx4)
    console.log('✅ getBreadcrumb validates UUID:', ctx4.status === 400 ? 'PASS' : 'FAIL')
    
    console.log('✅ All parameter validation tests completed!')
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// 只在直接运行时执行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testFileController()
}

export { testFileController }
