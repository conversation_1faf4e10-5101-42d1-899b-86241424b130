<template>
  <div class="home-view">
    <div class="hero-section">
      <h1 class="hero-title">期末复习平台</h1>
      <p class="hero-subtitle">高效整理，轻松复习，助你期末考试取得好成绩</p>
      <div class="hero-actions">
        <a-button type="primary" size="large" @click="goToSubjects">
          开始浏览学科
        </a-button>
        <a-button size="large" @click="goToSearch">
          搜索资料
        </a-button>
      </div>
    </div>
    
    <div class="features-section">
      <div class="container">
        <h2 class="section-title">平台特色</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">📚</div>
            <h3>学科分类</h3>
            <p>按学科整理复习资料，结构清晰，查找方便</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">📝</div>
            <h3>Markdown支持</h3>
            <p>支持Markdown格式，让笔记更加美观易读</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🔍</div>
            <h3>智能搜索</h3>
            <p>快速搜索文件内容，精准定位所需资料</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">📱</div>
            <h3>响应式设计</h3>
            <p>支持多设备访问，随时随地进行复习</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goToSubjects = () => {
  router.push('/subjects')
}

const goToSearch = () => {
  router.push('/search')
}
</script>

<style scoped>
.home-view {
  min-height: 100vh;
}

.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 120px 20px;
  text-align: center;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.features-section {
  padding: 80px 20px;
  background: #f8fafc;
}

.container {
  max-width: 90%;
  margin: 0 auto;
  width: 100%;
}

@media (min-width: 768px) {
  .container {
    max-width: 85%;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 80%;
  }
}

@media (min-width: 1600px) {
  .container {
    max-width: 75%;
  }
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 3rem;
  color: #2d3748;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #2d3748;
}

.feature-card p {
  color: #718096;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
}
</style>
