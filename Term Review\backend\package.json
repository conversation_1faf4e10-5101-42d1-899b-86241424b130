{"name": "term-review-backend", "version": "0.1.0", "type": "module", "scripts": {"dev": "tsx watch src/app.ts", "build": "tsc", "start": "node dist/app.js", "lint": "eslint src --ext .ts --fix", "lint:check": "eslint src --ext .ts", "type-check": "tsc --noEmit", "db:init": "tsx src/database/init.ts", "test": "jest --config jest.config.cjs", "test:watch": "jest --watch --config jest.config.cjs"}, "dependencies": {"@koa/multer": "^3.0.2", "better-sqlite3": "^9.4.3", "joi": "^17.12.1", "koa": "^2.15.0", "koa-bodyparser": "^4.4.1", "koa-cors": "^0.0.16", "koa-router": "^12.0.1", "koa-static": "^5.0.0", "marked": "^5.1.2", "uuid": "^9.0.1"}, "devDependencies": {"@types/better-sqlite3": "^7.6.9", "@types/jest": "^29.5.12", "@types/koa": "^2.15.0", "@types/koa-bodyparser": "^4.3.12", "@types/koa-cors": "^0.0.5", "@types/koa-router": "^7.4.8", "@types/koa-static": "^4.0.4", "@types/marked": "^5.0.2", "@types/node": "^20.11.10", "@types/supertest": "^6.0.3", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "prettier": "^3.2.4", "supertest": "^6.3.4", "ts-jest": "^29.1.2", "tsx": "^4.7.1", "typescript": "^5.3.3"}}