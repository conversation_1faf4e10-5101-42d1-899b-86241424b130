import { describe, test, expect, beforeEach, afterEach } from '@jest/globals'
import request from 'supertest'
import app from '../../src/app.js'
import { SubjectModel } from '../../src/models/Subject.js'

describe('Subject Management API', () => {
  // 每个测试前清理数据
  beforeEach(async () => {
    // 清理测试数据
    const subjects = SubjectModel.findAll()
    for (const subject of subjects) {
      SubjectModel.delete(subject.id)
    }
  })

  afterEach(async () => {
    // 清理测试数据
    const subjects = SubjectModel.findAll()
    for (const subject of subjects) {
      SubjectModel.delete(subject.id)
    }
  })

  describe('POST /api/subjects', () => {
    test('应能成功创建学科', async () => {
      const subjectData = { name: '计算机科学' }

      const response = await request(app.callback())
        .post('/api/subjects')
        .send(subjectData)
        .expect(201)

      expect(response.body.success).toBe(true)
      expect(response.body.data.name).toBe('计算机科学')
      expect(response.body.data.id).toBeDefined()
      expect(response.body.data.fileCount).toBe(0)
      expect(response.body.message).toBe('学科创建成功')
    })

    test('应拒绝重复的学科名称', async () => {
      // 先创建一个学科
      SubjectModel.create({ name: '数学' })

      // 尝试创建重复学科
      const response = await request(app.callback())
        .post('/api/subjects')
        .send({ name: '数学' })
        .expect(409)

      expect(response.body.success).toBe(false)
      expect(response.body.message).toContain('已存在')
      expect(response.body.error).toBe('CONFLICT')
    })

    test('应拒绝空的学科名称', async () => {
      const response = await request(app.callback())
        .post('/api/subjects')
        .send({ name: '' })
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('VALIDATION_ERROR')
    })

    test('应拒绝包含特殊字符的学科名称', async () => {
      const response = await request(app.callback())
        .post('/api/subjects')
        .send({ name: '计算机<科学>' })
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('VALIDATION_ERROR')
      expect(response.body.message).toContain('特殊字符')
    })

    test('应拒绝过长的学科名称', async () => {
      const longName = 'a'.repeat(51)

      const response = await request(app.callback())
        .post('/api/subjects')
        .send({ name: longName })
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('VALIDATION_ERROR')
    })

    test('应自动去除名称前后空格', async () => {
      const response = await request(app.callback())
        .post('/api/subjects')
        .send({ name: '  物理  ' })
        .expect(201)

      expect(response.body.data.name).toBe('物理')
    })
  })

  describe('GET /api/subjects', () => {
    test('应能获取空的学科列表', async () => {
      const response = await request(app.callback())
        .get('/api/subjects')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(Array.isArray(response.body.data)).toBe(true)
      expect(response.body.data.length).toBe(0)
      expect(response.body.total).toBe(0)
    })

    test('应能获取学科列表', async () => {
      // 创建测试数据
      const subject1 = SubjectModel.create({ name: '计算机科学' })
      const subject2 = SubjectModel.create({ name: '数学' })

      const response = await request(app.callback())
        .get('/api/subjects')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(Array.isArray(response.body.data)).toBe(true)
      expect(response.body.data.length).toBe(2)
      expect(response.body.total).toBe(2)

      // 验证数据结构
      const firstSubject = response.body.data[0]
      expect(firstSubject.id).toBeDefined()
      expect(firstSubject.name).toBeDefined()
      expect(firstSubject.createdAt).toBeDefined()
      expect(firstSubject.updatedAt).toBeDefined()
      expect(firstSubject.fileCount).toBeDefined()
    })

    test('学科列表应按创建时间倒序排列', async () => {
      // 创建测试数据（有时间间隔）
      const subject1 = SubjectModel.create({ name: '计算机科学' })
      await new Promise(resolve => setTimeout(resolve, 10)) // 等待10ms
      const subject2 = SubjectModel.create({ name: '数学' })

      const response = await request(app.callback())
        .get('/api/subjects')
        .expect(200)

      expect(response.body.data[0].name).toBe('数学') // 后创建的在前
      expect(response.body.data[1].name).toBe('计算机科学')
    })
  })

  describe('GET /api/subjects/:id', () => {
    test('应能获取单个学科详情', async () => {
      const created = SubjectModel.create({ name: '物理' })

      const response = await request(app.callback())
        .get(`/api/subjects/${created.id}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.id).toBe(created.id)
      expect(response.body.data.name).toBe('物理')
      expect(response.body.message).toBe('获取学科详情成功')
    })

    test('应返回404当学科不存在', async () => {
      const fakeId = '550e8400-e29b-41d4-a716-446655440000'

      const response = await request(app.callback())
        .get(`/api/subjects/${fakeId}`)
        .expect(404)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('NOT_FOUND')
      expect(response.body.message).toContain('不存在')
    })

    test('应返回400当学科ID格式无效', async () => {
      const response = await request(app.callback())
        .get('/api/subjects/invalid-id')
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('VALIDATION_ERROR')
    })
  })

  describe('PUT /api/subjects/:id', () => {
    test('应能更新学科名称', async () => {
      const created = SubjectModel.create({ name: '化学' })

      const response = await request(app.callback())
        .put(`/api/subjects/${created.id}`)
        .send({ name: '有机化学' })
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.name).toBe('有机化学')
      expect(response.body.message).toBe('学科更新成功')
    })

    test('应返回404当更新不存在的学科', async () => {
      const fakeId = '550e8400-e29b-41d4-a716-446655440000'

      const response = await request(app.callback())
        .put(`/api/subjects/${fakeId}`)
        .send({ name: '生物' })
        .expect(404)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('NOT_FOUND')
    })

    test('应拒绝重复的学科名称', async () => {
      const subject1 = SubjectModel.create({ name: '生物' })
      const subject2 = SubjectModel.create({ name: '地理' })

      const response = await request(app.callback())
        .put(`/api/subjects/${subject2.id}`)
        .send({ name: '生物' })
        .expect(409)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('CONFLICT')
    })
  })

  describe('DELETE /api/subjects/:id', () => {
    test('应能删除学科', async () => {
      const created = SubjectModel.create({ name: '历史' })

      const response = await request(app.callback())
        .delete(`/api/subjects/${created.id}`)
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.message).toBe('学科删除成功')

      // 验证学科已被删除
      const subjects = SubjectModel.findAll()
      expect(subjects.length).toBe(0)
    })

    test('应返回404当删除不存在的学科', async () => {
      const fakeId = '550e8400-e29b-41d4-a716-446655440000'

      const response = await request(app.callback())
        .delete(`/api/subjects/${fakeId}`)
        .expect(404)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('NOT_FOUND')
    })
  })

  describe('Health Check', () => {
    test('应返回健康检查信息', async () => {
      const response = await request(app.callback())
        .get('/api/health')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.message).toContain('running')
      expect(response.body.version).toBeDefined()
      expect(response.body.timestamp).toBeDefined()
    })
  })
})