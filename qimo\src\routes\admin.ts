/**
 * 管理员专用路由
 * 处理管理员权限相关的API接口
 * 
 * <AUTHOR> (Engineer)
 * @date 2025-07-27
 */

import Router from 'koa-router';
import { Context } from 'koa';
import { subjectService } from '../services/subjectService';
import { fileService } from '../services/fileService';

// 创建管理员路由实例
const router = new Router();

/**
 * 管理员权限验证中间件
 * 确保请求具有管理员权限
 */
const requireAdmin = async (ctx: Context, next: () => Promise<void>) => {
  // 检查是否已通过管理员认证中间件
  if (!ctx.state.isAdmin) {
    ctx.apiResponse.forbidden('需要管理员权限');
    return;
  }
  await next();
};

// 应用管理员权限验证中间件到所有路由
router.use(requireAdmin);

/**
 * 获取所有学科列表（管理员版本，包含统计信息）
 * GET /api/admin/subjects
 */
router.get('/subjects', async (ctx: Context) => {
  try {
    // 管理员版本默认包含统计信息
    const subjects = await subjectService.getAllSubjects(true);
    ctx.apiResponse.success(subjects);
  } catch (error) {
    console.error('GET /api/admin/subjects error:', error);
    const message = error instanceof Error ? error.message : '获取学科列表失败';
    ctx.apiResponse.error('DATABASE_ERROR', message);
  }
});

/**
 * 获取单个学科详情（管理员版本，包含统计信息）
 * GET /api/admin/subjects/:id
 */
router.get('/subjects/:id', async (ctx: Context) => {
  try {
    const { id } = ctx.params;

    // 参数验证
    if (!id || isNaN(Number(id))) {
      ctx.apiResponse.badRequest('学科ID必须是有效的数字');
      return;
    }

    // 管理员版本默认包含统计信息
    const subject = await subjectService.getSubjectById(Number(id), true);

    if (!subject) {
      ctx.apiResponse.notFound('学科不存在');
      return;
    }

    ctx.apiResponse.success(subject);
  } catch (error) {
    console.error('GET /api/admin/subjects/:id error:', error);
    const message = error instanceof Error ? error.message : '获取学科详情失败';
    ctx.apiResponse.error('DATABASE_ERROR', message);
  }
});

/**
 * 创建新学科（管理员专用）
 * POST /api/admin/subjects
 */
router.post('/subjects', async (ctx: Context) => {
  try {
    const { name, description, status, sort_order } = ctx.request.body as {
      name?: string;
      description?: string;
      status?: number;
      sort_order?: number;
    };

    // 参数验证
    if (!name || name.trim().length === 0) {
      ctx.apiResponse.badRequest('学科名称不能为空');
      return;
    }

    if (name.length > 100) {
      ctx.apiResponse.badRequest('学科名称不能超过100个字符');
      return;
    }

    // 验证状态值
    const finalStatus = status !== undefined ? status : 1;
    if (![0, 1].includes(finalStatus)) {
      ctx.apiResponse.badRequest('状态值必须是0或1');
      return;
    }

    // 验证排序权重
    const finalSortOrder = sort_order !== undefined ? sort_order : 0;
    if (!Number.isInteger(finalSortOrder) || finalSortOrder < 0) {
      ctx.apiResponse.badRequest('排序权重必须是非负整数');
      return;
    }

    // 创建学科
    const subjectId = await subjectService.createSubject({
      name: name.trim(),
      description: description?.trim() || '',
      status: finalStatus,
      sort_order: finalSortOrder
    });

    // 获取创建的学科详情（包含统计信息）
    const newSubject = await subjectService.getSubjectById(subjectId, true);
    ctx.apiResponse.success(newSubject, '学科创建成功');
  } catch (error) {
    console.error('POST /api/admin/subjects error:', error);
    const message = error instanceof Error ? error.message : '创建学科失败';

    if (message.includes('已存在')) {
      ctx.apiResponse.conflict(message);
    } else {
      ctx.apiResponse.error('DATABASE_ERROR', message);
    }
  }
});

/**
 * 更新学科信息（管理员专用）
 * PUT /api/admin/subjects/:id
 */
router.put('/subjects/:id', async (ctx: Context) => {
  try {
    const { id } = ctx.params;
    const { name, description, status, sort_order } = ctx.request.body as {
      name?: string;
      description?: string;
      status?: number;
      sort_order?: number;
    };

    // 参数验证
    if (!id || isNaN(Number(id))) {
      ctx.apiResponse.badRequest('学科ID必须是有效的数字');
      return;
    }

    // 构建更新数据
    const updates: any = {};
    if (name !== undefined) {
      if (!name || name.trim().length === 0) {
        ctx.apiResponse.badRequest('学科名称不能为空');
        return;
      }
      if (name.length > 100) {
        ctx.apiResponse.badRequest('学科名称不能超过100个字符');
        return;
      }
      updates.name = name.trim();
    }
    if (description !== undefined) {
      updates.description = description?.trim() || '';
    }
    if (status !== undefined) {
      if (![0, 1].includes(status)) {
        ctx.apiResponse.badRequest('状态值必须是0或1');
        return;
      }
      updates.status = status;
    }
    if (sort_order !== undefined) {
      if (!Number.isInteger(sort_order) || sort_order < 0) {
        ctx.apiResponse.badRequest('排序权重必须是非负整数');
        return;
      }
      updates.sort_order = sort_order;
    }

    if (Object.keys(updates).length === 0) {
      ctx.apiResponse.badRequest('至少需要提供一个更新字段');
      return;
    }

    // 更新学科
    const success = await subjectService.updateSubject(Number(id), updates);
    if (!success) {
      ctx.apiResponse.error('DATABASE_ERROR', '更新学科失败');
      return;
    }

    // 获取更新后的学科详情（包含统计信息）
    const updatedSubject = await subjectService.getSubjectById(Number(id), true);
    ctx.apiResponse.success(updatedSubject);
  } catch (error) {
    console.error('PUT /api/admin/subjects/:id error:', error);
    const message = error instanceof Error ? error.message : '更新学科失败';

    if (message.includes('不存在')) {
      ctx.apiResponse.notFound(message);
    } else if (message.includes('已存在')) {
      ctx.apiResponse.conflict(message);
    } else {
      ctx.apiResponse.error('DATABASE_ERROR', message);
    }
  }
});

/**
 * 删除学科（管理员专用，支持级联删除）
 * DELETE /api/admin/subjects/:id
 */
router.delete('/subjects/:id', async (ctx: Context) => {
  try {
    const { id } = ctx.params;
    const { cascade } = ctx.query;

    // 参数验证
    if (!id || isNaN(Number(id))) {
      ctx.apiResponse.badRequest('学科ID必须是有效的数字');
      return;
    }

    const subjectId = Number(id);

    // 检查是否需要级联删除
    if (cascade === 'true') {
      // 级联删除：先删除相关文件，再删除学科
      try {
        // 获取学科的所有文件节点
        const fileTree = await fileService.getSubjectFileTree(subjectId);

        // 如果有文件，进行级联删除
        if (fileTree && fileTree.children && fileTree.children.length > 0) {
          // 这里可以添加文件系统清理逻辑
          // 目前只进行数据库级联删除（通过外键约束自动处理）
        }
      } catch (error) {
        // 如果文件删除失败，记录错误但继续删除学科
        console.warn('级联删除文件时出现警告:', error);
      }
    } else {
      // 检查是否有关联文件
      try {
        const stats = await subjectService.getSubjectStats(subjectId);
        if (stats.fileCount > 0) {
          ctx.apiResponse.badRequest('学科下还有文件，请先删除文件或使用级联删除（cascade=true）');
          return;
        }
      } catch (error) {
        // 如果学科不存在，会在deleteSubject中处理
      }
    }

    // 删除学科
    const success = await subjectService.deleteSubject(subjectId);
    if (!success) {
      ctx.apiResponse.error('DATABASE_ERROR', '删除学科失败');
      return;
    }

    ctx.apiResponse.success(null, '学科删除成功');
  } catch (error) {
    console.error('DELETE /api/admin/subjects/:id error:', error);
    const message = error instanceof Error ? error.message : '删除学科失败';

    if (message.includes('不存在')) {
      ctx.apiResponse.notFound(message);
    } else {
      ctx.apiResponse.error('DATABASE_ERROR', message);
    }
  }
});

/**
 * 获取学科统计信息（管理员专用）
 * GET /api/admin/subjects/:id/stats
 */
router.get('/subjects/:id/stats', async (ctx: Context) => {
  try {
    const { id } = ctx.params;

    // 参数验证
    if (!id || isNaN(Number(id))) {
      ctx.apiResponse.badRequest('学科ID必须是有效的数字');
      return;
    }

    const stats = await subjectService.getSubjectStats(Number(id));
    ctx.apiResponse.success(stats);
  } catch (error) {
    console.error('GET /api/admin/subjects/:id/stats error:', error);
    const message = error instanceof Error ? error.message : '获取统计信息失败';

    if (message.includes('不存在')) {
      ctx.apiResponse.notFound(message);
    } else {
      ctx.apiResponse.error('DATABASE_ERROR', message);
    }
  }
});

export default router;
