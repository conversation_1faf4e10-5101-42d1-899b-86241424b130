/**
 * 主路由配置
 * 统一管理所有API路由的注册和配置
 */

import Router from 'koa-router';
import { Context } from 'koa';
import { config } from '../config';
import { asyncHandler } from '../middleware/error';

// 导入子路由模块
import subjectsRouter from './subjects';
import filesRouter from './files';
import adminRouter from './admin';

// 创建主路由实例
const router = new Router({
  prefix: '/api' // 所有API路由都以/api开头
});

/**
 * 健康检查端点
 */
router.get('/health', asyncHandler(async (ctx: Context) => {
  const healthInfo = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: config.app.version,
    environment: config.app.env,
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    database: 'connected' // 后续可以添加数据库健康检查
  };

  ctx.apiResponse.success(healthInfo);
}));

/**
 * API信息端点
 */
router.get('/info', asyncHandler(async (ctx: Context) => {
  const apiInfo = {
    name: config.app.name,
    version: config.app.version,
    description: '期末复习平台API服务',
    environment: config.app.env,
    endpoints: {
      health: '/api/health',
      subjects: '/api/subjects',
      files: '/api/files',
      admin: '/api/admin'
    },
    documentation: '/api/docs', // 预留文档端点
    timestamp: new Date().toISOString()
  };

  ctx.apiResponse.success(apiInfo);
}));

/**
 * 根路径重定向
 */
router.get('/', asyncHandler(async (ctx: Context) => {
  ctx.redirect('/api/info');
}));

// 注册子路由
router.use('/subjects', subjectsRouter.routes(), subjectsRouter.allowedMethods());
router.use('/files', filesRouter.routes(), filesRouter.allowedMethods());
router.use('/admin', adminRouter.routes(), adminRouter.allowedMethods());

// 导出路由
export default router;