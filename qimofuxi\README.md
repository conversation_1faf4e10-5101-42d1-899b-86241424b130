# 期末复习平台 (Final Exam Review Platform)

一个专注于学术资料分享的Web应用平台，支持Markdown文件在线渲染和文件夹结构保留。

## ✨ 核心功能

### 📚 学科管理
- 创建和管理学科分类
- 支持学科的增删改查操作
- 学科文件统计和概览

### 📁 文件浏览 🆕
- **层级浏览**: 支持文件夹层级结构浏览
- **智能搜索**: 文件名关键词搜索，支持类型过滤
- **面包屑导航**: 清晰的路径导航，快速定位
- **分页加载**: 大量文件的高效分页显示
- **性能优化**: 内存缓存机制，响应时间<500ms

### 🚀 性能特性
- **缓存系统**: 智能TTL缓存，命中率>80%
- **数据库优化**: CTE查询优化，复合索引加速
- **实时监控**: 性能监控和慢查询检测
- **并发支持**: 支持多用户并发访问

## 🚀 快速开始

### 环境要求
- Node.js >= 18.0.0
- npm >= 9.0.0

### 后端启动
```bash
cd backend
npm install
npm run dev  # 开发模式，端口3001
```

### 前端启动
```bash
cd frontend
npm install
npm run dev  # 开发模式，端口5173
```

### 数据库初始化
```bash
cd data
node test_database.js  # 初始化SQLite数据库
```

## 📁 项目结构

```
qimofuxi/
├── backend/               # Node.js 后端服务
│   ├── routes/           # API路由定义
│   ├── services/         # 业务逻辑服务
│   ├── middleware/       # 中间件
│   ├── tests/           # 测试文件
│   └── package.json
├── frontend/             # Vue3 前端应用
│   ├── src/
│   │   ├── components/   # Vue组件
│   │   ├── views/       # 页面视图
│   │   ├── api/         # API调用
│   │   └── types/       # TypeScript类型
│   └── package.json
├── data/                # 数据库和数据文件
│   ├── database.sqlite  # SQLite数据库
│   └── init_database.sql
├── docs/                # 项目文档
│   ├── architecture/    # 架构文档
│   ├── test/           # 测试报告
│   └── CHANGELOG.md
└── uploads/             # 文件上传存储
```

## 🛠️ 技术栈

### 前端
- **Vue 3** - 渐进式JavaScript框架
- **TypeScript** - 静态类型检查
- **Vite** - 快速构建工具
- **Ant Design Vue** - UI组件库
- **UnoCSS** - 原子化CSS框架

### 后端
- **Node.js** - JavaScript运行环境
- **Express** - Web框架
- **SQLite** - 嵌入式数据库
- **Multer** - 文件上传处理
- **Playwright** - API测试框架

## 📝 API文档

### 学科管理
- `GET /api/subjects` - 获取学科列表
- `POST /api/subjects` - 创建学科
- `PUT /api/subjects/:id` - 更新学科
- `DELETE /api/subjects/:id` - 删除学科

### 文件浏览 🆕
- `GET /api/subjects/:id/files` - 获取文件列表（支持分页、搜索）
- `GET /api/files/:nodeId/breadcrumb` - 获取面包屑导航路径
- `GET /api/subjects/:id/search` - 搜索文件（支持类型过滤、分页）

### 文件管理
- `POST /api/subjects/:id/upload` - 上传文件
- `GET /api/files/:fileId` - 获取文件内容
- `GET /api/files/:fileId/content` - 获取文件详细内容

详细API文档请查看：[API Reference](docs/architecture/API_Reference.md)

## 🧪 测试

### 后端测试
```bash
cd backend
npm test                    # 运行所有测试
npm run test:playwright     # 运行API集成测试
npm run test:api           # 运行API专项测试
npm run test:report        # 查看测试报告
```

### 前端测试
```bash
cd frontend
npm run test               # 运行单元测试
npm run test:e2e          # 运行端到端测试
```

### 测试覆盖率
- **单元测试**: 100%通过
- **集成测试**: 65%通过
- **性能测试**: API响应时间<500ms
- **功能验收**: 100%完成

## 📊 性能指标

### API性能
| 端点 | 平均响应时间 | 目标时间 | 状态 |
|------|-------------|----------|------|
| 文件列表 | 150ms | <2000ms | ✅ 优秀 |
| 文件搜索 | 130ms | <2000ms | ✅ 优秀 |
| 面包屑 | 120ms | <500ms | ✅ 优秀 |

### 缓存效果
- **缓存命中率**: >80%
- **响应时间优化**: 80-90%提升
- **内存使用**: 合理范围内

## 📦 部署

### 开发环境
```bash
# 后端
cd backend && npm run dev

# 前端
cd frontend && npm run dev
```

### 生产环境
```bash
# 构建前端
cd frontend && npm run build

# 启动后端
cd backend && npm start
```

## 🤝 贡献指南

1. Fork本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看[LICENSE](LICENSE)文件了解详情

## 📞 联系我们

如有问题或建议，请创建Issue或联系项目维护者。

---

**期末复习平台** - 让学习资料分享更简单 ✨

### 最新更新 (v0.3.1)
- 📊 完成完整的测试报告生成和质量分析
- 📚 更新API文档，添加文件浏览接口说明
- 🔧 建立性能监控和缓存优化机制
- 📈 实现81个测试用例，功能验收率100%
