/**
 * 文件业务逻辑层 (Service)
 * 负责文件相关的业务逻辑处理
 * 
 * <AUTHOR> (Engineer)
 * @date 2025-01-27
 */

import { fileDao } from '../dao/fileDao';
import { subjectService } from './subjectService';
import { FileNode } from '../types';
import * as path from 'path';

export interface FileWithContent extends FileNode {
  content?: string;
}

export interface FileSearchResult {
  query: string;
  subjectId?: number;
  total: number;
  results: FileNode[];
}

export class FileService {
  /**
   * 获取学科的文件树结构
   * @param subjectId 学科ID
   * @returns 文件树结构
   */
  async getSubjectFileTree(subjectId: number): Promise<FileNode[]> {
    try {
      // 参数验证
      if (!subjectId || subjectId <= 0) {
        throw new Error('学科ID必须是有效的正整数');
      }
      
      // 验证学科是否存在
      const isValidSubject = await subjectService.isValidSubjectId(subjectId);
      if (!isValidSubject) {
        throw new Error('学科不存在');
      }
      
      return fileDao.getFileTree(subjectId);
    } catch (error) {
      console.error('FileService.getSubjectFileTree error:', error);
      throw error;
    }
  }

  /**
   * 获取文件内容
   * @param fileId 文件ID
   * @returns 文件信息和内容
   */
  async getFileContent(fileId: number): Promise<FileWithContent> {
    try {
      // 参数验证
      if (!fileId || fileId <= 0) {
        throw new Error('文件ID必须是有效的正整数');
      }
      
      // 获取文件信息
      const fileNode = fileDao.findById(fileId);
      if (!fileNode) {
        throw new Error('文件不存在');
      }
      
      // 只有文件类型才能获取内容
      if (fileNode.type !== 'file') {
        throw new Error('只能获取文件类型的内容');
      }
      
      try {
        // 尝试读取文件内容
        const content = fileDao.getFileContent(fileNode);
        return {
          ...fileNode,
          content
        };
      } catch (contentError) {
        // 如果文件不存在，返回模拟内容
        console.warn(`File not found on disk: ${fileNode.path}, returning mock content`);
        const mockContent = this.generateMockContent(fileNode);
        return {
          ...fileNode,
          content: mockContent
        };
      }
    } catch (error) {
      console.error('FileService.getFileContent error:', error);
      throw error;
    }
  }

  /**
   * 获取文件元数据
   * @param fileId 文件ID
   * @returns 文件元数据
   */
  async getFileMetadata(fileId: number): Promise<FileNode> {
    try {
      // 参数验证
      if (!fileId || fileId <= 0) {
        throw new Error('文件ID必须是有效的正整数');
      }
      
      const fileNode = fileDao.findById(fileId);
      if (!fileNode) {
        throw new Error('文件不存在');
      }
      
      return fileNode;
    } catch (error) {
      console.error('FileService.getFileMetadata error:', error);
      throw error;
    }
  }

  /**
   * 搜索文件
   * @param keyword 搜索关键词
   * @param subjectId 学科ID，可选
   * @param fileType 文件类型，可选
   * @returns 搜索结果
   */
  async searchFiles(keyword: string, subjectId?: number, fileType?: 'file' | 'folder'): Promise<FileSearchResult> {
    try {
      // 参数验证
      if (!keyword || keyword.trim().length === 0) {
        throw new Error('搜索关键词不能为空');
      }
      
      if (keyword.trim().length < 2) {
        throw new Error('搜索关键词至少需要2个字符');
      }
      
      // 验证学科ID（如果提供）
      if (subjectId !== undefined) {
        if (subjectId <= 0) {
          throw new Error('学科ID必须是有效的正整数');
        }
        
        const isValidSubject = await subjectService.isValidSubjectId(subjectId);
        if (!isValidSubject) {
          throw new Error('学科不存在');
        }
      }
      
      const results = fileDao.search(keyword.trim(), subjectId, fileType);
      
      return {
        query: keyword.trim(),
        subjectId,
        total: results.length,
        results
      };
    } catch (error) {
      console.error('FileService.searchFiles error:', error);
      throw error;
    }
  }

  /**
   * 创建文件节点
   * @param fileData 文件数据
   * @returns 创建的文件节点ID
   */
  async createFileNode(fileData: Omit<FileNode, 'id' | 'created_at' | 'updated_at'>): Promise<number> {
    try {
      // 参数验证
      if (!fileData.name || fileData.name.trim().length === 0) {
        throw new Error('文件名不能为空');
      }
      
      if (!fileData.subject_id || fileData.subject_id <= 0) {
        throw new Error('学科ID必须是有效的正整数');
      }
      
      if (!['file', 'folder'].includes(fileData.type)) {
        throw new Error('文件类型必须是file或folder');
      }
      
      // 验证学科是否存在
      const isValidSubject = await subjectService.isValidSubjectId(fileData.subject_id);
      if (!isValidSubject) {
        throw new Error('学科不存在');
      }
      
      // 验证父节点（如果提供）
      if (fileData.parent_id !== null && fileData.parent_id !== undefined) {
        const parentNode = fileDao.findById(fileData.parent_id);
        if (!parentNode) {
          throw new Error('父节点不存在');
        }
        if (parentNode.type !== 'folder') {
          throw new Error('父节点必须是文件夹类型');
        }
        if (parentNode.subject_id !== fileData.subject_id) {
          throw new Error('父节点必须属于同一学科');
        }
      }
      
      // 检查同级目录下是否有重名文件
      const existingFile = fileDao.findByPath(fileData.subject_id, fileData.path);
      if (existingFile) {
        throw new Error('文件路径已存在');
      }
      
      return fileDao.create({
        ...fileData,
        name: fileData.name.trim(),
        path: fileData.path.trim()
      });
    } catch (error) {
      console.error('FileService.createFileNode error:', error);
      throw error;
    }
  }

  /**
   * 更新文件节点
   * @param fileId 文件ID
   * @param updates 更新的字段
   * @returns 是否更新成功
   */
  async updateFileNode(fileId: number, updates: Partial<Omit<FileNode, 'id' | 'created_at' | 'updated_at'>>): Promise<boolean> {
    try {
      // 参数验证
      if (!fileId || fileId <= 0) {
        throw new Error('文件ID必须是有效的正整数');
      }
      
      if (Object.keys(updates).length === 0) {
        throw new Error('更新数据不能为空');
      }
      
      // 检查文件是否存在
      const existingFile = fileDao.findById(fileId);
      if (!existingFile) {
        throw new Error('文件不存在');
      }
      
      // 验证更新字段
      if (updates.name !== undefined) {
        if (!updates.name || updates.name.trim().length === 0) {
          throw new Error('文件名不能为空');
        }
        updates.name = updates.name.trim();
      }
      
      if (updates.path !== undefined) {
        updates.path = updates.path.trim();
        
        // 检查新路径是否与其他文件冲突
        const conflictFile = fileDao.findByPath(existingFile.subject_id, updates.path);
        if (conflictFile && conflictFile.id !== fileId) {
          throw new Error('文件路径已存在');
        }
      }
      
      return fileDao.update(fileId, updates);
    } catch (error) {
      console.error('FileService.updateFileNode error:', error);
      throw error;
    }
  }

  /**
   * 删除文件节点（软删除）
   * @param fileId 文件ID
   * @returns 是否删除成功
   */
  async deleteFileNode(fileId: number): Promise<boolean> {
    try {
      // 参数验证
      if (!fileId || fileId <= 0) {
        throw new Error('文件ID必须是有效的正整数');
      }
      
      // 检查文件是否存在
      const existingFile = fileDao.findById(fileId);
      if (!existingFile) {
        throw new Error('文件不存在');
      }
      
      return fileDao.softDelete(fileId);
    } catch (error) {
      console.error('FileService.deleteFileNode error:', error);
      throw error;
    }
  }

  /**
   * 生成模拟文件内容
   * @param fileNode 文件节点
   * @returns 模拟内容
   */
  private generateMockContent(fileNode: FileNode): string {
    const extension = path.extname(fileNode.name).toLowerCase();
    
    switch (extension) {
      case '.md':
        return `# ${fileNode.name.replace('.md', '')}

这是一个示例Markdown文件内容。

## 主要概念

1. **概念一**: 这是第一个重要概念的说明
2. **概念二**: 这是第二个重要概念的说明

## 公式示例

数学公式: $E = mc^2$

## 代码示例

\`\`\`javascript
function hello() {
  console.log('Hello, World!');
}
\`\`\`

## 总结

这是本章的总结内容。

---
*文件路径: ${fileNode.path}*
*创建时间: ${fileNode.created_at}*`;

      case '.txt':
        return `${fileNode.name.replace('.txt', '')}

这是一个文本文件的示例内容。

文件信息:
- 路径: ${fileNode.path}
- 大小: ${fileNode.size} 字节
- 创建时间: ${fileNode.created_at}

内容正文...`;

      default:
        return `文件名: ${fileNode.name}
文件路径: ${fileNode.path}
文件类型: ${fileNode.type}
文件大小: ${fileNode.size} 字节
创建时间: ${fileNode.created_at}

这是一个示例文件内容。`;
    }
  }
}

// 导出单例实例
export const fileService = new FileService();