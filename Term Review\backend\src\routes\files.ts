import Router from 'koa-router'
import { FileController } from '../controllers/FileController.js'

const router = new Router()

// 文件浏览路由
// GET /api/subjects/:id/files - 获取指定学科下的文件列表
router.get('/subjects/:id/files', FileController.getFiles)

// GET /api/subjects/:id/search - 搜索指定学科下的文件
router.get('/subjects/:id/search', FileController.searchFiles)

// GET /api/files/:id/breadcrumb - 获取面包屑导航路径
router.get('/files/:id/breadcrumb', FileController.getBreadcrumb)

export default router
