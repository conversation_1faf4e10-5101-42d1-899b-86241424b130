import Database from 'better-sqlite3'
import path from 'path'
import fs from 'fs'

/**
 * 测试数据管理工具
 * 负责测试数据的准备、清理和隔离
 */

// 测试数据库路径
const TEST_DB_PATH = path.join(process.cwd(), 'data', 'test_platform.db')
const BACKUP_DB_PATH = path.join(process.cwd(), 'data', 'platform.db')

export class TestDataManager {
  private db: Database.Database | null = null

  /**
   * 初始化测试数据库
   */
  async initTestDatabase(): Promise<void> {
    try {
      // 确保数据目录存在
      const dataDir = path.dirname(TEST_DB_PATH)
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true })
      }

      // 如果测试数据库已存在，删除它
      if (fs.existsSync(TEST_DB_PATH)) {
        fs.unlinkSync(TEST_DB_PATH)
      }

      // 复制生产数据库作为测试基础
      if (fs.existsSync(BACKUP_DB_PATH)) {
        fs.copyFileSync(BACKUP_DB_PATH, TEST_DB_PATH)
      }

      // 连接测试数据库
      this.db = new Database(TEST_DB_PATH)

      console.log('✅ 测试数据库初始化完成')
    } catch (error) {
      console.error('❌ 测试数据库初始化失败:', error)
      throw error
    }
  }

  /**
   * 清理测试数据库
   */
  async cleanupTestDatabase(): Promise<void> {
    try {
      if (this.db) {
        this.db.close()
        this.db = null
      }

      if (fs.existsSync(TEST_DB_PATH)) {
        fs.unlinkSync(TEST_DB_PATH)
      }

      console.log('✅ 测试数据库清理完成')
    } catch (error) {
      console.error('❌ 测试数据库清理失败:', error)
    }
  }

  /**
   * 创建数据库表结构
   */
  private createTables(): void {
    if (!this.db) return

    // 创建学科表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS subjects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        icon TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `)

    // 创建文件节点表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS file_nodes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        subject_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('file', 'folder')),
        path TEXT NOT NULL,
        parent_path TEXT,
        content TEXT,
        size INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (subject_id) REFERENCES subjects (id) ON DELETE CASCADE
      )
    `)

    // 创建索引
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_file_nodes_subject_id ON file_nodes (subject_id)
    `)
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_file_nodes_path ON file_nodes (path)
    `)
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_file_nodes_parent_path ON file_nodes (parent_path)
    `)
  }

  /**
   * 重置测试数据
   */
  async resetTestData(): Promise<void> {
    if (!this.db) {
      throw new Error('测试数据库未初始化')
    }

    try {
      // 创建表结构（如果不存在）
      this.createTables()

      // 清空现有数据
      this.db.exec('DELETE FROM file_nodes')
      this.db.exec('DELETE FROM subjects')

      // 插入测试学科数据
      const insertSubject = this.db.prepare(`
        INSERT INTO subjects (id, name, description, icon, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?)
      `)

      const now = new Date().toISOString()

      insertSubject.run(1, '测试数学', '数学测试学科', '📐', now, now)
      insertSubject.run(2, '测试计算机', '计算机测试学科', '💻', now, now)
      insertSubject.run(3, '测试英语', '英语测试学科', '🇬🇧', now, now)

      // 插入测试文件数据
      const insertFile = this.db.prepare(`
        INSERT INTO file_nodes (id, subject_id, name, type, content, parent_path, path, size, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)

      // 数学学科文件
      insertFile.run(1, 1, '基础数学', 'folder', null, null, '/基础数学', 0, now, now)
      insertFile.run(2, 1, '函数概念.md', 'file', '# 函数概念\n\n函数是数学中的基本概念...', 1, '/基础数学/函数概念.md', 256, now, now)
      insertFile.run(3, 1, '极限理论.md', 'file', '# 极限理论\n\n极限是微积分的基础...', 1, '/基础数学/极限理论.md', 312, now, now)

      // 计算机学科文件
      insertFile.run(4, 2, '数据结构', 'folder', null, null, '/数据结构', 0, now, now)
      insertFile.run(5, 2, '链表.md', 'file', '# 链表\n\n链表是一种线性数据结构...', 4, '/数据结构/链表.md', 428, now, now)
      insertFile.run(6, 2, '树结构.md', 'file', '# 树结构\n\n树是一种分层数据结构...', 4, '/数据结构/树结构.md', 512, now, now)

      // 英语学科文件
      insertFile.run(7, 3, '语法基础', 'folder', null, null, '/语法基础', 0, now, now)
      insertFile.run(8, 3, '时态.md', 'file', '# 英语时态\n\n英语有多种时态形式...', 7, '/语法基础/时态.md', 384, now, now)

      console.log('✅ 测试数据重置完成')
    } catch (error) {
      console.error('❌ 测试数据重置失败:', error)
      throw error
    }
  }

  /**
   * 获取测试学科数据
   */
  getTestSubjects() {
    return [
      { id: 1, name: '测试数学', description: '数学测试学科', icon: '📐' },
      { id: 2, name: '测试计算机', description: '计算机测试学科', icon: '💻' },
      { id: 3, name: '测试英语', description: '英语测试学科', icon: '🇬🇧' }
    ]
  }

  /**
   * 获取测试文件数据
   */
  getTestFiles() {
    return [
      { id: 2, name: '函数概念.md', type: 'file', subject_id: 1 },
      { id: 3, name: '极限理论.md', type: 'file', subject_id: 1 },
      { id: 5, name: '链表.md', type: 'file', subject_id: 2 },
      { id: 6, name: '树结构.md', type: 'file', subject_id: 2 },
      { id: 8, name: '时态.md', type: 'file', subject_id: 3 }
    ]
  }
}

// 导出单例实例
export const testDataManager = new TestDataManager()
