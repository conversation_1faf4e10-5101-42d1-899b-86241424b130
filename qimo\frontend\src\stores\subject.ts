import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { subjectApi } from '@/utils/api'
import type { Subject, FileNode } from '@/types/api'

export const useSubjectStore = defineStore('subject', () => {
  // 状态
  const subjects = ref<Subject[]>([])
  const currentSubject = ref<Subject | null>(null)
  const subjectFiles = ref<FileNode[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 缓存状态
  const subjectsCache = ref<{ data: Subject[], timestamp: number } | null>(null)
  const subjectFilesCache = ref<Map<number, { data: FileNode[], timestamp: number }>>(new Map())
  const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

  // 计算属性
  const subjectCount = computed(() => subjects.value.length)
  const hasSubjects = computed(() => subjects.value.length > 0)
  const isLoading = computed(() => loading.value)
  const hasError = computed(() => !!error.value)

  // 根据ID获取学科
  const getSubjectById = computed(() => {
    return (id: number) => subjects.value.find(subject => subject.id === id)
  })

  // 方法
  const setLoading = (value: boolean) => {
    loading.value = value
  }

  const setError = (message: string | null) => {
    error.value = message
  }

  const clearError = () => {
    error.value = null
  }

  // 检查缓存是否有效
  const isCacheValid = (timestamp: number) => {
    return Date.now() - timestamp < CACHE_DURATION
  }

  // 获取所有学科
  const fetchSubjects = async (includeStats = false, forceRefresh = false) => {
    try {
      // 检查缓存
      if (!forceRefresh && subjectsCache.value && isCacheValid(subjectsCache.value.timestamp)) {
        console.log('📦 Using cached subjects data')
        subjects.value = subjectsCache.value.data
        return subjectsCache.value.data
      }

      setLoading(true)
      clearError()

      const response = await subjectApi.getSubjects(includeStats)
      const data = response.data.data

      // 更新状态和缓存
      subjects.value = data
      subjectsCache.value = {
        data,
        timestamp: Date.now()
      }

      console.log('🔄 Fetched fresh subjects data')
      return data
    } catch (err: any) {
      const message = err?.response?.data?.error?.message || '获取学科列表失败'
      setError(message)
      throw err
    } finally {
      setLoading(false)
    }
  }

  // 获取学科详情
  const fetchSubjectDetail = async (id: number) => {
    try {
      setLoading(true)
      clearError()

      const response = await subjectApi.getSubjectById(id)
      currentSubject.value = response.data.data

      return response.data.data
    } catch (err: any) {
      const message = err?.response?.data?.error?.message || '获取学科详情失败'
      setError(message)
      throw err
    } finally {
      setLoading(false)
    }
  }

  // 获取学科文件结构
  const fetchSubjectFiles = async (id: number, forceRefresh = false) => {
    try {
      // 检查缓存
      const cached = subjectFilesCache.value.get(id)
      if (!forceRefresh && cached && isCacheValid(cached.timestamp)) {
        console.log(`📦 Using cached files data for subject ${id}`)
        subjectFiles.value = cached.data
        return cached.data
      }

      setLoading(true)
      clearError()

      const response = await subjectApi.getSubjectFiles(id)
      const data = response.data.data

      // 更新状态和缓存
      subjectFiles.value = data
      subjectFilesCache.value.set(id, {
        data,
        timestamp: Date.now()
      })

      console.log(`🔄 Fetched fresh files data for subject ${id}`)
      return data
    } catch (err: any) {
      const message = err?.response?.data?.error?.message || '获取文件结构失败'
      setError(message)
      throw err
    } finally {
      setLoading(false)
    }
  }

  // 设置当前学科
  const setCurrentSubject = (subject: Subject | null) => {
    currentSubject.value = subject
  }

  // 清空缓存
  const clearCache = () => {
    subjectsCache.value = null
    subjectFilesCache.value.clear()
    console.log('🗑️ Cache cleared')
  }

  // 清空数据
  const clearData = () => {
    subjects.value = []
    currentSubject.value = null
    subjectFiles.value = []
    clearError()
  }

  // 清空所有数据和缓存
  const clearAll = () => {
    clearData()
    clearCache()
  }

  return {
    // 状态
    subjects,
    currentSubject,
    subjectFiles,
    loading,
    error,

    // 计算属性
    subjectCount,
    hasSubjects,
    isLoading,
    hasError,
    getSubjectById,

    // 方法
    setLoading,
    setError,
    clearError,
    fetchSubjects,
    fetchSubjectDetail,
    fetchSubjectFiles,
    setCurrentSubject,
    clearData,
    clearCache,
    clearAll
  }
})
