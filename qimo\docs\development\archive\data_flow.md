# 前后端数据流文档

## 概述

本文档描述期末复习平台前后端数据联调的完整流程，包括API调用、状态管理、缓存策略和错误处理机制。

## 技术架构

### 后端架构
- **框架**: Node.js + Koa + TypeScript
- **数据库**: SQLite + better-sqlite3
- **架构模式**: DAO + Service + Controller 三层架构
- **API格式**: RESTful API，统一响应格式

### 前端架构
- **框架**: Vue3 + TypeScript + Vite
- **状态管理**: Pinia
- **HTTP客户端**: Axios
- **UI组件**: Ant Design Vue

## API接口设计

### 统一响应格式
```typescript
interface ApiResponse<T = any> {
  success: boolean
  data: T
  error?: {
    code: string
    message: string
  }
  timestamp: string
  requestId: string
}
```

### 核心接口

#### 1. 学科相关接口
- `GET /api/subjects` - 获取所有学科列表
- `GET /api/subjects/:id` - 获取学科详情
- `GET /api/subjects/:id/files` - 获取学科文件结构

#### 2. 文件相关接口
- `GET /api/files/:id` - 获取文件内容
- `GET /api/files/:id/metadata` - 获取文件元数据
- `GET /api/files/search` - 搜索文件

## 前端状态管理

### Subject Store (学科状态)
```typescript
interface SubjectState {
  subjects: Subject[]           // 学科列表
  currentSubject: Subject | null // 当前学科
  subjectFiles: FileNode[]      // 学科文件结构
  loading: boolean              // 加载状态
  error: string | null          // 错误信息
}
```

### File Store (文件状态)
```typescript
interface FileState {
  currentFile: FileWithContent | null // 当前文件
  searchResults: SearchResult[]       // 搜索结果
  recentFiles: FileNode[]            // 最近访问文件
  loading: boolean                   // 加载状态
  error: string | null               // 错误信息
}
```

## 数据流程

### 1. 学科列表页面流程
```
用户访问 → SubjectListView.vue → useSubjectStore.fetchSubjects() 
→ subjectApi.getSubjects() → 后端 /api/subjects → 返回学科列表 
→ 更新 subjects 状态 → 页面渲染学科卡片
```

### 2. 学科详情页面流程
```
用户点击学科 → SubjectDetailView.vue → useSubjectStore.fetchSubjectDetail() 
→ subjectApi.getSubjectById() → 后端 /api/subjects/:id → 返回学科详情
→ useSubjectStore.fetchSubjectFiles() → subjectApi.getSubjectFiles() 
→ 后端 /api/subjects/:id/files → 返回文件树 → 渲染文件树组件
```

### 3. 文件查看页面流程
```
用户点击文件 → FileViewerView.vue → useFileStore.fetchFileContent() 
→ fileApi.getFileContent() → 后端 /api/files/:id → 返回文件内容 
→ 更新 currentFile 状态 → Markdown渲染 → 添加到最近访问
```

### 4. 搜索功能流程
```
用户输入搜索词 → SearchView.vue → useFileStore.searchFiles() 
→ fileApi.searchFiles() → 后端 /api/files/search → 返回搜索结果 
→ 更新 searchResults 状态 → 渲染搜索结果列表
```

## 缓存策略

### 缓存配置
- **缓存时长**: 5分钟
- **缓存类型**: 内存缓存（Map结构）
- **缓存键**: 基于请求参数生成

### 缓存实现

#### Subject Store 缓存
- `subjectsCache`: 学科列表缓存
- `subjectFilesCache`: 学科文件结构缓存（按学科ID分组）

#### File Store 缓存
- `fileCache`: 文件内容缓存（按文件ID分组）
- `searchCache`: 搜索结果缓存（按搜索条件分组）

### 缓存策略
1. **读取优先**: 优先从缓存读取，缓存失效时请求API
2. **强制刷新**: 提供forceRefresh参数跳过缓存
3. **自动清理**: 提供clearCache方法手动清理缓存

## 错误处理

### HTTP错误处理
- **网络错误**: 显示"网络连接失败"
- **400错误**: 显示"请求参数错误"
- **401错误**: 显示"未授权访问"
- **403错误**: 显示"禁止访问"
- **404错误**: 显示"请求的资源不存在"
- **500错误**: 显示"服务器内部错误"

### 重试机制
- **重试条件**: 网络错误或5xx服务器错误
- **重试次数**: 最多3次
- **重试延迟**: 递增延迟（1秒、2秒、3秒）

### 业务错误处理
- **统一错误格式**: 通过ApiResponse.error传递
- **用户友好提示**: 将技术错误转换为用户可理解的信息
- **错误状态管理**: 在Store中统一管理错误状态

## 性能优化

### 1. 请求优化
- **请求合并**: 避免重复请求
- **请求取消**: 组件卸载时取消未完成的请求
- **超时设置**: 10秒请求超时

### 2. 数据优化
- **懒加载**: 按需加载文件内容
- **分页加载**: 大量数据分页处理
- **数据压缩**: 减少传输数据量

### 3. 缓存优化
- **智能缓存**: 根据数据更新频率调整缓存策略
- **缓存预热**: 预加载常用数据
- **缓存清理**: 定期清理过期缓存

## 用户体验优化

### 1. 加载状态
- **全局Loading**: 页面级加载状态
- **局部Loading**: 组件级加载状态
- **骨架屏**: 内容加载时的占位符

### 2. 错误反馈
- **Toast提示**: 操作结果即时反馈
- **错误页面**: 友好的错误页面
- **重试按钮**: 允许用户重试失败操作

### 3. 交互优化
- **防抖处理**: 搜索输入防抖
- **节流处理**: 滚动事件节流
- **快捷键**: 键盘快捷键支持

## 测试策略

### 1. 单元测试
- **Store测试**: 状态管理逻辑测试
- **API测试**: 接口调用测试
- **工具函数测试**: 纯函数测试

### 2. 集成测试
- **组件测试**: 组件与Store集成测试
- **页面测试**: 完整页面功能测试
- **API集成测试**: 前后端接口集成测试

### 3. E2E测试
- **用户流程测试**: 完整用户操作流程
- **跨浏览器测试**: 多浏览器兼容性测试
- **性能测试**: 页面加载性能测试

## 部署配置

### 1. 环境配置
- **开发环境**: 本地开发配置
- **测试环境**: 测试服务器配置
- **生产环境**: 生产服务器配置

### 2. API配置
- **代理配置**: 开发环境API代理
- **域名配置**: 不同环境API域名
- **CORS配置**: 跨域请求配置

### 3. 监控配置
- **错误监控**: 前端错误收集
- **性能监控**: 页面性能监控
- **用户行为监控**: 用户操作统计

## 总结

前后端数据联调是整个应用的核心，通过合理的架构设计、完善的错误处理、有效的缓存策略和良好的用户体验，确保了应用的稳定性和可用性。本文档将作为团队开发和维护的重要参考。
