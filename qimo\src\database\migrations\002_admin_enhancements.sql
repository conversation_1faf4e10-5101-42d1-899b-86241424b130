-- 管理员功能增强数据库迁移
-- 创建时间: 2025-07-27
-- 版本: v1.1
-- 作者: <PERSON> (Engineer)
-- 目的: 为subjects表添加统计字段和自动更新触发器，支持管理员学科管理功能

-- 为subjects表添加统计字段
ALTER TABLE subjects ADD COLUMN file_count INTEGER DEFAULT 0;
ALTER TABLE subjects ADD COLUMN total_size INTEGER DEFAULT 0;

-- 创建函数：更新学科统计信息
-- 注意：SQLite不支持存储过程，使用触发器实现自动更新

-- 创建触发器：当file_nodes表有变化时自动更新subjects表的统计信息
-- 插入文件节点时更新统计
CREATE TRIGGER IF NOT EXISTS trigger_update_subject_stats_insert
    AFTER INSERT ON file_nodes
    FOR EACH ROW
    WHEN NEW.type = 'file' AND NEW.is_deleted = 0
BEGIN
    UPDATE subjects 
    SET 
        file_count = (
            SELECT COUNT(*) 
            FROM file_nodes 
            WHERE subject_id = NEW.subject_id 
            AND type = 'file' 
            AND is_deleted = 0
        ),
        total_size = (
            SELECT COALESCE(SUM(size), 0) 
            FROM file_nodes 
            WHERE subject_id = NEW.subject_id 
            AND type = 'file' 
            AND is_deleted = 0
        )
    WHERE id = NEW.subject_id;
END;

-- 更新文件节点时更新统计
CREATE TRIGGER IF NOT EXISTS trigger_update_subject_stats_update
    AFTER UPDATE ON file_nodes
    FOR EACH ROW
    WHEN (OLD.type = 'file' OR NEW.type = 'file') 
    AND (OLD.is_deleted != NEW.is_deleted OR OLD.size != NEW.size OR OLD.subject_id != NEW.subject_id)
BEGIN
    -- 更新旧学科的统计（如果学科ID发生变化）
    UPDATE subjects 
    SET 
        file_count = (
            SELECT COUNT(*) 
            FROM file_nodes 
            WHERE subject_id = OLD.subject_id 
            AND type = 'file' 
            AND is_deleted = 0
        ),
        total_size = (
            SELECT COALESCE(SUM(size), 0) 
            FROM file_nodes 
            WHERE subject_id = OLD.subject_id 
            AND type = 'file' 
            AND is_deleted = 0
        )
    WHERE id = OLD.subject_id AND OLD.subject_id != NEW.subject_id;
    
    -- 更新新学科的统计
    UPDATE subjects 
    SET 
        file_count = (
            SELECT COUNT(*) 
            FROM file_nodes 
            WHERE subject_id = NEW.subject_id 
            AND type = 'file' 
            AND is_deleted = 0
        ),
        total_size = (
            SELECT COALESCE(SUM(size), 0) 
            FROM file_nodes 
            WHERE subject_id = NEW.subject_id 
            AND type = 'file' 
            AND is_deleted = 0
        )
    WHERE id = NEW.subject_id;
END;

-- 删除文件节点时更新统计
CREATE TRIGGER IF NOT EXISTS trigger_update_subject_stats_delete
    AFTER DELETE ON file_nodes
    FOR EACH ROW
    WHEN OLD.type = 'file'
BEGIN
    UPDATE subjects 
    SET 
        file_count = (
            SELECT COUNT(*) 
            FROM file_nodes 
            WHERE subject_id = OLD.subject_id 
            AND type = 'file' 
            AND is_deleted = 0
        ),
        total_size = (
            SELECT COALESCE(SUM(size), 0) 
            FROM file_nodes 
            WHERE subject_id = OLD.subject_id 
            AND type = 'file' 
            AND is_deleted = 0
        )
    WHERE id = OLD.subject_id;
END;

-- 初始化现有数据的统计信息
UPDATE subjects 
SET 
    file_count = (
        SELECT COUNT(*) 
        FROM file_nodes 
        WHERE subject_id = subjects.id 
        AND type = 'file' 
        AND is_deleted = 0
    ),
    total_size = (
        SELECT COALESCE(SUM(size), 0) 
        FROM file_nodes 
        WHERE subject_id = subjects.id 
        AND type = 'file' 
        AND is_deleted = 0
    );

-- 创建索引优化统计查询性能
CREATE INDEX IF NOT EXISTS idx_file_nodes_subject_type_deleted ON file_nodes(subject_id, type, is_deleted);
CREATE INDEX IF NOT EXISTS idx_subjects_file_count ON subjects(file_count);
CREATE INDEX IF NOT EXISTS idx_subjects_total_size ON subjects(total_size);

-- 验证迁移是否成功
-- 可以通过以下查询验证统计信息是否正确：
-- SELECT s.id, s.name, s.file_count, s.total_size,
--        (SELECT COUNT(*) FROM file_nodes WHERE subject_id = s.id AND type = 'file' AND is_deleted = 0) as actual_file_count,
--        (SELECT COALESCE(SUM(size), 0) FROM file_nodes WHERE subject_id = s.id AND type = 'file' AND is_deleted = 0) as actual_total_size
-- FROM subjects s;
