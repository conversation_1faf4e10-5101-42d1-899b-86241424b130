-- 期末复习平台初始数据库结构
-- 创建时间: 2025-01-27
-- 版本: v1.0
-- 作者: <PERSON> (Engineer)

-- 学科表：存储所有学科信息
CREATE TABLE IF NOT EXISTS subjects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,                    -- 学科名称，唯一约束
    description TEXT DEFAULT '',                  -- 学科描述（预留字段）
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    status INTEGER DEFAULT 1,                     -- 状态：1=启用，0=禁用（预留字段）
    sort_order INTEGER DEFAULT 0                  -- 排序权重（预留字段）
);

-- 文件节点表：存储文件和文件夹的树形结构
CREATE TABLE IF NOT EXISTS file_nodes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    subject_id INTEGER NOT NULL,                  -- 所属学科ID
    parent_id INTEGER DEFAULT NULL,               -- 父节点ID，NULL表示根节点
    name TEXT NOT NULL,                           -- 文件/文件夹名称
    type TEXT NOT NULL CHECK (type IN ('file', 'folder')), -- 节点类型
    path TEXT NOT NULL,                           -- 相对路径
    size INTEGER DEFAULT 0,                       -- 文件大小（字节），文件夹为0
    mime_type TEXT DEFAULT '',                    -- MIME类型（预留字段）
    content_hash TEXT DEFAULT '',                 -- 内容哈希值（预留字段）
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_deleted INTEGER DEFAULT 0,                 -- 软删除标记（预留字段）
    
    -- 外键约束
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES file_nodes(id) ON DELETE CASCADE
);

-- 创建索引以优化查询性能
-- 学科表索引
CREATE INDEX IF NOT EXISTS idx_subjects_name ON subjects(name);
CREATE INDEX IF NOT EXISTS idx_subjects_status ON subjects(status);
CREATE INDEX IF NOT EXISTS idx_subjects_created_at ON subjects(created_at);

-- 文件节点表索引
CREATE INDEX IF NOT EXISTS idx_file_nodes_subject_id ON file_nodes(subject_id);
CREATE INDEX IF NOT EXISTS idx_file_nodes_parent_id ON file_nodes(parent_id);
CREATE INDEX IF NOT EXISTS idx_file_nodes_path ON file_nodes(path);
CREATE INDEX IF NOT EXISTS idx_file_nodes_type ON file_nodes(type);
CREATE INDEX IF NOT EXISTS idx_file_nodes_name ON file_nodes(name);
CREATE INDEX IF NOT EXISTS idx_file_nodes_is_deleted ON file_nodes(is_deleted);

-- 复合索引优化常用查询
CREATE INDEX IF NOT EXISTS idx_file_nodes_subject_parent ON file_nodes(subject_id, parent_id);
CREATE INDEX IF NOT EXISTS idx_file_nodes_subject_type ON file_nodes(subject_id, type);
CREATE INDEX IF NOT EXISTS idx_file_nodes_parent_type ON file_nodes(parent_id, type);

-- 创建触发器自动更新updated_at字段
-- 学科表更新触发器
CREATE TRIGGER IF NOT EXISTS trigger_subjects_updated_at
    AFTER UPDATE ON subjects
    FOR EACH ROW
BEGIN
    UPDATE subjects SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 文件节点表更新触发器
CREATE TRIGGER IF NOT EXISTS trigger_file_nodes_updated_at
    AFTER UPDATE ON file_nodes
    FOR EACH ROW
BEGIN
    UPDATE file_nodes SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 插入初始测试数据（可选，用于开发测试）
-- INSERT INTO subjects (name, description) VALUES 
--     ('数学', '高等数学、线性代数、概率论等数学相关课程'),
--     ('计算机科学', '数据结构、算法、操作系统等计算机课程'),
--     ('英语', '英语语法、词汇、阅读理解等英语学习资料');