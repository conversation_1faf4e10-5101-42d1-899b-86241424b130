import { describe, test, expect, beforeAll, afterAll, beforeEach } from '@jest/globals'
import request from 'supertest'
import app from '../../src/app.js'
import { SubjectModel } from '../../src/models/Subject.js'
import { FileNodeModel } from '../../src/models/FileNode.js'
import { FileService } from '../../src/services/FileService.js'
import type { Subject, FileNode } from '../../src/types/index.js'

describe('File API Performance Tests', () => {
  let testSubject: Subject
  const testFiles: FileNode[] = []

  beforeAll(async () => {
    // 创建测试学科
    testSubject = SubjectModel.create({
      name: '性能测试学科'
    })
    // 手动设置ID用于测试
    testSubject.id = 'perf-test-subject'

    // 创建大量测试数据（1000+文件）
    console.log('🚀 Creating large dataset for performance testing...')

    // 创建100个文件夹
    for (let i = 1; i <= 100; i++) {
      const folder = FileNodeModel.create({
        id: `folder-${i}`,
        subject_id: testSubject.id,
        name: `文件夹${i}`,
        type: 'folder',
        path: `/文件夹${i}`,
        parent_id: null,
        size: null
      })
      testFiles.push(folder)
    }

    // 在每个文件夹中创建10个文件
    for (let i = 1; i <= 100; i++) {
      for (let j = 1; j <= 10; j++) {
        const file = FileNodeModel.create({
          id: `file-${i}-${j}`,
          subject_id: testSubject.id,
          name: `文档${j}.pdf`,
          type: 'file',
          path: `/文件夹${i}/文档${j}.pdf`,
          parent_id: `folder-${i}`,
          size: Math.floor(Math.random() * 1000000) + 100000 // 100KB-1MB
        })
        testFiles.push(file)
      }
    }

    console.log(`✅ Created ${testFiles.length} test files`)
  })

  afterAll(async () => {
    // 清理测试数据
    testFiles.forEach(file => FileNodeModel.delete(file.id))
    SubjectModel.delete(testSubject.id)
    console.log('🧹 Performance test data cleaned up')
  })

  beforeEach(() => {
    // 清空缓存确保测试准确性
    FileService.clearCache()
  })

  describe('Large Dataset Performance', () => {
    test('根目录文件列表查询应在2秒内完成', async () => {
      const startTime = Date.now()

      const response = await request(app.callback())
        .get(`/api/subjects/${testSubject.id}/files`)
        .expect(200)

      const responseTime = Date.now() - startTime

      expect(response.body.success).toBe(true)
      expect(response.body.data.files).toBeDefined()
      expect(response.body.data.total).toBe(100) // 100个根文件夹
      expect(responseTime).toBeLessThan(2000) // 性能要求：<2秒

      console.log(`📊 Root directory query: ${responseTime}ms`)
    })

    test('分页查询性能应保持稳定', async () => {
      const times: number[] = []

      // 测试前5页的查询时间
      for (let page = 1; page <= 5; page++) {
        const startTime = Date.now()

        const response = await request(app.callback())
          .get(`/api/subjects/${testSubject.id}/files?page=${page}&limit=20`)
          .expect(200)

        const responseTime = Date.now() - startTime
        times.push(responseTime)

        expect(response.body.success).toBe(true)
        expect(responseTime).toBeLessThan(2000)
      }

      const avgTime = times.reduce((a, b) => a + b, 0) / times.length
      console.log(`📊 Average pagination query time: ${avgTime.toFixed(2)}ms`)
      console.log(`📊 Query times: ${times.join(', ')}ms`)
    })

    test('搜索功能在大数据集下应保持高性能', async () => {
      const startTime = Date.now()

      const response = await request(app.callback())
        .get(`/api/subjects/${testSubject.id}/search?query=${encodeURIComponent('文档')}`)
        .expect(200)

      const responseTime = Date.now() - startTime

      expect(response.body.success).toBe(true)
      expect(response.body.data.files).toBeDefined()
      expect(response.body.data.total).toBe(1000) // 1000个文档文件
      expect(responseTime).toBeLessThan(2000) // 性能要求：<2秒

      console.log(`📊 Search query (1000 results): ${responseTime}ms`)
    })

    test('面包屑查询应快速响应', async () => {
      const startTime = Date.now()

      const response = await request(app.callback())
        .get(`/api/files/file-50-5/breadcrumb?subject_id=${testSubject.id}`)
        .expect(200)

      const responseTime = Date.now() - startTime

      expect(response.body.success).toBe(true)
      expect(response.body.data.breadcrumb).toBeDefined()
      expect(responseTime).toBeLessThan(500) // 面包屑查询应更快

      console.log(`📊 Breadcrumb query: ${responseTime}ms`)
    })
  })

  describe('Cache Performance', () => {
    test('缓存应显著提升重复查询性能', async () => {
      // 第一次查询（无缓存）
      const startTime1 = Date.now()
      const response1 = await request(app.callback())
        .get(`/api/subjects/${testSubject.id}/files`)
        .expect(200)
      const time1 = Date.now() - startTime1

      // 第二次查询（有缓存）
      const startTime2 = Date.now()
      const response2 = await request(app.callback())
        .get(`/api/subjects/${testSubject.id}/files`)
        .expect(200)
      const time2 = Date.now() - startTime2

      expect(response1.body).toEqual(response2.body)
      expect(time2).toBeLessThan(time1 * 0.5) // 缓存查询应至少快50%

      console.log(`📊 First query: ${time1}ms, Cached query: ${time2}ms`)
      console.log(`📊 Cache speedup: ${(time1 / time2).toFixed(2)}x`)
    })

    test('缓存命中率应超过80%', async () => {
      // 执行多次相同查询
      for (let i = 0; i < 10; i++) {
        await request(app.callback())
          .get(`/api/subjects/${testSubject.id}/files?page=1&limit=10`)
          .expect(200)
      }

      const stats = FileService.getCacheStats()
      const hitRate = stats.hitRate

      expect(hitRate).toBeGreaterThan(0.8) // 命中率应超过80%

      console.log(`📊 Cache stats:`, stats)
      console.log(`📊 Hit rate: ${(hitRate * 100).toFixed(2)}%`)
    })
  })

  describe('Stress Testing', () => {
    test('并发查询应保持性能稳定', async () => {
      const concurrentRequests = 10
      const promises: Promise<any>[] = []

      const startTime = Date.now()

      // 发起10个并发请求
      for (let i = 0; i < concurrentRequests; i++) {
        promises.push(
          request(app.callback())
            .get(`/api/subjects/${testSubject.id}/files?page=${i + 1}&limit=10`)
            .expect(200)
        )
      }

      const responses = await Promise.all(promises)
      const totalTime = Date.now() - startTime

      responses.forEach(response => {
        expect(response.body.success).toBe(true)
      })

      expect(totalTime).toBeLessThan(5000) // 10个并发请求应在5秒内完成

      console.log(`📊 ${concurrentRequests} concurrent requests: ${totalTime}ms`)
      console.log(`📊 Average per request: ${(totalTime / concurrentRequests).toFixed(2)}ms`)
    })
  })
})
