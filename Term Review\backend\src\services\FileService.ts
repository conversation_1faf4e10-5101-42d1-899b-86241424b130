import { FileNodeModel } from '../models/FileNode.js'
import { SubjectService } from './SubjectService.js'
import { PerformanceMonitor, PerformanceTester } from '../utils/performance.js'
import type {
  GetFilesByParentRequest,
  GetFilesByParentResponse,
  GetBreadcrumbRequest,
  GetBreadcrumbResponse,
  SearchFilesRequest,
  SearchFilesResponse,
  BreadcrumbItem
} from '../types/index.js'

// 简单的内存缓存实现
interface CacheItem<T> {
  data: T
  timestamp: number
  ttl: number // 生存时间（毫秒）
}

class MemoryCache {
  private cache = new Map<string, CacheItem<any>>()
  private hitCount = 0
  private missCount = 0

  set<T>(key: string, data: T, ttl: number = 300000): void { // 默认5分钟TTL
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key)
    if (!item) {
      this.missCount++
      return null
    }

    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      this.missCount++
      return null
    }

    this.hitCount++
    return item.data as T
  }

  delete(key: string): void {
    this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
    this.hitCount = 0
    this.missCount = 0
  }

  getStats(): { hitCount: number; missCount: number; hitRate: number; size: number } {
    const total = this.hitCount + this.missCount
    return {
      hitCount: this.hitCount,
      missCount: this.missCount,
      hitRate: total > 0 ? this.hitCount / total : 0,
      size: this.cache.size
    }
  }

  // 清理过期缓存
  cleanup(): void {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key)
      }
    }
  }
}

// 全局缓存实例
const cache = new MemoryCache()

// 定期清理过期缓存
setInterval(() => cache.cleanup(), 60000) // 每分钟清理一次

export class FileService {
  // 获取缓存统计信息
  static getCacheStats() {
    return cache.getStats()
  }

  // 清空缓存
  static clearCache() {
    cache.clear()
  }

  // 清除特定学科的缓存
  static clearSubjectCache(subjectId: string) {
    const keysToDelete: string[] = []

    // 查找所有相关的缓存键
    for (const [key] of (cache as any).cache.entries()) {
      if (key.includes(subjectId)) {
        keysToDelete.push(key)
      }
    }

    // 删除相关缓存
    keysToDelete.forEach(key => cache.delete(key))

    console.log(`🗑️ Cleared ${keysToDelete.length} cache entries for subject ${subjectId}`)
  }

  // 清除特定节点的缓存
  static clearNodeCache(subjectId: string, nodeId: string) {
    const keysToDelete: string[] = []

    // 查找所有相关的缓存键
    for (const [key] of (cache as any).cache.entries()) {
      if (key.includes(subjectId) && key.includes(nodeId)) {
        keysToDelete.push(key)
      }
    }

    // 删除相关缓存
    keysToDelete.forEach(key => cache.delete(key))

    console.log(`🗑️ Cleared ${keysToDelete.length} cache entries for node ${nodeId}`)
  }

  // 获取指定父节点下的文件列表（带缓存优化）
  static async getFilesByParent(params: GetFilesByParentRequest): Promise<GetFilesByParentResponse> {
    const startTime = Date.now()

    // 参数验证
    if (!params.subject_id || params.subject_id.trim().length === 0) {
      throw new Error('学科ID不能为空')
    }

    // 构建缓存键
    const parentId = params.parent_id ? params.parent_id.trim() : null
    const search = params.search ? params.search.trim() : undefined
    const page = Math.max(1, params.page || 1)
    const limit = Math.min(100, Math.max(1, params.limit || 20))

    const cacheKey = `files:${params.subject_id.trim()}:${parentId || 'root'}:${search || ''}:${page}:${limit}`

    // 尝试从缓存获取
    const cached = cache.get<GetFilesByParentResponse>(cacheKey)
    if (cached) {
      const responseTime = Date.now() - startTime
      console.log(`📊 Cache hit for getFilesByParent: ${responseTime}ms`)
      return cached
    }

    // 缓存学科存在性检查
    const subjectCacheKey = `subject:exists:${params.subject_id.trim()}`
    let subjectExists = cache.get<boolean>(subjectCacheKey)

    if (subjectExists === null) {
      subjectExists = await SubjectService.subjectExists(params.subject_id.trim())
      cache.set(subjectCacheKey, subjectExists, 600000) // 10分钟TTL
    }

    if (!subjectExists) {
      throw new Error('学科不存在')
    }

    // 验证父节点ID（如果提供）
    if (params.parent_id) {
      if (params.parent_id.trim().length === 0) {
        throw new Error('父节点ID不能为空字符串')
      }

      // 缓存父节点存在性检查
      const parentCacheKey = `node:belongs:${params.parent_id.trim()}:${params.subject_id.trim()}`
      let parentExists = cache.get<boolean>(parentCacheKey)

      if (parentExists === null) {
        parentExists = FileNodeModel.belongsToSubject(params.parent_id.trim(), params.subject_id.trim())
        cache.set(parentCacheKey, parentExists, 300000) // 5分钟TTL
      }

      if (!parentExists) {
        throw new Error('父节点不存在或不属于指定学科')
      }
    }

    try {
      const { files, total } = FileNodeModel.findByParent(
        params.subject_id.trim(),
        parentId,
        search,
        page,
        limit
      )

      const has_more = page * limit < total
      const result = {
        files,
        total,
        page,
        limit,
        has_more
      }

      // 缓存结果（热门查询缓存更长时间）
      const cacheTTL = search ? 180000 : 300000 // 搜索结果3分钟，普通查询5分钟
      cache.set(cacheKey, result, cacheTTL)

      const responseTime = Date.now() - startTime
      PerformanceMonitor.recordMetric('getFilesByParent', responseTime)
      console.log(`📊 getFilesByParent query time: ${responseTime}ms, cached for ${cacheTTL / 1000}s`)

      // 记录慢查询
      if (responseTime > 1000) {
        console.warn(`🐌 Slow query detected: getFilesByParent took ${responseTime}ms`)
      }

      return result
    } catch (error) {
      console.error('Get files by parent error:', error)
      throw new Error('获取文件列表失败')
    }
  }

  // 获取面包屑导航路径（带缓存优化）
  static async getBreadcrumb(params: GetBreadcrumbRequest): Promise<GetBreadcrumbResponse> {
    const startTime = Date.now()

    // 参数验证
    if (!params.subject_id || params.subject_id.trim().length === 0) {
      throw new Error('学科ID不能为空')
    }

    const nodeId = params.node_id ? params.node_id.trim() : null
    const cacheKey = `breadcrumb:${params.subject_id.trim()}:${nodeId || 'root'}`

    // 尝试从缓存获取
    const cached = cache.get<GetBreadcrumbResponse>(cacheKey)
    if (cached) {
      const responseTime = Date.now() - startTime
      console.log(`📊 Cache hit for getBreadcrumb: ${responseTime}ms`)
      return cached
    }

    // 缓存学科存在性检查
    const subjectCacheKey = `subject:exists:${params.subject_id.trim()}`
    let subjectExists = cache.get<boolean>(subjectCacheKey)

    if (subjectExists === null) {
      subjectExists = await SubjectService.subjectExists(params.subject_id.trim())
      cache.set(subjectCacheKey, subjectExists, 600000) // 10分钟TTL
    }

    if (!subjectExists) {
      throw new Error('学科不存在')
    }

    // 验证节点ID（如果提供）
    if (params.node_id) {
      if (params.node_id.trim().length === 0) {
        throw new Error('节点ID不能为空字符串')
      }

      // 缓存节点存在性检查
      const nodeCacheKey = `node:belongs:${params.node_id.trim()}:${params.subject_id.trim()}`
      let nodeExists = cache.get<boolean>(nodeCacheKey)

      if (nodeExists === null) {
        nodeExists = FileNodeModel.belongsToSubject(params.node_id.trim(), params.subject_id.trim())
        cache.set(nodeCacheKey, nodeExists, 300000) // 5分钟TTL
      }

      if (!nodeExists) {
        throw new Error('节点不存在或不属于指定学科')
      }
    }

    try {
      const pathNodes = FileNodeModel.getBreadcrumbPath(params.subject_id.trim(), nodeId)

      // 转换为面包屑格式
      const breadcrumb: BreadcrumbItem[] = pathNodes.map(node => ({
        id: node.id,
        name: node.name,
        path: node.path
      }))

      const result = { breadcrumb }

      // 缓存结果（面包屑路径相对稳定，缓存时间较长）
      cache.set(cacheKey, result, 600000) // 10分钟TTL

      const responseTime = Date.now() - startTime
      PerformanceMonitor.recordMetric('getBreadcrumb', responseTime)
      console.log(`📊 getBreadcrumb query time: ${responseTime}ms`)

      // 记录慢查询
      if (responseTime > 500) {
        console.warn(`🐌 Slow breadcrumb query: ${responseTime}ms for node ${nodeId}`)
      }

      return result
    } catch (error) {
      console.error('Get breadcrumb error:', error)
      throw new Error('获取面包屑导航失败')
    }
  }

  // 搜索文件（带缓存优化）
  static async searchFiles(params: SearchFilesRequest): Promise<SearchFilesResponse> {
    const startTime = Date.now()

    // 参数验证
    if (!params.subject_id || params.subject_id.trim().length === 0) {
      throw new Error('学科ID不能为空')
    }

    if (!params.query || params.query.trim().length === 0) {
      throw new Error('搜索关键词不能为空')
    }

    if (params.query.trim().length > 100) {
      throw new Error('搜索关键词长度不能超过100个字符')
    }

    // 验证类型过滤参数
    const validTypes = ['file', 'folder', 'all']
    const type = params.type || 'all'
    if (!validTypes.includes(type)) {
      throw new Error('文件类型过滤参数无效')
    }

    // 验证分页参数
    const page = Math.max(1, params.page || 1)
    const limit = Math.min(100, Math.max(1, params.limit || 20))

    // 构建缓存键
    const cacheKey = `search:${params.subject_id.trim()}:${params.query.trim()}:${type}:${page}:${limit}`

    // 尝试从缓存获取
    const cached = cache.get<SearchFilesResponse>(cacheKey)
    if (cached) {
      const responseTime = Date.now() - startTime
      console.log(`📊 Cache hit for searchFiles: ${responseTime}ms`)
      return cached
    }

    // 缓存学科存在性检查
    const subjectCacheKey = `subject:exists:${params.subject_id.trim()}`
    let subjectExists = cache.get<boolean>(subjectCacheKey)

    if (subjectExists === null) {
      subjectExists = await SubjectService.subjectExists(params.subject_id.trim())
      cache.set(subjectCacheKey, subjectExists, 600000) // 10分钟TTL
    }

    if (!subjectExists) {
      throw new Error('学科不存在')
    }

    try {
      const { files, total } = FileNodeModel.search(
        params.subject_id.trim(),
        params.query.trim(),
        type as 'file' | 'folder' | 'all',
        page,
        limit
      )

      const has_more = page * limit < total
      const result = {
        files,
        total,
        page,
        limit,
        has_more,
        query: params.query.trim()
      }

      // 缓存搜索结果（搜索结果缓存时间较短）
      cache.set(cacheKey, result, 180000) // 3分钟TTL

      const responseTime = Date.now() - startTime
      PerformanceMonitor.recordMetric('searchFiles', responseTime)
      console.log(`📊 searchFiles query time: ${responseTime}ms, query: "${params.query.trim()}"`)

      // 记录慢查询
      if (responseTime > 1000) {
        console.warn(`🐌 Slow search query: ${responseTime}ms for "${params.query.trim()}"`)
      }

      return result
    } catch (error) {
      console.error('Search files error:', error)
      throw new Error('搜索文件失败')
    }
  }

  // 获取性能统计信息
  static getPerformanceStats() {
    return PerformanceMonitor.getStats()
  }

  // 生成性能报告
  static generatePerformanceReport() {
    return PerformanceTester.generateReport()
  }
}
