# 后端架构与开发指南

## 概述

期末复习平台后端基于Node.js + Koa + TypeScript构建，采用分层架构设计，提供RESTful API服务。本文档提供完整的后端架构设计、开发环境搭建、开发规范和部署指南。

## 最新更新记录

### 2025-07-27 - 数据访问层优化
- **修复** SubjectDao.findById 方法：增加 `includeDisabled` 参数支持管理员查询
- **优化** 管理员权限下的数据库查询逻辑：区分普通用户和管理员查询权限
- **修复** 管理员路由响应处理：纠正 ResponseHelper.success 方法参数传递
- **改进** 数据库查询性能：优化查询条件和索引使用

### 2025-07-27 - 管理员API完善
- 完成管理员学科管理API的全套CRUD操作
- 实现管理员专用的数据查询和状态管理功能
- 优化API响应格式和错误处理机制
- 建立管理员操作的审计日志记录

## 技术栈

### 核心技术
- **运行时**: Node.js 18+
- **框架**: Koa 2.x
- **语言**: TypeScript 5.x
- **数据库**: SQLite 3.x + better-sqlite3
- **包管理**: npm

### 中间件和工具
- **错误处理**: 统一错误处理中间件
- **日志记录**: 结构化日志记录
- **CORS**: 跨域资源共享支持
- **限流**: API请求频率限制
- **压缩**: Gzip响应压缩

## 项目架构

### 整体架构设计
```
期末复习平台后端架构

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   HTTP Client   │────│   Koa Server    │────│   SQLite DB     │
│  (Frontend/API) │    │   (Middleware)  │    │   (Data Layer)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                    ┌─────────┼─────────┐
                    │         │         │
            ┌───────▼───┐ ┌───▼───┐ ┌───▼────┐
            │  Routes   │ │  DAO  │ │Service │
            │  (API)    │ │(Data) │ │(Logic) │
            └───────────┘ └───────┘ └────────┘
```

### 分层架构
1. **路由层 (Routes)**: 处理HTTP请求和响应
2. **服务层 (Services)**: 业务逻辑处理
3. **数据访问层 (DAO)**: 数据库操作封装
4. **中间件层 (Middleware)**: 横切关注点处理

## 项目结构

```
src/
├── app.ts                 # 应用入口文件
├── config/
│   └── index.ts          # 环境配置管理
├── middleware/
│   ├── index.ts          # 中间件注册
│   ├── error.ts          # 错误处理中间件
│   ├── logger.ts         # 日志中间件
│   ├── cors.ts           # CORS中间件
│   ├── rateLimit.ts      # 限流中间件
│   └── auth.ts           # 认证中间件
├── routes/
│   ├── index.ts          # 主路由
│   ├── subjects.ts       # 学科路由
│   ├── files.ts          # 文件路由
│   └── admin.ts          # 管理员路由
├── dao/                  # 数据访问层
│   ├── BaseDAO.ts        # 基础DAO类
│   ├── SubjectDAO.ts     # 学科数据访问
│   └── FileDAO.ts        # 文件数据访问
├── services/             # 业务逻辑层
│   ├── SubjectService.ts # 学科业务逻辑
│   └── FileService.ts    # 文件业务逻辑
├── utils/
│   ├── response.ts       # 统一响应格式
│   ├── logger.ts         # 日志工具
│   └── database.ts       # 数据库工具
├── types/
│   ├── index.ts          # 通用类型定义
│   ├── api.ts            # API类型定义
│   └── koa-extensions.ts # Koa扩展类型
└── database/             # 数据库模块
    ├── index.ts          # 数据库连接
    ├── migrations/       # 数据库迁移
    └── seeds/            # 测试数据
```

## 数据库设计

### 核心表结构

#### subjects (学科表)
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | 学科唯一标识 |
| name | TEXT | NOT NULL UNIQUE | 学科名称，唯一约束 |
| description | TEXT | DEFAULT '' | 学科描述 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 更新时间 |
| status | INTEGER | DEFAULT 1 | 状态：1=启用，0=禁用 |
| sort_order | INTEGER | DEFAULT 0 | 排序权重 |
| file_count | INTEGER | DEFAULT 0 | 文件数量统计 |
| total_size | INTEGER | DEFAULT 0 | 总文件大小 |

#### file_nodes (文件节点表)
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | 节点唯一标识 |
| subject_id | INTEGER | NOT NULL, FK | 所属学科ID |
| parent_id | INTEGER | DEFAULT NULL, FK | 父节点ID，NULL表示根节点 |
| name | TEXT | NOT NULL | 文件/文件夹名称 |
| type | TEXT | NOT NULL, CHECK | 节点类型：'file'或'folder' |
| path | TEXT | NOT NULL | 相对路径 |
| size | INTEGER | DEFAULT 0 | 文件大小（字节） |
| mime_type | TEXT | DEFAULT '' | MIME类型 |
| content_hash | TEXT | DEFAULT '' | 内容哈希值 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 更新时间 |
| is_deleted | INTEGER | DEFAULT 0 | 软删除标记 |

### 数据库配置
```sql
-- 性能优化配置
PRAGMA journal_mode = WAL;        -- 启用WAL模式提升并发性能
PRAGMA synchronous = NORMAL;      -- 平衡性能和安全性
PRAGMA cache_size = 1000;         -- 设置缓存大小
PRAGMA temp_store = memory;       -- 临时表存储在内存中
PRAGMA foreign_keys = ON;         -- 启用外键约束

-- 索引优化
CREATE INDEX idx_subjects_status_sort ON subjects(status, sort_order);
CREATE INDEX idx_file_nodes_subject_parent ON file_nodes(subject_id, parent_id);
CREATE INDEX idx_file_nodes_path ON file_nodes(path);
```

### 数据库迁移
```sql
-- 001_initial_schema.sql
CREATE TABLE subjects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    description TEXT DEFAULT '',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    status INTEGER DEFAULT 1,
    sort_order INTEGER DEFAULT 0
);

-- 002_admin_enhancements.sql
ALTER TABLE subjects ADD COLUMN file_count INTEGER DEFAULT 0;
ALTER TABLE subjects ADD COLUMN total_size INTEGER DEFAULT 0;

-- 创建触发器自动更新统计信息
CREATE TRIGGER update_subject_stats
AFTER INSERT ON file_nodes
BEGIN
    UPDATE subjects 
    SET file_count = (
        SELECT COUNT(*) FROM file_nodes 
        WHERE subject_id = NEW.subject_id AND type = 'file' AND is_deleted = 0
    ),
    total_size = (
        SELECT COALESCE(SUM(size), 0) FROM file_nodes 
        WHERE subject_id = NEW.subject_id AND type = 'file' AND is_deleted = 0
    )
    WHERE id = NEW.subject_id;
END;
```

## 开发环境搭建

### 1. 系统要求
- Node.js 18.0+
- npm 9.0+
- SQLite 3.x
- Git

### 2. 项目安装
```bash
# 克隆项目
git clone <repository-url>
cd qimo

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env
```

### 3. 环境配置

创建 `.env` 文件：
```bash
# 服务器配置
NODE_ENV=development
PORT=3000
HOST=localhost

# 数据库配置
DATABASE_PATH=./data/platform.db
DATABASE_BACKUP_PATH=./data/backups/

# 日志配置
LOG_LEVEL=debug
LOG_FILE=./logs/app.log

# CORS配置
CORS_ORIGIN=http://localhost:5173

# 管理员配置
ADMIN_PATH=/admin-panel-abcdef
ADMIN_SECRET=your-admin-secret-key

# 限流配置
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100
```

### 4. 数据库初始化
```bash
# 运行数据库迁移
npm run migrate

# 填充测试数据（可选）
npm run seed
```

### 5. 启动开发服务器
```bash
# 开发模式启动
npm run dev

# 生产模式启动
npm run build
npm start

# 类型检查
npm run type-check

# 代码检查
npm run lint
```

## 核心模块实现

### 1. 应用入口 (app.ts)
```typescript
import Koa from 'koa'
import { config } from './config'
import { setupMiddleware } from './middleware'
import { setupRoutes } from './routes'
import { initDatabase } from './database'
import { logger } from './utils/logger'

const app = new Koa()

// 初始化数据库
initDatabase()

// 设置中间件
setupMiddleware(app)

// 设置路由
setupRoutes(app)

// 启动服务器
const server = app.listen(config.port, config.host, () => {
  logger.info(`Server running on http://${config.host}:${config.port}`)
})

// 优雅关闭
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully')
  server.close(() => {
    logger.info('Process terminated')
    process.exit(0)
  })
})

export default app
```

### 2. 配置管理 (config/index.ts)
```typescript
import dotenv from 'dotenv'
import path from 'path'

dotenv.config()

export const config = {
  // 服务器配置
  env: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT || '3000'),
  host: process.env.HOST || 'localhost',
  
  // 数据库配置
  database: {
    path: process.env.DATABASE_PATH || './data/platform.db',
    backupPath: process.env.DATABASE_BACKUP_PATH || './data/backups/',
    options: {
      verbose: process.env.NODE_ENV === 'development' ? console.log : null,
      fileMustExist: false
    }
  },
  
  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || './logs/app.log'
  },
  
  // CORS配置
  cors: {
    origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
    credentials: true
  },
  
  // 管理员配置
  admin: {
    path: process.env.ADMIN_PATH || '/admin-panel-abcdef',
    secret: process.env.ADMIN_SECRET || 'default-secret'
  },
  
  // 限流配置
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW || '15') * 60 * 1000,
    max: parseInt(process.env.RATE_LIMIT_MAX || '100')
  }
}

// 验证必需的配置
const requiredEnvVars = ['DATABASE_PATH']
for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Required environment variable ${envVar} is not set`)
  }
}
```

### 3. 中间件设置 (middleware/index.ts)
```typescript
import Koa from 'koa'
import cors from '@koa/cors'
import bodyParser from 'koa-bodyparser'
import compress from 'koa-compress'
import helmet from 'koa-helmet'

import { config } from '../config'
import { errorHandler } from './error'
import { logger } from './logger'
import { rateLimit } from './rateLimit'
import { authMiddleware } from './auth'

export const setupMiddleware = (app: Koa) => {
  // 安全头
  app.use(helmet())
  
  // 压缩
  app.use(compress({
    threshold: 2048,
    gzip: {
      flush: require('zlib').constants.Z_SYNC_FLUSH
    },
    deflate: {
      flush: require('zlib').constants.Z_SYNC_FLUSH
    }
  }))
  
  // CORS
  app.use(cors(config.cors))
  
  // 限流
  app.use(rateLimit)
  
  // 日志
  app.use(logger)
  
  // 错误处理
  app.use(errorHandler)
  
  // 请求体解析
  app.use(bodyParser({
    jsonLimit: '10mb',
    textLimit: '10mb'
  }))
  
  // 认证中间件
  app.use(authMiddleware)
}
```

### 4. 数据访问层 (DAO)

#### 基础DAO类 (dao/BaseDAO.ts)
```typescript
import Database from 'better-sqlite3'
import { database } from '../database'

export abstract class BaseDAO {
  protected db: Database.Database

  constructor() {
    this.db = database
  }

  protected executeQuery<T = any>(sql: string, params: any[] = []): T[] {
    try {
      const stmt = this.db.prepare(sql)
      return stmt.all(params) as T[]
    } catch (error) {
      throw new Error(`Database query failed: ${error.message}`)
    }
  }

  protected executeInsert(sql: string, params: any[] = []): number {
    try {
      const stmt = this.db.prepare(sql)
      const result = stmt.run(params)
      return result.lastInsertRowid as number
    } catch (error) {
      throw new Error(`Database insert failed: ${error.message}`)
    }
  }

  protected executeUpdate(sql: string, params: any[] = []): number {
    try {
      const stmt = this.db.prepare(sql)
      const result = stmt.run(params)
      return result.changes
    } catch (error) {
      throw new Error(`Database update failed: ${error.message}`)
    }
  }
}
```

#### 学科DAO (dao/SubjectDAO.ts)
```typescript
import { BaseDAO } from './BaseDAO'
import type { Subject, CreateSubjectData } from '../types'

export class SubjectDAO extends BaseDAO {
  // 获取所有启用的学科
  findAllActive(): Subject[] {
    const sql = `
      SELECT * FROM subjects
      WHERE status = 1
      ORDER BY sort_order ASC, created_at DESC
    `
    return this.executeQuery<Subject>(sql)
  }

  // 获取所有学科（管理员用）
  findAll(): Subject[] {
    const sql = `
      SELECT * FROM subjects
      ORDER BY sort_order ASC, created_at DESC
    `
    return this.executeQuery<Subject>(sql)
  }

  // 根据ID获取学科
  findById(id: number): Subject | null {
    const sql = 'SELECT * FROM subjects WHERE id = ?'
    const results = this.executeQuery<Subject>(sql, [id])
    return results[0] || null
  }

  // 创建学科
  create(data: CreateSubjectData): Subject {
    const sql = `
      INSERT INTO subjects (name, description, status, sort_order)
      VALUES (?, ?, ?, ?)
    `
    const id = this.executeInsert(sql, [
      data.name,
      data.description || '',
      data.status ?? 1,
      data.sort_order ?? 0
    ])

    return this.findById(id)!
  }

  // 更新学科
  update(id: number, data: Partial<CreateSubjectData>): Subject | null {
    const fields = []
    const values = []

    if (data.name !== undefined) {
      fields.push('name = ?')
      values.push(data.name)
    }
    if (data.description !== undefined) {
      fields.push('description = ?')
      values.push(data.description)
    }
    if (data.status !== undefined) {
      fields.push('status = ?')
      values.push(data.status)
    }
    if (data.sort_order !== undefined) {
      fields.push('sort_order = ?')
      values.push(data.sort_order)
    }

    if (fields.length === 0) return this.findById(id)

    fields.push('updated_at = CURRENT_TIMESTAMP')
    values.push(id)

    const sql = `UPDATE subjects SET ${fields.join(', ')} WHERE id = ?`
    const changes = this.executeUpdate(sql, values)

    return changes > 0 ? this.findById(id) : null
  }

  // 软删除学科
  softDelete(id: number): boolean {
    const sql = 'UPDATE subjects SET status = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?'
    const changes = this.executeUpdate(sql, [id])
    return changes > 0
  }

  // 硬删除学科
  hardDelete(id: number): boolean {
    const sql = 'DELETE FROM subjects WHERE id = ?'
    const changes = this.executeUpdate(sql, [id])
    return changes > 0
  }

  // 批量更新状态
  batchUpdateStatus(ids: number[], status: 0 | 1): number {
    const placeholders = ids.map(() => '?').join(',')
    const sql = `
      UPDATE subjects
      SET status = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id IN (${placeholders})
    `
    return this.executeUpdate(sql, [status, ...ids])
  }

  // 获取学科统计信息
  getStats(id: number): { fileCount: number; folderCount: number; totalSize: number } {
    const sql = `
      SELECT
        COUNT(CASE WHEN type = 'file' THEN 1 END) as fileCount,
        COUNT(CASE WHEN type = 'folder' THEN 1 END) as folderCount,
        COALESCE(SUM(CASE WHEN type = 'file' THEN size ELSE 0 END), 0) as totalSize
      FROM file_nodes
      WHERE subject_id = ? AND is_deleted = 0
    `
    const results = this.executeQuery(sql, [id])
    return results[0] || { fileCount: 0, folderCount: 0, totalSize: 0 }
  }
}
```

### 5. 服务层 (Services)

#### 学科服务 (services/SubjectService.ts)
```typescript
import { SubjectDAO } from '../dao/SubjectDAO'
import type { Subject, CreateSubjectData } from '../types'

export class SubjectService {
  private subjectDAO: SubjectDAO

  constructor() {
    this.subjectDAO = new SubjectDAO()
  }

  // 获取所有启用的学科
  async getAllActiveSubjects(): Promise<Subject[]> {
    return this.subjectDAO.findAllActive()
  }

  // 获取所有学科（管理员）
  async getAllSubjects(): Promise<Subject[]> {
    return this.subjectDAO.findAll()
  }

  // 获取学科详情
  async getSubjectById(id: number): Promise<Subject | null> {
    if (!Number.isInteger(id) || id <= 0) {
      throw new Error('Invalid subject ID')
    }
    return this.subjectDAO.findById(id)
  }

  // 创建学科
  async createSubject(data: CreateSubjectData): Promise<Subject> {
    // 验证数据
    if (!data.name || data.name.trim().length === 0) {
      throw new Error('Subject name is required')
    }

    if (data.name.length > 100) {
      throw new Error('Subject name is too long')
    }

    if (data.description && data.description.length > 500) {
      throw new Error('Subject description is too long')
    }

    return this.subjectDAO.create({
      ...data,
      name: data.name.trim(),
      description: data.description?.trim() || ''
    })
  }

  // 更新学科
  async updateSubject(id: number, data: Partial<CreateSubjectData>): Promise<Subject | null> {
    if (!Number.isInteger(id) || id <= 0) {
      throw new Error('Invalid subject ID')
    }

    // 验证数据
    if (data.name !== undefined) {
      if (!data.name || data.name.trim().length === 0) {
        throw new Error('Subject name cannot be empty')
      }
      if (data.name.length > 100) {
        throw new Error('Subject name is too long')
      }
      data.name = data.name.trim()
    }

    if (data.description !== undefined && data.description.length > 500) {
      throw new Error('Subject description is too long')
    }

    return this.subjectDAO.update(id, data)
  }

  // 删除学科
  async deleteSubject(id: number, cascade: boolean = false): Promise<boolean> {
    if (!Number.isInteger(id) || id <= 0) {
      throw new Error('Invalid subject ID')
    }

    if (cascade) {
      return this.subjectDAO.hardDelete(id)
    } else {
      return this.subjectDAO.softDelete(id)
    }
  }

  // 批量更新学科状态
  async batchUpdateStatus(ids: number[], status: 0 | 1): Promise<number> {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Subject IDs are required')
    }

    // 验证所有ID都是有效的正整数
    for (const id of ids) {
      if (!Number.isInteger(id) || id <= 0) {
        throw new Error(`Invalid subject ID: ${id}`)
      }
    }

    return this.subjectDAO.batchUpdateStatus(ids, status)
  }

  // 获取学科统计信息
  async getSubjectStats(id: number): Promise<{ fileCount: number; folderCount: number; totalSize: number }> {
    if (!Number.isInteger(id) || id <= 0) {
      throw new Error('Invalid subject ID')
    }

    return this.subjectDAO.getStats(id)
  }
}
```

### 6. 路由实现 (Routes)

#### 学科路由 (routes/subjects.ts)
```typescript
import Router from '@koa/router'
import { SubjectService } from '../services/SubjectService'
import { successResponse, errorResponse } from '../utils/response'
import type { Context } from 'koa'

const router = new Router({ prefix: '/api/subjects' })
const subjectService = new SubjectService()

// GET /api/subjects - 获取学科列表
router.get('/', async (ctx: Context) => {
  try {
    const subjects = await subjectService.getAllActiveSubjects()
    ctx.body = successResponse(subjects)
  } catch (error) {
    ctx.body = errorResponse('INTERNAL_ERROR', error.message)
    ctx.status = 500
  }
})

// GET /api/subjects/:id - 获取学科详情
router.get('/:id', async (ctx: Context) => {
  try {
    const id = parseInt(ctx.params.id)
    if (isNaN(id)) {
      ctx.body = errorResponse('INVALID_REQUEST', 'Invalid subject ID')
      ctx.status = 400
      return
    }

    const subject = await subjectService.getSubjectById(id)
    if (!subject) {
      ctx.body = errorResponse('NOT_FOUND', 'Subject not found')
      ctx.status = 404
      return
    }

    ctx.body = successResponse(subject)
  } catch (error) {
    ctx.body = errorResponse('INTERNAL_ERROR', error.message)
    ctx.status = 500
  }
})

// POST /api/subjects - 创建学科
router.post('/', async (ctx: Context) => {
  try {
    const data = ctx.request.body
    const subject = await subjectService.createSubject(data)
    ctx.body = successResponse(subject, '学科创建成功')
    ctx.status = 201
  } catch (error) {
    if (error.message.includes('UNIQUE constraint failed')) {
      ctx.body = errorResponse('RESOURCE_EXISTS', '学科名称已存在')
      ctx.status = 409
    } else if (error.message.includes('required') || error.message.includes('too long')) {
      ctx.body = errorResponse('VALIDATION_ERROR', error.message)
      ctx.status = 400
    } else {
      ctx.body = errorResponse('INTERNAL_ERROR', error.message)
      ctx.status = 500
    }
  }
})

// PUT /api/subjects/:id - 更新学科
router.put('/:id', async (ctx: Context) => {
  try {
    const id = parseInt(ctx.params.id)
    if (isNaN(id)) {
      ctx.body = errorResponse('INVALID_REQUEST', 'Invalid subject ID')
      ctx.status = 400
      return
    }

    const data = ctx.request.body
    const subject = await subjectService.updateSubject(id, data)

    if (!subject) {
      ctx.body = errorResponse('NOT_FOUND', 'Subject not found')
      ctx.status = 404
      return
    }

    ctx.body = successResponse(subject, '学科更新成功')
  } catch (error) {
    if (error.message.includes('UNIQUE constraint failed')) {
      ctx.body = errorResponse('RESOURCE_EXISTS', '学科名称已存在')
      ctx.status = 409
    } else if (error.message.includes('required') || error.message.includes('too long')) {
      ctx.body = errorResponse('VALIDATION_ERROR', error.message)
      ctx.status = 400
    } else {
      ctx.body = errorResponse('INTERNAL_ERROR', error.message)
      ctx.status = 500
    }
  }
})

// DELETE /api/subjects/:id - 删除学科
router.delete('/:id', async (ctx: Context) => {
  try {
    const id = parseInt(ctx.params.id)
    if (isNaN(id)) {
      ctx.body = errorResponse('INVALID_REQUEST', 'Invalid subject ID')
      ctx.status = 400
      return
    }

    const cascade = ctx.query.cascade === 'true'
    const success = await subjectService.deleteSubject(id, cascade)

    if (!success) {
      ctx.body = errorResponse('NOT_FOUND', 'Subject not found')
      ctx.status = 404
      return
    }

    ctx.body = successResponse(null, '学科删除成功')
  } catch (error) {
    ctx.body = errorResponse('INTERNAL_ERROR', error.message)
    ctx.status = 500
  }
})

export default router
```

### 7. 管理员路由 (routes/admin.ts)

管理员路由提供完整的CRUD操作，支持前后端集成和数据同步功能。

#### 前后端集成特性

1. **统一权限验证**: 通过 `x-admin-path` 请求头进行权限控制
2. **数据同步支持**: 管理操作完成后触发前端数据同步
3. **错误处理优化**: 提供详细的错误信息和状态码
4. **性能优化**: 支持批量操作和缓存机制

```typescript
import Router from '@koa/router'
import { SubjectService } from '../services/SubjectService'
import { successResponse, errorResponse } from '../utils/response'
import type { Context } from 'koa'

const router = new Router({ prefix: '/api/admin' })
const subjectService = new SubjectService()

// 管理员权限验证中间件
router.use(async (ctx: Context, next) => {
  const adminPath = ctx.headers['x-admin-path']
  if (adminPath !== '/admin-panel-abcdef') {
    ctx.body = errorResponse('FORBIDDEN', 'Admin access required')
    ctx.status = 403
    return
  }
  await next()
})

// GET /api/admin/subjects - 获取所有学科（管理员）
router.get('/subjects', async (ctx: Context) => {
  try {
    const subjects = await subjectService.getAllSubjects()

    // 为每个学科添加统计信息
    const subjectsWithStats = await Promise.all(
      subjects.map(async (subject) => {
        const stats = await subjectService.getSubjectStats(subject.id)
        return {
          ...subject,
          ...stats
        }
      })
    )

    ctx.body = successResponse(subjectsWithStats)
  } catch (error) {
    ctx.body = errorResponse('INTERNAL_ERROR', error.message)
    ctx.status = 500
  }
})

// POST /api/admin/subjects - 创建学科（管理员）
router.post('/subjects', async (ctx: Context) => {
  try {
    const data = ctx.request.body
    const subject = await subjectService.createSubject(data)
    ctx.body = successResponse(subject, '学科创建成功')
    ctx.status = 201
  } catch (error) {
    if (error.message.includes('UNIQUE constraint failed')) {
      ctx.body = errorResponse('RESOURCE_EXISTS', '学科名称已存在')
      ctx.status = 409
    } else {
      ctx.body = errorResponse('VALIDATION_ERROR', error.message)
      ctx.status = 400
    }
  }
})

// GET /api/admin/subjects/:id/stats - 获取学科统计信息
router.get('/subjects/:id/stats', async (ctx: Context) => {
  try {
    const id = parseInt(ctx.params.id)
    if (isNaN(id)) {
      ctx.body = errorResponse('INVALID_REQUEST', 'Invalid subject ID')
      ctx.status = 400
      return
    }

    const stats = await subjectService.getSubjectStats(id)
    ctx.body = successResponse(stats)
  } catch (error) {
    ctx.body = errorResponse('INTERNAL_ERROR', error.message)
    ctx.status = 500
  }
})

export default router
```

## 部署指南

### 1. 生产环境配置

#### 环境变量配置
```bash
# .env.production
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# 数据库配置
DATABASE_PATH=/app/data/platform.db
DATABASE_BACKUP_PATH=/app/data/backups/

# 日志配置
LOG_LEVEL=info
LOG_FILE=/app/logs/app.log

# CORS配置
CORS_ORIGIN=https://your-domain.com

# 管理员配置
ADMIN_PATH=/admin-panel-secure-path
ADMIN_SECRET=your-secure-admin-secret

# 限流配置
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100
```

#### PM2配置 (ecosystem.config.js)
```javascript
module.exports = {
  apps: [{
    name: 'qimo-backend',
    script: './dist/app.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
}
```

### 2. Docker部署

#### Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

# 安装依赖
COPY package*.json ./
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 创建数据目录
RUN mkdir -p /app/data /app/logs

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# 启动应用
CMD ["npm", "start"]
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  backend:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_PATH=/app/data/platform.db
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - backend
    restart: unless-stopped
```

### 3. 部署脚本

#### 部署脚本 (deploy.sh)
```bash
#!/bin/bash

set -e

echo "🚀 Starting deployment..."

# 拉取最新代码
git pull origin main

# 安装依赖
npm ci

# 运行测试
npm test

# 构建应用
npm run build

# 数据库迁移
npm run migrate

# 重启服务
pm2 reload ecosystem.config.js

echo "✅ Deployment completed successfully!"
```

## 监控和日志

### 1. 日志配置

#### 日志工具 (utils/logger.ts)
```typescript
import winston from 'winston'
import { config } from '../config'

const logger = winston.createLogger({
  level: config.logging.level,
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'qimo-backend' },
  transports: [
    new winston.transports.File({
      filename: config.logging.file,
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5
    })
  ]
})

if (config.env !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }))
}

export { logger }
```

### 2. 健康检查

#### 健康检查端点 (routes/health.ts)
```typescript
import Router from '@koa/router'
import { database } from '../database'
import { successResponse, errorResponse } from '../utils/response'
import type { Context } from 'koa'

const router = new Router()

router.get('/health', async (ctx: Context) => {
  try {
    // 检查数据库连接
    const result = database.prepare('SELECT 1 as test').get()

    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      database: result ? 'connected' : 'disconnected'
    }

    ctx.body = successResponse(healthStatus)
  } catch (error) {
    ctx.status = 503
    ctx.body = errorResponse('SERVICE_UNAVAILABLE', 'Health check failed')
  }
})

export default router
```

### 3. 性能监控

#### 性能中间件 (middleware/performance.ts)
```typescript
import type { Context, Next } from 'koa'
import { logger } from '../utils/logger'

export const performanceMiddleware = async (ctx: Context, next: Next) => {
  const start = Date.now()

  await next()

  const duration = Date.now() - start

  // 记录慢请求
  if (duration > 1000) {
    logger.warn('Slow request detected', {
      method: ctx.method,
      url: ctx.url,
      duration,
      status: ctx.status
    })
  }

  // 添加响应头
  ctx.set('X-Response-Time', `${duration}ms`)
}
```

## 故障排除

### 1. 常见问题

#### 数据库连接问题
```bash
# 检查数据库文件权限
ls -la data/platform.db

# 检查数据库完整性
sqlite3 data/platform.db "PRAGMA integrity_check;"

# 重建数据库索引
sqlite3 data/platform.db "REINDEX;"
```

#### 内存泄漏问题
```bash
# 监控内存使用
node --inspect app.js

# 生成堆快照
kill -USR2 <pid>
```

### 2. 调试技巧

#### 开启调试模式
```bash
# 设置调试级别
export LOG_LEVEL=debug

# 启用SQL查询日志
export DATABASE_VERBOSE=true

# 启动调试服务器
npm run dev:debug
```

#### 性能分析
```bash
# 使用clinic.js进行性能分析
npx clinic doctor -- node dist/app.js
npx clinic flame -- node dist/app.js
```

---

**文档版本**: v1.0
**创建时间**: 2025年07月27日
**作者**: Alex (Engineer)
**版权**: 随影
```

## 数据访问层最佳实践

### 权限控制查询模式

在设计DAO层方法时，需要考虑不同用户角色的数据访问权限：

```typescript
// ❌ 不推荐：硬编码查询条件
findById(id: number): Subject | null {
  const sql = 'SELECT * FROM subjects WHERE id = ? AND status = 1'; // 只能查询启用的
  // ...
}

// ✅ 推荐：灵活的权限控制
findById(id: number, includeDisabled: boolean = false): Subject | null {
  const sql = includeDisabled
    ? 'SELECT * FROM subjects WHERE id = ?'
    : 'SELECT * FROM subjects WHERE id = ? AND status = 1';
  // ...
}
```

### 管理员操作模式

管理员操作需要更高的数据访问权限：

```typescript
// Service层调用示例
class SubjectService {
  async getSubjectById(id: number, includeStats: boolean = false): Promise<SubjectWithStats | null> {
    // 管理员可以查看所有学科，包括禁用的
    const subject = subjectDao.findById(id, true); // includeDisabled = true
    if (!subject) {
      return null;
    }
    // ...
  }

  async updateSubject(id: number, updates: Partial<Subject>): Promise<boolean> {
    // 检查学科是否存在（包括禁用的）
    const existingSubject = await this.getSubjectById(id);
    if (!existingSubject) {
      throw new Error('学科不存在');
    }
    // ...
  }
}
```

### 响应处理最佳实践

正确使用ResponseHelper避免参数错误：

```typescript
// ❌ 错误：将消息作为statusCode参数传递
ctx.apiResponse.success(data, '操作成功'); // 第二个参数应该是statusCode

// ✅ 正确：只传递数据，使用默认状态码200
ctx.apiResponse.success(data);

// ✅ 正确：指定状态码
ctx.apiResponse.success(data, 201); // 创建成功
```

### 数据库查询优化

合理使用索引和查询条件：

```typescript
// ✅ 推荐：使用索引字段进行查询
const sql = 'SELECT * FROM subjects WHERE id = ?'; // id是主键，有索引

// ✅ 推荐：复合查询条件
const sql = 'SELECT * FROM subjects WHERE status = ? ORDER BY sort_order ASC'; // status和sort_order都有索引
```

---

**文档版本**: v1.1
**创建时间**: 2025年07月27日
**最后更新**: 2025年07月27日
**作者**: Alex (Engineer)
**版权**: 随影
