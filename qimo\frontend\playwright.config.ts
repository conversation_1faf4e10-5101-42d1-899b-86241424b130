import { defineConfig, devices } from '@playwright/test'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

/**
 * Playwright测试配置
 * 配置浏览器、测试超时、并发设置等
 */
export default defineConfig({
  // 测试目录
  testDir: './tests',

  // 全局测试超时 (30秒)
  timeout: 30 * 1000,

  // 期望超时 (5秒)
  expect: {
    timeout: 5 * 1000,
  },

  // 失败时重试次数
  retries: process.env.CI ? 2 : 0,

  // 并发工作进程数
  workers: process.env.CI ? 1 : undefined,

  // 测试报告配置
  reporter: [
    ['html', { outputFolder: 'test-results/html-report' }],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/junit.xml' }],
    ['list']
  ],

  // 全局设置
  use: {
    // 基础URL
    baseURL: 'http://localhost:5173',

    // 浏览器上下文选项
    viewport: { width: 1280, height: 720 },

    // 操作超时
    actionTimeout: 10 * 1000,

    // 导航超时
    navigationTimeout: 15 * 1000,

    // 截图设置
    screenshot: 'only-on-failure',

    // 视频录制
    video: 'retain-on-failure',

    // 跟踪设置
    trace: 'retain-on-failure',

    // 忽略HTTPS错误
    ignoreHTTPSErrors: true,
  },

  // 项目配置 - 多浏览器测试
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },

    // 移动端测试
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
  ],

  // 测试服务器配置
  webServer: [
    {
      command: 'cd ../backend && npm run dev',
      port: 3000,
      reuseExistingServer: !process.env.CI,
      timeout: 30 * 1000,
    },
    {
      command: 'npm run dev',
      port: 5173,
      reuseExistingServer: !process.env.CI,
      timeout: 30 * 1000,
    },
  ],

  // 输出目录
  outputDir: 'test-results/artifacts',

  // 全局设置和拆卸 (暂时禁用)
  // globalSetup: path.resolve(__dirname, '../tests/utils/global-setup.ts'),
  // globalTeardown: path.resolve(__dirname, '../tests/utils/global-teardown.ts'),
})
