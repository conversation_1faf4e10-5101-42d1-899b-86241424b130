// 临时测试文件，用于验证路由配置
import router from './routes/index.js'

function testRouteConfiguration() {
  console.log('🧪 Testing Route Configuration...')
  
  try {
    // 获取所有注册的路由
    const routes = router.stack
    
    console.log(`📊 Total registered routes: ${routes.length}`)
    
    // 检查文件浏览相关路由是否正确注册
    const expectedRoutes = [
      'GET /api/subjects/:id/files',
      'GET /api/subjects/:id/search', 
      'GET /api/files/:id/breadcrumb'
    ]
    
    const registeredRoutes = routes.map(layer => {
      const methods = layer.methods.join(', ')
      const path = layer.path
      return `${methods} ${path}`
    })
    
    console.log('\n📋 Registered routes:')
    registeredRoutes.forEach(route => {
      console.log(`  - ${route}`)
    })
    
    // 验证预期路由是否存在
    console.log('\n✅ Route validation:')
    expectedRoutes.forEach(expectedRoute => {
      const found = registeredRoutes.some(route => 
        route.includes(expectedRoute.split(' ')[1]) && 
        route.includes(expectedRoute.split(' ')[0])
      )
      console.log(`  ${found ? '✅' : '❌'} ${expectedRoute}: ${found ? 'FOUND' : 'MISSING'}`)
    })
    
    // 检查健康检查路由
    const healthRoute = registeredRoutes.find(route => route.includes('/api/health'))
    console.log(`  ${healthRoute ? '✅' : '❌'} Health check route: ${healthRoute ? 'FOUND' : 'MISSING'}`)
    
    // 检查学科管理路由
    const subjectRoutes = registeredRoutes.filter(route => route.includes('/api/subjects'))
    console.log(`  ✅ Subject routes count: ${subjectRoutes.length}`)
    
    console.log('\n🎉 Route configuration test completed!')
    
  } catch (error) {
    console.error('❌ Route test failed:', error)
  }
}

// 只在直接运行时执行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testRouteConfiguration()
}

export { testRouteConfiguration }
