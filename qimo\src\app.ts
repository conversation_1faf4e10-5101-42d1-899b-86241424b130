/**
 * Koa应用主文件
 * 应用的入口点，配置所有中间件和路由
 */

import Koa from 'koa';
import { config } from './config';
import { setupMiddleware } from './middleware';
import { notFoundHandler } from './middleware/error';
import { logAppStart, logAppShutdown } from './middleware/logger';
import { initDatabase } from './database/connection';
import router from './routes';

// 初始化数据库连接
initDatabase();

// 创建Koa应用实例
const app = new Koa();

// 设置应用名称
app.name = config.app.name;

// 配置中间件
setupMiddleware(app);

// 注册路由
app.use(router.routes());
app.use(router.allowedMethods());

// 404处理（放在最后）
app.use(notFoundHandler());

// 错误事件监听
app.on('error', (err, ctx) => {
  console.error('应用级错误:', {
    error: err.message,
    stack: err.stack,
    url: ctx?.url,
    method: ctx?.method,
    ip: ctx?.ip,
    userAgent: ctx?.get('User-Agent')
  });
});

// 启动服务器
function startServer(): void {
  const server = app.listen(config.app.port, config.app.host, () => {
    logAppStart();
  });

  // 优雅关闭处理
  process.on('SIGTERM', () => {
    console.log('收到SIGTERM信号，开始优雅关闭...');
    server.close(() => {
      logAppShutdown();
      process.exit(0);
    });
  });

  process.on('SIGINT', () => {
    console.log('收到SIGINT信号，开始优雅关闭...');
    server.close(() => {
      logAppShutdown();
      process.exit(0);
    });
  });

  // 未捕获异常处理
  process.on('uncaughtException', (err) => {
    console.error('未捕获的异常:', err);
    process.exit(1);
  });

  process.on('unhandledRejection', (reason, promise) => {
    console.error('未处理的Promise拒绝:', reason);
    console.error('Promise:', promise);
    process.exit(1);
  });
}

// 如果直接运行此文件，则启动服务器
if (require.main === module) {
  startServer();
}

// 导出应用实例（用于测试）
export default app;