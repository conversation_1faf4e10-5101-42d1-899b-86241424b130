<template>
  <div class="file-viewer-view">
    <div class="page-header">
      <a-button @click="goBack" class="back-button">
        <ArrowLeftOutlined />
        返回
      </a-button>
      <div v-if="fileStore.currentFile" class="file-info">
        <h1>{{ fileStore.currentFile.name }}</h1>
        <div class="file-meta">
          <span class="meta-item">
            <FileOutlined />
            {{ formatFileSize(fileStore.currentFile.size) }}
          </span>
          <span class="meta-item">
            <ClockCircleOutlined />
            {{ formatDate(fileStore.currentFile.updated_at) }}
          </span>
        </div>
      </div>
    </div>
    
    <div class="content-container">
      <!-- 加载状态 -->
      <div v-if="fileStore.isLoading" class="loading-container">
        <a-spin size="large" />
        <p>正在加载文件内容...</p>
      </div>
      
      <!-- 错误状态 -->
      <a-alert
        v-else-if="fileStore.hasError"
        type="error"
        :message="fileStore.error"
        show-icon
        closable
        @close="fileStore.clearError"
      />
      
      <!-- 文件内容 -->
      <div v-else-if="fileStore.currentFile" class="file-content">
        <div class="content-viewer">
          <div v-html="renderedContent" class="markdown-content"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowLeftOutlined, FileOutlined, ClockCircleOutlined } from '@ant-design/icons-vue'
import { useFileStore } from '@/stores/file'
import { apiUtils } from '@/utils/api'

const router = useRouter()
const route = useRoute()
const fileStore = useFileStore()

const fileId = computed(() => Number(route.params.id))

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  return apiUtils.formatFileSize(bytes)
}

// 格式化日期
const formatDate = (dateString: string): string => {
  return apiUtils.formatDate(dateString)
}

// 渲染内容（简单的Markdown渲染）
const renderedContent = computed(() => {
  if (!fileStore.currentFile?.content) return ''
  
  let content = fileStore.currentFile.content
  
  // 简单的Markdown渲染
  content = content
    // 标题
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    // 粗体
    .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
    // 斜体
    .replace(/\*(.*)\*/gim, '<em>$1</em>')
    // 代码块
    .replace(/```([\s\S]*?)```/gim, '<pre><code>$1</code></pre>')
    // 行内代码
    .replace(/`([^`]*)`/gim, '<code>$1</code>')
    // 链接
    .replace(/\[([^\]]*)\]\(([^\)]*)\)/gim, '<a href="$2" target="_blank">$1</a>')
    // 换行
    .replace(/\n/gim, '<br>')
  
  return content
})

// 返回上一页
const goBack = () => {
  router.back()
}

// 加载文件内容
const loadFile = async () => {
  try {
    await fileStore.fetchFileContent(fileId.value)
  } catch (error) {
    console.error('加载文件内容失败:', error)
  }
}

onMounted(() => {
  loadFile()
})
</script>

<style scoped>
.file-viewer-view {
  min-height: 100vh;
  background: #f8fafc;
}

.page-header {
  background: white;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.file-info h1 {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #2d3748;
}

.file-meta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #718096;
  font-size: 0.9rem;
}

.content-container {
  max-width: 95%;
  margin: 0 auto;
  padding: 2rem;
  width: 100%;
}

@media (min-width: 768px) {
  .content-container {
    max-width: 90%;
    padding: 2rem 3rem;
  }
}

@media (min-width: 1200px) {
  .content-container {
    max-width: 85%;
    padding: 2rem 3rem;
  }
}

@media (min-width: 1600px) {
  .content-container {
    max-width: 80%;
    padding: 2rem 4rem;
  }
}

.loading-container {
  text-align: center;
  padding: 4rem;
}

.loading-container p {
  margin-top: 1rem;
  color: #718096;
}

.file-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.content-viewer {
  padding: 2rem;
}

.markdown-content {
  line-height: 1.8;
  color: #2d3748;
}

.markdown-content :deep(h1) {
  font-size: 2rem;
  font-weight: 600;
  margin: 1.5rem 0 1rem 0;
  color: #1a202c;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 0.5rem;
}

.markdown-content :deep(h2) {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1.25rem 0 0.75rem 0;
  color: #2d3748;
}

.markdown-content :deep(h3) {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 1rem 0 0.5rem 0;
  color: #4a5568;
}

.markdown-content :deep(p) {
  margin: 0.75rem 0;
}

.markdown-content :deep(strong) {
  font-weight: 600;
  color: #1a202c;
}

.markdown-content :deep(em) {
  font-style: italic;
  color: #4a5568;
}

.markdown-content :deep(code) {
  background: #f7fafc;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
  color: #e53e3e;
}

.markdown-content :deep(pre) {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.markdown-content :deep(pre code) {
  background: transparent;
  color: inherit;
  padding: 0;
}

.markdown-content :deep(a) {
  color: #3182ce;
  text-decoration: none;
}

.markdown-content :deep(a:hover) {
  text-decoration: underline;
}

@media (max-width: 768px) {
  .page-header {
    padding: 1rem;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .content-container {
    padding: 1rem;
  }
  
  .content-viewer {
    padding: 1rem;
  }
}
</style>
