// 文件节点类型
export interface FileNode {
  id: string                    // UUID
  subject_id: string           // 所属学科ID
  name: string                 // 文件/文件夹名称
  type: 'file' | 'folder'      // 文件类型
  path: string                 // 完整相对路径
  parent_id: string | null     // 父节点ID
  size: number | null          // 文件大小（字节）
  created_at: number           // 创建时间戳
}

// 面包屑导航项
export interface BreadcrumbItem {
  id: string                   // 节点ID
  name: string                 // 显示名称
  path: string                 // 路径
}

// 文件浏览请求参数
export interface GetFilesByParentRequest {
  subject_id: string           // 学科ID
  parent_id?: string           // 父节点ID（可选，为空表示根目录）
  search?: string              // 搜索关键词（可选）
  page?: number                // 页码（默认1）
  limit?: number               // 每页数量（默认20）
}

// 文件浏览响应
export interface GetFilesByParentResponse {
  files: FileNode[]            // 文件列表
  total: number                // 总数量
  page: number                 // 当前页码
  limit: number                // 每页数量
  has_more: boolean            // 是否有更多
}

// 面包屑导航请求参数
export interface GetBreadcrumbRequest {
  subject_id: string           // 学科ID
  node_id?: string             // 当前节点ID（可选，为空表示根目录）
}

// 面包屑导航响应
export interface GetBreadcrumbResponse {
  breadcrumb: BreadcrumbItem[] // 面包屑路径
}

// 文件搜索请求参数
export interface SearchFilesRequest {
  subject_id: string           // 学科ID
  query: string                // 搜索关键词
  type?: 'file' | 'folder' | 'all' // 文件类型过滤（默认all）
  page?: number                // 页码（默认1）
  limit?: number               // 每页数量（默认20）
}

// 文件搜索响应
export interface SearchFilesResponse {
  files: FileNode[]            // 搜索结果
  total: number                // 总数量
  page: number                 // 当前页码
  limit: number                // 每页数量
  has_more: boolean            // 是否有更多
  query: string                // 搜索关键词
}
