/**
 * 学科相关路由
 * 处理学科的查询、创建、更新、删除等操作
 */

import Router from 'koa-router';
import { Context } from 'koa';
import { subjectService } from '../services/subjectService';
import { fileService } from '../services/fileService';

// 创建学科路由实例
const router = new Router();

/**
 * 获取所有学科列表
 * GET /api/subjects
 */
router.get('/', async (ctx: Context) => {
  try {
    const includeStats = ctx.query.include_stats === 'true';
    const subjects = await subjectService.getAllSubjects(includeStats);
    ctx.apiResponse.success(subjects);
  } catch (error) {
    console.error('GET /api/subjects error:', error);
    const message = error instanceof Error ? error.message : '获取学科列表失败';
    ctx.apiResponse.error('DATABASE_ERROR', message);
  }
});

/**
 * 获取单个学科详情
 * GET /api/subjects/:id
 */
router.get('/:id', async (ctx: Context) => {
  try {
    const { id } = ctx.params;

    // 参数验证
    if (!id || isNaN(Number(id))) {
      ctx.apiResponse.badRequest('学科ID必须是有效的数字');
      return;
    }

    const includeStats = ctx.query.include_stats === 'true';
    const subject = await subjectService.getSubjectById(Number(id), includeStats);

    if (!subject) {
      ctx.apiResponse.notFound('学科不存在');
      return;
    }

    ctx.apiResponse.success(subject);
  } catch (error) {
    console.error('GET /api/subjects/:id error:', error);
    const message = error instanceof Error ? error.message : '获取学科详情失败';
    ctx.apiResponse.error('DATABASE_ERROR', message);
  }
});

/**
 * 获取学科的文件结构
 * GET /api/subjects/:id/files
 */
router.get('/:id/files', async (ctx: Context) => {
  try {
    const { id } = ctx.params;

    // 参数验证
    if (!id || isNaN(Number(id))) {
      ctx.apiResponse.badRequest('学科ID必须是有效的数字');
      return;
    }

    const fileTree = await fileService.getSubjectFileTree(Number(id));
    ctx.apiResponse.success(fileTree);
  } catch (error) {
    console.error('GET /api/subjects/:id/files error:', error);
    const message = error instanceof Error ? error.message : '获取文件结构失败';

    if (message.includes('学科不存在')) {
      ctx.apiResponse.notFound(message);
    } else {
      ctx.apiResponse.error('DATABASE_ERROR', message);
    }
  }
});

/**
 * 创建新学科（管理员功能）
 * POST /api/subjects
 */
router.post('/', async (ctx: Context) => {
  try {
    const { name, description } = ctx.request.body as { name?: string; description?: string };

    // 参数验证
    if (!name || name.trim().length === 0) {
      ctx.apiResponse.badRequest('学科名称不能为空');
      return;
    }

    if (name.length > 100) {
      ctx.apiResponse.badRequest('学科名称不能超过100个字符');
      return;
    }

    // 创建学科
    const subjectId = await subjectService.createSubject({
      name: name.trim(),
      description: description?.trim() || '',
      status: 1,
      sort_order: 0
    });

    // 获取创建的学科详情
    const newSubject = await subjectService.getSubjectById(subjectId, true);
    ctx.apiResponse.success(newSubject, '学科创建成功');
  } catch (error) {
    console.error('POST /api/subjects error:', error);
    const message = error instanceof Error ? error.message : '创建学科失败';

    if (message.includes('已存在')) {
      ctx.apiResponse.conflict(message);
    } else {
      ctx.apiResponse.error('DATABASE_ERROR', message);
    }
  }
});

/**
 * 更新学科信息（管理员功能）
 * PUT /api/subjects/:id
 */
router.put('/:id', async (ctx: Context) => {
  try {
    const { id } = ctx.params;
    const { name, description, status, sort_order } = ctx.request.body as {
      name?: string;
      description?: string;
      status?: number;
      sort_order?: number;
    };

    // 参数验证
    if (!id || isNaN(Number(id))) {
      ctx.apiResponse.badRequest('学科ID必须是有效的数字');
      return;
    }

    // 构建更新数据
    const updates: any = {};
    if (name !== undefined) {
      if (!name || name.trim().length === 0) {
        ctx.apiResponse.badRequest('学科名称不能为空');
        return;
      }
      if (name.length > 100) {
        ctx.apiResponse.badRequest('学科名称不能超过100个字符');
        return;
      }
      updates.name = name.trim();
    }
    if (description !== undefined) {
      updates.description = description?.trim() || '';
    }
    if (status !== undefined) {
      if (![0, 1].includes(status)) {
        ctx.apiResponse.badRequest('状态值必须是0或1');
        return;
      }
      updates.status = status;
    }
    if (sort_order !== undefined) {
      if (!Number.isInteger(sort_order) || sort_order < 0) {
        ctx.apiResponse.badRequest('排序权重必须是非负整数');
        return;
      }
      updates.sort_order = sort_order;
    }

    if (Object.keys(updates).length === 0) {
      ctx.apiResponse.badRequest('至少需要提供一个更新字段');
      return;
    }

    // 更新学科
    const success = await subjectService.updateSubject(Number(id), updates);
    if (!success) {
      ctx.apiResponse.error('DATABASE_ERROR', '更新学科失败');
      return;
    }

    // 获取更新后的学科详情
    const updatedSubject = await subjectService.getSubjectById(Number(id), true);
    ctx.apiResponse.success(updatedSubject, '学科更新成功');
  } catch (error) {
    console.error('PUT /api/subjects/:id error:', error);
    const message = error instanceof Error ? error.message : '更新学科失败';

    if (message.includes('不存在')) {
      ctx.apiResponse.notFound(message);
    } else if (message.includes('已存在')) {
      ctx.apiResponse.conflict(message);
    } else {
      ctx.apiResponse.error('DATABASE_ERROR', message);
    }
  }
});

/**
 * 删除学科（管理员功能）
 * DELETE /api/subjects/:id
 */
router.delete('/:id', async (ctx: Context) => {
  try {
    const { id } = ctx.params;

    // 参数验证
    if (!id || isNaN(Number(id))) {
      ctx.apiResponse.badRequest('学科ID必须是有效的数字');
      return;
    }

    // 删除学科（软删除）
    const success = await subjectService.deleteSubject(Number(id));
    if (!success) {
      ctx.apiResponse.error('DATABASE_ERROR', '删除学科失败');
      return;
    }

    ctx.apiResponse.success(null, '学科删除成功');
  } catch (error) {
    console.error('DELETE /api/subjects/:id error:', error);
    const message = error instanceof Error ? error.message : '删除学科失败';

    if (message.includes('不存在')) {
      ctx.apiResponse.notFound(message);
    } else {
      ctx.apiResponse.error('DATABASE_ERROR', message);
    }
  }
});

export default router;