import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 管理后台配置
const ADMIN_PANEL_PATH = import.meta.env.VITE_ADMIN_PANEL_PATH || '/admin-panel-abcdef'
const ADMIN_PANEL_ENABLED = import.meta.env.VITE_ADMIN_PANEL_ENABLED !== 'false'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/HomeView.vue'),
    meta: {
      title: '期末复习平台',
      description: '选择学科开始复习'
    }
  },
  {
    path: '/subjects',
    name: 'SubjectList',
    component: () => import('@/views/SubjectListView.vue'),
    meta: {
      title: '学科列表',
      description: '浏览所有可用学科'
    }
  },
  {
    path: '/subjects/:id',
    name: 'SubjectDetail',
    component: () => import('@/views/SubjectDetailView.vue'),
    props: true,
    meta: {
      title: '学科详情',
      description: '浏览学科文件结构'
    }
  },
  {
    path: '/files/:id',
    name: 'FileViewer',
    component: () => import('@/views/FileViewerView.vue'),
    props: true,
    meta: {
      title: '文件查看',
      description: '查看文件内容'
    }
  },
  {
    path: '/search',
    name: 'Search',
    component: () => import('@/views/SearchView.vue'),
    meta: {
      title: '搜索',
      description: '搜索文件和内容'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFoundView.vue'),
    meta: {
      title: '页面未找到',
      description: '请检查URL是否正确'
    }
  }
]

// 管理后台路由配置
const adminRoutes: RouteRecordRaw[] = ADMIN_PANEL_ENABLED ? [
  {
    path: ADMIN_PANEL_PATH,
    name: 'AdminPanel',
    component: () => import('@/layouts/AdminLayout.vue'),
    meta: {
      title: '管理后台',
      description: '系统管理后台',
      requiresAdmin: true
    },
    children: [
      {
        path: '',
        name: 'AdminDashboard',
        component: () => import('@/views/admin/DashboardView.vue'),
        meta: {
          title: '管理面板',
          description: '管理后台首页'
        }
      },
      {
        path: 'subjects',
        name: 'AdminSubjects',
        component: () => import('@/views/admin/SubjectManagement.vue'),
        meta: {
          title: '学科管理',
          description: '管理学科分类'
        }
      }
    ]
  }
] : []

// 合并所有路由
const allRoutes = [...routes, ...adminRoutes]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: allRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 期末复习平台`
  } else {
    document.title = '期末复习平台'
  }

  // 管理后台访问控制
  if (to.meta?.requiresAdmin) {
    // 检查是否启用管理后台
    if (!ADMIN_PANEL_ENABLED) {
      next({ name: 'NotFound' })
      return
    }

    // 检查是否是正确的管理后台路径
    if (!to.path.startsWith(ADMIN_PANEL_PATH)) {
      next({ name: 'NotFound' })
      return
    }

    // 设置管理后台访问标识
    const adminHeader = import.meta.env.VITE_ADMIN_ACCESS_HEADER || 'x-admin-path'
    // 在实际应用中，这里可以添加更复杂的权限验证逻辑
    // 目前只是设置标识，实际的权限验证在后端进行
  }

  next()
})

export default router
