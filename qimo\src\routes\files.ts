/**
 * 文件相关路由
 * 处理文件的查询、上传、下载等操作
 */

import Router from 'koa-router';
import { Context } from 'koa';
import { fileService } from '../services/fileService';

// 创建文件路由实例
const router = new Router();

/**
 * 搜索文件
 * GET /api/files/search?q=keyword&subject_id=1
 */
router.get('/search', async (ctx: Context) => {
  try {
    const { q, subject_id, type } = ctx.query;

    // 参数验证
    if (!q || typeof q !== 'string' || q.trim().length === 0) {
      ctx.apiResponse.badRequest('搜索关键词不能为空');
      return;
    }

    const subjectIdNum = subject_id ? Number(subject_id) : undefined;
    const fileType = type === 'file' || type === 'folder' ? type : undefined;

    const searchResult = await fileService.searchFiles(q, subjectIdNum, fileType);
    ctx.apiResponse.success(searchResult);
  } catch (error) {
    console.error('GET /api/files/search error:', error);
    const message = error instanceof Error ? error.message : '搜索文件失败';

    if (message.includes('学科不存在') || message.includes('关键词')) {
      ctx.apiResponse.badRequest(message);
    } else {
      ctx.apiResponse.error('DATABASE_ERROR', message);
    }
  }
});

/**
 * 获取文件内容
 * GET /api/files/:id
 */
router.get('/:id', async (ctx: Context) => {
  try {
    const { id } = ctx.params;

    // 参数验证
    if (!id || isNaN(Number(id))) {
      ctx.apiResponse.badRequest('文件ID必须是有效的数字');
      return;
    }

    const fileWithContent = await fileService.getFileContent(Number(id));
    ctx.apiResponse.success(fileWithContent);
  } catch (error) {
    console.error('GET /api/files/:id error:', error);
    const message = error instanceof Error ? error.message : '获取文件内容失败';

    if (message.includes('文件不存在') || message.includes('不存在')) {
      ctx.apiResponse.notFound(message);
    } else if (message.includes('只能获取文件类型')) {
      ctx.apiResponse.badRequest(message);
    } else {
      ctx.apiResponse.error('DATABASE_ERROR', message);
    }
  }
});

/**
 * 获取文件元信息（不包含内容）
 * GET /api/files/:id/meta
 */
router.get('/:id/meta', async (ctx: Context) => {
  try {
    const { id } = ctx.params;

    // 参数验证
    if (!id || isNaN(Number(id))) {
      ctx.apiResponse.badRequest('文件ID必须是有效的数字');
      return;
    }

    const fileMetadata = await fileService.getFileMetadata(Number(id));
    ctx.apiResponse.success(fileMetadata);
  } catch (error) {
    console.error('GET /api/files/:id/meta error:', error);
    const message = error instanceof Error ? error.message : '获取文件元数据失败';

    if (message.includes('文件不存在') || message.includes('不存在')) {
      ctx.apiResponse.notFound(message);
    } else {
      ctx.apiResponse.error('DATABASE_ERROR', message);
    }
  }
});



/**
 * 上传文件（预留，管理员功能）
 * POST /api/files
 */
router.post('/', async (ctx: Context) => {
  // TODO: 在P0-基础文件上传功能中实现
  ctx.apiResponse.error('FEATURE_NOT_IMPLEMENTED', '此功能将在文件上传任务中实现', null, 501);
});

/**
 * 更新文件内容（预留，管理员功能）
 * PUT /api/files/:id
 */
router.put('/:id', async (ctx: Context) => {
  // TODO: 在P1阶段实现管理员文件编辑功能
  ctx.apiResponse.error('FEATURE_NOT_IMPLEMENTED', '此功能将在P1阶段实现', null, 501);
});

/**
 * 删除文件（预留，管理员功能）
 * DELETE /api/files/:id
 */
router.delete('/:id', async (ctx: Context) => {
  // TODO: 在P1阶段实现管理员文件删除功能
  ctx.apiResponse.error('FEATURE_NOT_IMPLEMENTED', '此功能将在P1阶段实现', null, 501);
});

/**
 * 下载文件
 * GET /api/files/:id/download
 */
router.get('/:id/download', async (ctx: Context) => {
  const { id } = ctx.params;

  // 参数验证
  if (!id || isNaN(Number(id))) {
    ctx.apiResponse.badRequest('文件ID必须是有效的数字');
    return;
  }

  // TODO: 在任务3中实现具体的文件下载逻辑
  if (Number(id) > 10) {
    ctx.apiResponse.notFound('文件不存在');
    return;
  }

  // 模拟文件下载
  ctx.set('Content-Type', 'application/octet-stream');
  ctx.set('Content-Disposition', 'attachment; filename="基础概念.md"');
  ctx.body = '# 基础概念\n\n这是下载的文件内容...';
});

export default router;