import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { fileApi } from '@/utils/api'
import type { FileNode, FileWithContent, SearchResult } from '@/types/api'

export const useFileStore = defineStore('file', () => {
  // 状态
  const currentFile = ref<FileWithContent | null>(null)
  const searchResults = ref<SearchResult[]>([])
  const recentFiles = ref<FileNode[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 缓存状态
  const fileCache = ref<Map<number, { data: FileWithContent, timestamp: number }>>(new Map())
  const searchCache = ref<Map<string, { data: SearchResult[], timestamp: number }>>(new Map())
  const CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

  // 计算属性
  const hasCurrentFile = computed(() => !!currentFile.value)
  const hasSearchResults = computed(() => searchResults.value.length > 0)
  const hasRecentFiles = computed(() => recentFiles.value.length > 0)
  const isLoading = computed(() => loading.value)
  const hasError = computed(() => !!error.value)

  // 方法
  const setLoading = (value: boolean) => {
    loading.value = value
  }

  const setError = (message: string | null) => {
    error.value = message
  }

  const clearError = () => {
    error.value = null
  }

  // 检查缓存是否有效
  const isCacheValid = (timestamp: number) => {
    return Date.now() - timestamp < CACHE_DURATION
  }

  // 获取文件内容
  const fetchFileContent = async (id: number, forceRefresh = false) => {
    try {
      // 检查缓存
      const cached = fileCache.value.get(id)
      if (!forceRefresh && cached && isCacheValid(cached.timestamp)) {
        console.log(`📦 Using cached file content for file ${id}`)
        currentFile.value = cached.data
        addToRecentFiles(cached.data)
        return cached.data
      }

      setLoading(true)
      clearError()

      const response = await fileApi.getFileContent(id)
      const data = response.data.data

      // 更新状态和缓存
      currentFile.value = data
      fileCache.value.set(id, {
        data,
        timestamp: Date.now()
      })

      // 添加到最近访问文件
      addToRecentFiles(data)

      console.log(`🔄 Fetched fresh file content for file ${id}`)
      return data
    } catch (err: any) {
      const message = err?.response?.data?.error?.message || '获取文件内容失败'
      setError(message)
      throw err
    } finally {
      setLoading(false)
    }
  }

  // 获取文件元数据
  const fetchFileMetadata = async (id: number) => {
    try {
      setLoading(true)
      clearError()

      const response = await fileApi.getFileMetadata(id)
      return response.data.data
    } catch (err: any) {
      const message = err?.response?.data?.error?.message || '获取文件信息失败'
      setError(message)
      throw err
    } finally {
      setLoading(false)
    }
  }

  // 搜索文件
  const searchFiles = async (query: string, subjectId?: number, type?: 'file' | 'folder', forceRefresh = false) => {
    try {
      // 生成缓存键
      const cacheKey = `${query}-${subjectId || 'all'}-${type || 'all'}`

      // 检查缓存
      const cached = searchCache.value.get(cacheKey)
      if (!forceRefresh && cached && isCacheValid(cached.timestamp)) {
        console.log(`📦 Using cached search results for: ${query}`)
        searchResults.value = cached.data
        return { results: cached.data, query, total: cached.data.length }
      }

      setLoading(true)
      clearError()

      const response = await fileApi.searchFiles(query, subjectId, type)
      const results = response.data.data.results

      // 更新状态和缓存
      searchResults.value = results
      searchCache.value.set(cacheKey, {
        data: results,
        timestamp: Date.now()
      })

      console.log(`🔄 Fetched fresh search results for: ${query}`)
      return response.data.data
    } catch (err: any) {
      const message = err?.response?.data?.error?.message || '搜索失败'
      setError(message)
      throw err
    } finally {
      setLoading(false)
    }
  }

  // 添加到最近访问文件
  const addToRecentFiles = (file: FileWithContent) => {
    const fileNode: FileNode = {
      id: file.id,
      subject_id: file.subject_id,
      parent_id: file.parent_id,
      name: file.name,
      type: file.type,
      path: file.path,
      size: file.size,
      mime_type: file.mime_type,
      content_hash: file.content_hash,
      created_at: file.created_at,
      updated_at: file.updated_at,
      is_deleted: file.is_deleted
    }

    // 移除已存在的相同文件
    recentFiles.value = recentFiles.value.filter(f => f.id !== file.id)

    // 添加到开头
    recentFiles.value.unshift(fileNode)

    // 限制最近文件数量
    if (recentFiles.value.length > 10) {
      recentFiles.value = recentFiles.value.slice(0, 10)
    }
  }

  // 清空缓存
  const clearCache = () => {
    fileCache.value.clear()
    searchCache.value.clear()
    console.log('🗑️ File cache cleared')
  }

  // 清空搜索结果
  const clearSearchResults = () => {
    searchResults.value = []
  }

  // 设置当前文件
  const setCurrentFile = (file: FileWithContent | null) => {
    currentFile.value = file
  }

  // 清空数据
  const clearData = () => {
    currentFile.value = null
    searchResults.value = []
    clearError()
  }

  // 清空最近文件
  const clearRecentFiles = () => {
    recentFiles.value = []
  }

  // 清空所有数据和缓存
  const clearAll = () => {
    clearData()
    clearRecentFiles()
    clearCache()
  }

  return {
    // 状态
    currentFile,
    searchResults,
    recentFiles,
    loading,
    error,

    // 计算属性
    hasCurrentFile,
    hasSearchResults,
    hasRecentFiles,
    isLoading,
    hasError,

    // 方法
    setLoading,
    setError,
    clearError,
    fetchFileContent,
    fetchFileMetadata,
    searchFiles,
    addToRecentFiles,
    clearSearchResults,
    setCurrentFile,
    clearData,
    clearRecentFiles,
    clearCache,
    clearAll
  }
})
