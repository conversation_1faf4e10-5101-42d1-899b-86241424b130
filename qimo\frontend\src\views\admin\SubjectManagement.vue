<template>
  <div class="subject-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">学科管理</h1>
          <p class="page-description">管理系统中的学科分类，包括创建、编辑、删除等操作</p>
        </div>
        <div class="header-right">
          <a-button type="primary" @click="showCreateModal">
            <template #icon>
              <PlusOutlined />
            </template>
            新建学科
          </a-button>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <a-card :bordered="false" class="search-card">
        <a-row :gutter="16" align="middle">
          <a-col :span="8">
            <a-input-search
              v-model:value="searchKeyword"
              placeholder="搜索学科名称或描述"
              allow-clear
              @search="handleSearch"
              @change="handleSearchChange"
            >
              <template #prefix>
                <SearchOutlined />
              </template>
            </a-input-search>
          </a-col>
          <a-col :span="4">
            <a-select
              v-model:value="statusFilter"
              placeholder="状态筛选"
              allow-clear
              @change="handleStatusFilter"
              style="width: 100%"
            >
              <a-select-option :value="1">启用</a-select-option>
              <a-select-option :value="0">禁用</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="4">
            <a-button @click="handleReset">
              <template #icon>
                <ReloadOutlined />
              </template>
              重置
            </a-button>
          </a-col>
          <a-col :span="8" class="text-right">
            <a-space>
              <a-button
                :disabled="!hasSelected"
                @click="handleBatchEnable"
              >
                批量启用
              </a-button>
              <a-button
                :disabled="!hasSelected"
                @click="handleBatchDisable"
              >
                批量禁用
              </a-button>
              <a-button @click="handleRefresh">
                <template #icon>
                  <ReloadOutlined />
                </template>
                刷新
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <a-card :bordered="false" class="table-card">
        <a-table
          :columns="columns"
          :data-source="paginatedSubjects"
          :loading="loading"
          :pagination="paginationConfig"
          :row-selection="rowSelection"
          row-key="id"
          size="middle"
          @change="handleTableChange"
        >
          <!-- 学科名称列 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div class="subject-name">
                <span class="name-text">{{ record.name }}</span>
                <a-tag v-if="record.description" color="blue" class="name-tag">
                  {{ truncateText(record.description, 20) }}
                </a-tag>
              </div>
            </template>

            <!-- 统计信息列 -->
            <template v-else-if="column.key === 'stats'">
              <div class="stats-info">
                <div class="stat-item">
                  <FileOutlined />
                  <span>{{ record.fileCount || 0 }} 文件</span>
                </div>
                <div class="stat-item">
                  <FolderOutlined />
                  <span>{{ record.folderCount || 0 }} 文件夹</span>
                </div>
                <div class="stat-item">
                  <DatabaseOutlined />
                  <span>{{ formatFileSize(record.totalSize || 0) }}</span>
                </div>
              </div>
            </template>

            <!-- 状态列 -->
            <template v-else-if="column.key === 'status'">
              <a-switch
                :checked="record.status === 1"
                :loading="record.updating"
                @change="(checked) => handleStatusChange(record, checked)"
              >
                <template #checkedChildren>启用</template>
                <template #unCheckedChildren>禁用</template>
              </a-switch>
            </template>

            <!-- 时间列 -->
            <template v-else-if="column.key === 'time'">
              <div class="time-info">
                <div class="time-item">
                  <span class="time-label">创建:</span>
                  <span class="time-value">{{ formatDateTime(record.created_at) }}</span>
                </div>
                <div class="time-item">
                  <span class="time-label">更新:</span>
                  <span class="time-value">{{ formatDateTime(record.updated_at) }}</span>
                </div>
              </div>
            </template>

            <!-- 操作列 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="handleView(record)">
                  <EyeOutlined />
                  查看
                </a-button>
                <a-button type="link" size="small" @click="handleEdit(record)">
                  <EditOutlined />
                  编辑
                </a-button>
                <a-popconfirm
                  title="确定要删除这个学科吗？"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="handleDelete(record)"
                >
                  <a-button type="link" size="small" danger>
                    <DeleteOutlined />
                    删除
                  </a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 创建/编辑学科模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :confirm-loading="modalLoading"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      width="600px"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
        class="subject-form"
      >
        <a-form-item label="学科名称" name="name">
          <a-input
            v-model:value="formData.name"
            placeholder="请输入学科名称"
            :maxlength="50"
            show-count
          />
        </a-form-item>

        <a-form-item label="学科描述" name="description">
          <a-textarea
            v-model:value="formData.description"
            placeholder="请输入学科描述（可选）"
            :rows="4"
            :maxlength="200"
            show-count
          />
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="状态" name="status">
              <a-select v-model:value="formData.status" placeholder="请选择状态">
                <a-select-option :value="1">启用</a-select-option>
                <a-select-option :value="0">禁用</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="排序顺序" name="sort_order">
              <a-input-number
                v-model:value="formData.sort_order"
                placeholder="排序顺序"
                :min="0"
                :max="9999"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>

    <!-- 查看学科详情模态框 -->
    <a-modal
      v-model:open="viewModalVisible"
      title="学科详情"
      :footer="null"
      width="700px"
    >
      <div v-if="currentSubject" class="subject-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="学科ID">
            {{ currentSubject.id }}
          </a-descriptions-item>
          <a-descriptions-item label="学科名称">
            {{ currentSubject.name }}
          </a-descriptions-item>
          <a-descriptions-item label="状态" :span="2">
            <a-tag :color="currentSubject.status === 1 ? 'success' : 'error'">
              {{ currentSubject.status === 1 ? '启用' : '禁用' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="学科描述" :span="2">
            {{ currentSubject.description || '暂无描述' }}
          </a-descriptions-item>
          <a-descriptions-item label="排序顺序">
            {{ currentSubject.sort_order }}
          </a-descriptions-item>
          <a-descriptions-item label="文件统计">
            <div class="detail-stats">
              <div>文件: {{ currentSubject.fileCount || 0 }} 个</div>
              <div>文件夹: {{ currentSubject.folderCount || 0 }} 个</div>
              <div>总大小: {{ formatFileSize(currentSubject.totalSize || 0) }}</div>
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ formatDateTime(currentSubject.created_at) }}
          </a-descriptions-item>
          <a-descriptions-item label="更新时间">
            {{ formatDateTime(currentSubject.updated_at) }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import type { FormInstance, TableColumnsType } from 'ant-design-vue'
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  FileOutlined,
  FolderOutlined,
  DatabaseOutlined
} from '@ant-design/icons-vue'
import { useSubjectManagement, useFormValidation } from '@/composables/useAdminApi'
import { formatDateTime, formatFileSize, truncateText } from '@/utils/format'
import type { Subject } from '@/types/api'

// 组合函数
const {
  loading,
  subjects,
  currentSubject,
  pagination,
  paginatedSubjects,
  fetchSubjects,
  fetchSubjectById,
  createSubject,
  updateSubject,
  deleteSubject,
  batchUpdateStatus,
  search,
  filterByStatus,
  resetSearch,
  refresh
} = useSubjectManagement()

const { validateSubjectName, validateDescription, validateSortOrder } = useFormValidation()

// 响应式状态
const searchKeyword = ref('')
const statusFilter = ref<number | undefined>(undefined)
const selectedRowKeys = ref<number[]>([])

// 模态框状态
const modalVisible = ref(false)
const modalLoading = ref(false)
const modalTitle = ref('')
const isEditing = ref(false)
const editingId = ref<number | null>(null)

const viewModalVisible = ref(false)

// 表单相关
const formRef = ref<FormInstance>()
const formData = reactive({
  name: '',
  description: '',
  status: 1,
  sort_order: 0
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入学科名称', trigger: 'blur' },
    { validator: (_: any, value: string) => {
        const error = validateSubjectName(value)
        return error ? Promise.reject(error) : Promise.resolve()
      }, trigger: 'blur' }
  ],
  description: [
    { validator: (_: any, value: string) => {
        const error = validateDescription(value)
        return error ? Promise.reject(error) : Promise.resolve()
      }, trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  sort_order: [
    { validator: (_: any, value: number) => {
        const error = validateSortOrder(value)
        return error ? Promise.reject(error) : Promise.resolve()
      }, trigger: 'blur' }
  ]
}

// 计算属性
const hasSelected = computed(() => selectedRowKeys.value.length > 0)

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '学科名称',
    dataIndex: 'name',
    key: 'name',
    width: 250,
    ellipsis: true
  },
  {
    title: '统计信息',
    key: 'stats',
    width: 200
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    align: 'center'
  },
  {
    title: '排序',
    dataIndex: 'sort_order',
    key: 'sort_order',
    width: 80,
    align: 'center',
    sorter: (a: Subject, b: Subject) => a.sort_order - b.sort_order
  },
  {
    title: '时间信息',
    key: 'time',
    width: 200
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    align: 'center'
  }
]

// 分页配置
const paginationConfig = computed(() => ({
  ...pagination,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) =>
    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
}))

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys
  },
  onSelectAll: (selected: boolean, selectedRows: Subject[], changeRows: Subject[]) => {
    console.log('Select all:', selected, selectedRows, changeRows)
  }
}

// 事件处理函数
const handleSearch = () => {
  search(searchKeyword.value)
}

const handleSearchChange = () => {
  if (!searchKeyword.value) {
    search('')
  }
}

const handleStatusFilter = () => {
  filterByStatus(statusFilter.value)
}

const handleReset = () => {
  searchKeyword.value = ''
  statusFilter.value = undefined
  selectedRowKeys.value = []
  resetSearch()
}

const handleRefresh = () => {
  refresh()
}

const handleTableChange = (pag: any, filters: any, sorter: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

// 批量操作
const handleBatchEnable = async () => {
  try {
    await batchUpdateStatus(selectedRowKeys.value, 1)
    selectedRowKeys.value = []
  } catch (error) {
    console.error('Batch enable failed:', error)
  }
}

const handleBatchDisable = async () => {
  try {
    await batchUpdateStatus(selectedRowKeys.value, 0)
    selectedRowKeys.value = []
  } catch (error) {
    console.error('Batch disable failed:', error)
  }
}

// 状态切换
const handleStatusChange = async (record: Subject, checked: boolean) => {
  try {
    record.updating = true
    await updateSubject(record.id, { status: checked ? 1 : 0 })
  } catch (error) {
    console.error('Status change failed:', error)
  } finally {
    record.updating = false
  }
}

// 模态框操作
const showCreateModal = () => {
  modalTitle.value = '新建学科'
  isEditing.value = false
  editingId.value = null
  resetFormData()
  modalVisible.value = true
}

const handleEdit = (record: Subject) => {
  modalTitle.value = '编辑学科'
  isEditing.value = true
  editingId.value = record.id

  formData.name = record.name
  formData.description = record.description || ''
  formData.status = record.status
  formData.sort_order = record.sort_order

  modalVisible.value = true
}

const handleView = async (record: Subject) => {
  try {
    const subject = await fetchSubjectById(record.id)
    if (subject) {
      viewModalVisible.value = true
    }
  } catch (error) {
    console.error('Failed to fetch subject details:', error)
  }
}

const handleDelete = async (record: Subject) => {
  try {
    await deleteSubject(record.id)
  } catch (error) {
    console.error('Delete failed:', error)
  }
}

const handleModalOk = async () => {
  try {
    await formRef.value?.validate()
    modalLoading.value = true

    if (isEditing.value && editingId.value) {
      await updateSubject(editingId.value, formData)
    } else {
      await createSubject(formData)
    }

    modalVisible.value = false
    resetFormData()
  } catch (error) {
    console.error('Form validation or submission failed:', error)
  } finally {
    modalLoading.value = false
  }
}

const handleModalCancel = () => {
  modalVisible.value = false
  resetFormData()
}

const resetFormData = () => {
  formData.name = ''
  formData.description = ''
  formData.status = 1
  formData.sort_order = 0
  formRef.value?.resetFields()
}

// 生命周期
onMounted(() => {
  fetchSubjects()
})
</script>

<style scoped>
/* 页面整体布局 */
.subject-management {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 页面头部 */
.page-header {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.header-content {
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.88);
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.page-description {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  margin: 0;
  line-height: 1.4;
}

.header-right {
  flex-shrink: 0;
  margin-left: 24px;
}

/* 搜索区域 */
.search-section {
  flex-shrink: 0;
}

.search-card {
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.search-card :deep(.ant-card-body) {
  padding: 20px 24px;
}

.text-right {
  text-align: right;
}

/* 表格区域 */
.table-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.table-card {
  flex: 1;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.table-card :deep(.ant-card-body) {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-card :deep(.ant-table-wrapper) {
  flex: 1;
}

.table-card :deep(.ant-table) {
  height: 100%;
}

.table-card :deep(.ant-table-tbody) {
  height: 100%;
}

/* 表格内容样式 */
.subject-name {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.name-text {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.88);
  line-height: 1.4;
}

.name-tag {
  font-size: 12px;
  margin: 0;
}

.stats-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
}

.stat-item :deep(.anticon) {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.time-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.time-label {
  color: rgba(0, 0, 0, 0.45);
  min-width: 32px;
}

.time-value {
  color: rgba(0, 0, 0, 0.65);
}

/* 表单样式 */
.subject-form {
  margin-top: 16px;
}

.subject-form :deep(.ant-form-item) {
  margin-bottom: 20px;
}

.subject-form :deep(.ant-form-item-label) {
  font-weight: 500;
}

/* 详情页面样式 */
.subject-detail {
  margin-top: 16px;
}

.detail-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 14px;
}

.detail-stats > div {
  color: rgba(0, 0, 0, 0.65);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    margin-left: 0;
    align-self: flex-start;
  }
}

@media (max-width: 768px) {
  .page-header {
    margin: 0 -16px;
    border-radius: 0;
  }

  .search-section {
    margin: 0 -16px;
  }

  .search-card {
    border-radius: 0;
  }

  .table-section {
    margin: 0 -16px;
  }

  .table-card {
    border-radius: 0;
  }

  .header-content {
    padding: 16px;
  }

  .search-card :deep(.ant-card-body) {
    padding: 16px;
  }

  /* 移动端表格优化 */
  .table-card :deep(.ant-table) {
    font-size: 12px;
  }

  .table-card :deep(.ant-table-thead > tr > th) {
    padding: 8px 4px;
    font-size: 12px;
  }

  .table-card :deep(.ant-table-tbody > tr > td) {
    padding: 8px 4px;
  }

  .stats-info,
  .time-info {
    font-size: 11px;
  }

  .subject-name .name-text {
    font-size: 13px;
  }

  .name-tag {
    font-size: 11px;
  }
}

/* 表格行悬停效果 */
.table-card :deep(.ant-table-tbody > tr:hover > td) {
  background-color: #fafafa;
}

/* 加载状态优化 */
.table-card :deep(.ant-spin-nested-loading) {
  height: 100%;
}

.table-card :deep(.ant-spin-container) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 空状态样式 */
.table-card :deep(.ant-empty) {
  margin: 40px 0;
}

/* 分页样式优化 */
.table-card :deep(.ant-pagination) {
  margin: 16px 0;
  text-align: right;
}

@media (max-width: 768px) {
  .table-card :deep(.ant-pagination) {
    text-align: center;
  }

  .table-card :deep(.ant-pagination-options) {
    display: none;
  }
}

/* 模态框样式优化 */
:deep(.ant-modal-header) {
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 0;
}

:deep(.ant-modal-body) {
  padding-top: 24px;
}

/* 按钮组样式 */
:deep(.ant-space) {
  flex-wrap: wrap;
}

/* 开关组件样式 */
:deep(.ant-switch) {
  min-width: 44px;
}

/* 描述列表样式 */
:deep(.ant-descriptions-item-label) {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

:deep(.ant-descriptions-item-content) {
  color: rgba(0, 0, 0, 0.65);
}
</style>
