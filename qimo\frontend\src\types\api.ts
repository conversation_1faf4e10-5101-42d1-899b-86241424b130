// API响应基础类型
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  error?: {
    code: string
    message: string
  }
  timestamp: string
  requestId: string
}

// 学科类型
export interface Subject {
  id: number
  name: string
  description: string
  created_at: string
  updated_at: string
  status: number
  sort_order: number
  // 可选的统计信息
  file_count?: number
  folder_count?: number
  total_size?: number
}

// 文件节点类型
export interface FileNode {
  id: number
  subject_id: number
  parent_id: number | null
  name: string
  type: 'file' | 'folder'
  path: string
  size: number
  mime_type: string
  content_hash: string
  created_at: string
  updated_at: string
  is_deleted: number
  // 可选的子节点（用于树形结构）
  children?: FileNode[]
}

// 带内容的文件类型
export interface FileWithContent extends FileNode {
  content: string
}

// 搜索结果类型
export interface SearchResult {
  id: number
  subject_id: number
  subject_name: string
  name: string
  type: 'file' | 'folder'
  path: string
  size: number
  created_at: string
  updated_at: string
  // 搜索匹配信息
  match_type: 'name' | 'content'
  highlight?: string
}

// API请求参数类型
export interface GetSubjectsParams {
  include_stats?: boolean
}

export interface SearchFilesParams {
  q: string
  subject_id?: number
  type?: 'file' | 'folder'
}

// 错误类型
export interface ApiError {
  code: string
  message: string
  details?: any
}

// HTTP状态码类型
export type HttpStatusCode = 200 | 201 | 400 | 401 | 403 | 404 | 500

// 请求方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'

// 请求配置类型
export interface RequestConfig {
  method?: HttpMethod
  headers?: Record<string, string>
  params?: Record<string, any>
  data?: any
  timeout?: number
}

// 分页参数类型
export interface PaginationParams {
  page?: number
  limit?: number
  sort?: string
  order?: 'asc' | 'desc'
}

// 分页响应类型
export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
    has_next: boolean
    has_prev: boolean
  }
}

// 文件上传类型（预留）
export interface FileUploadParams {
  file: File
  subject_id: number
  parent_id?: number
  description?: string
}

// 文件上传响应类型（预留）
export interface FileUploadResponse {
  id: number
  name: string
  path: string
  size: number
  mime_type: string
  url: string
}
