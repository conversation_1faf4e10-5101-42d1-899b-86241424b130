/**
 * 数据库连接模块
 * 使用better-sqlite3提供同步API，简化事务处理
 * 支持连接池管理和健康检查
 */

import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

// 数据库配置接口
interface DatabaseConfig {
  path: string;
  readonly: boolean;
  fileMustExist: boolean;
  timeout: number;
  verbose?: (message?: unknown, ...additionalArgs: unknown[]) => void;
}

// 数据库连接类
class DatabaseConnection {
  private db: Database.Database | null = null;
  private config: DatabaseConfig;
  private isInitialized = false;

  constructor(config: Partial<DatabaseConfig> = {}) {
    // 默认配置
    this.config = {
      path: process.env.DATABASE_PATH || path.join(process.cwd(), 'data', 'platform.db'),
      readonly: false,
      fileMustExist: false,
      timeout: 10000, // 10秒超时
      verbose: process.env.NODE_ENV === 'development' ? console.log : undefined,
      ...config
    };
  }

  /**
   * 初始化数据库连接
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // 确保数据库目录存在
      const dbDir = path.dirname(this.config.path);
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }

      // 创建数据库连接
      this.db = new Database(this.config.path, {
        readonly: this.config.readonly,
        fileMustExist: this.config.fileMustExist,
        timeout: this.config.timeout,
        verbose: this.config.verbose
      });

      // 配置数据库选项
      this.db.pragma('journal_mode = WAL'); // 启用WAL模式提升并发性能
      this.db.pragma('synchronous = NORMAL'); // 平衡性能和安全性
      this.db.pragma('cache_size = 1000'); // 设置缓存大小
      this.db.pragma('temp_store = memory'); // 临时表存储在内存中
      this.db.pragma('foreign_keys = ON'); // 启用外键约束

      this.isInitialized = true;
      console.log(`✅ 数据库连接已建立: ${this.config.path}`);
    } catch (error) {
      console.error('❌ 数据库连接失败:', error);
      throw new Error(`数据库连接失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取数据库实例
   */
  public getDatabase(): Database.Database {
    if (!this.db || !this.isInitialized) {
      throw new Error('数据库未初始化，请先调用 initialize() 方法');
    }
    return this.db;
  }

  /**
   * 执行事务
   */
  public transaction<T>(fn: (db: Database.Database) => T): T {
    const db = this.getDatabase();
    const transaction = db.transaction(fn);
    return transaction(db);
  }

  /**
   * 数据库健康检查
   */
  public healthCheck(): { status: 'healthy' | 'unhealthy'; details: Record<string, unknown> } {
    try {
      const db = this.getDatabase();
      
      // 执行简单查询测试连接
      const result = db.prepare('SELECT 1 as test').get();
      
      // 获取数据库统计信息
      const stats = {
        isOpen: db.open,
        inTransaction: db.inTransaction,
        path: this.config.path,
        testQuery: result,
        pragma: {
          journalMode: db.pragma('journal_mode', { simple: true }),
          synchronous: db.pragma('synchronous', { simple: true }),
          foreignKeys: db.pragma('foreign_keys', { simple: true }),
          cacheSize: db.pragma('cache_size', { simple: true })
        }
      };

      return {
        status: 'healthy',
        details: stats
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          error: error instanceof Error ? error.message : String(error),
          isInitialized: this.isInitialized,
          dbExists: this.db !== null
        }
      };
    }
  }

  /**
   * 关闭数据库连接
   */
  public close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
      this.isInitialized = false;
      console.log('✅ 数据库连接已关闭');
    }
  }

  /**
   * 获取数据库信息
   */
  public getInfo(): Record<string, unknown> {
    const db = this.getDatabase();
    return {
      path: this.config.path,
      isOpen: db.open,
      inTransaction: db.inTransaction,
      readonly: this.config.readonly,
      memory: db.memory
    };
  }
}

// 创建全局数据库连接实例
const dbConnection = new DatabaseConnection();

// 便捷函数：获取数据库实例
export function getDatabase(): Database.Database {
  return dbConnection.getDatabase();
}

// 便捷函数：初始化数据库
export function initDatabase(): void {
  dbConnection.initialize();
}

// 便捷函数：关闭数据库连接
export function closeDatabase(): void {
  dbConnection.close();
}

// 导出连接实例和相关方法
export { DatabaseConnection, dbConnection };
export type { DatabaseConfig };