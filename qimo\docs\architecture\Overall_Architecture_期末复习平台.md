# 期末复习平台 - 总体架构蓝图

## 文档信息
- **项目名称**: 期末复习平台 (Final Review Platform)
- **架构版本**: v1.0
- **创建时间**: 2025-01-27
- **架构师**: Bob (系统架构师)
- **基于文档**: PRD_Final-Review-Platform_v1.0.md, 任务规划_期末复习平台_垂直切片.md
- **版权归属**: 随影

## 1. 架构概述

### 1.1 架构愿景
设计一个支持垂直切片渐进式开发的、高性能的、可扩展的Markdown笔记管理和阅读平台架构。该架构必须支持PRD中定义的所有功能需求，并能够按照P0/P1/P2优先级进行分阶段实现。

### 1.2 核心设计原则
- **前后端分离**: 清晰的API边界，支持独立开发和部署
- **垂直切片友好**: 架构层次支持功能的端到端实现
- **数据一致性**: 原子化操作保证数据完整性
- **性能优先**: 满足PRD中定义的性能指标
- **简单可维护**: 避免过度设计，保持架构清晰

### 1.3 技术栈选型

| 层次 | 技术选型 | 选型理由 |
|------|----------|----------|
| **前端框架** | Vue3 + TypeScript | 现代化响应式框架，类型安全 |
| **UI组件库** | Ant Design Vue | 企业级组件库，与Vben Admin兼容 |
| **样式方案** | UnoCSS | 原子化CSS，高性能 |
| **构建工具** | Vite | 快速构建，热更新 |
| **后端框架** | Node.js + Koa | 轻量级，中间件生态丰富 |
| **数据库** | SQLite + better-sqlite3 | 轻量级，同步API，事务支持 |
| **文件存储** | 本地文件系统 | 简单可靠，支持原子操作 |
| **容器化** | Docker + docker-compose | 标准化部署，环境一致性 |
| **反向代理** | Nginx | 高性能，静态资源服务 |
| **进程管理** | PM2 | 自动重启，负载均衡 |

## 2. 整体架构设计

### 2.1 架构层次图

```
┌─────────────────────────────────────────────────────────────┐
│                        用户层                                │
├─────────────────────────────────────────────────────────────┤
│  访客浏览器              │           管理员浏览器              │
│  (学科浏览、笔记阅读)      │     (后台管理、文件上传)            │
└─────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────┐
│                      前端层 (Vue3 SPA)                      │
├─────────────────────────────────────────────────────────────┤
│  访客模式                │              管理模式              │
│  ├─ 学科列表页           │        ├─ Vben Admin框架          │
│  ├─ 文件浏览页           │        ├─ 学科管理页面             │
│  ├─ Markdown阅读页       │        ├─ 文件上传页面             │
│  └─ 响应式布局           │        └─ 系统管理页面             │
└─────────────────────────────────────────────────────────────┘
                                    │
                                    ▼ HTTP/HTTPS
┌─────────────────────────────────────────────────────────────┐
│                    网关层 (Nginx)                           │
├─────────────────────────────────────────────────────────────┤
│  ├─ 反向代理 (API请求转发)                                   │
│  ├─ 静态资源服务 (前端资源、图片)                             │
│  ├─ 负载均衡 (多实例支持)                                    │
│  └─ SSL终止 (HTTPS支持)                                     │
└─────────────────────────────────────────────────────────────┘
                                    │
                                    ▼ HTTP
┌─────────────────────────────────────────────────────────────┐
│                  应用服务层 (Node.js + Koa)                 │
├─────────────────────────────────────────────────────────────┤
│  路由层     │  控制器层    │  服务层      │  数据访问层        │
│  ├─ /api/   │  ├─ 参数验证 │  ├─ 业务逻辑  │  ├─ 数据库操作    │
│  subjects   │  ├─ 错误处理 │  ├─ 事务管理  │  ├─ 文件操作      │
│  ├─ /api/   │  ├─ 响应格式 │  ├─ 权限控制  │  ├─ 缓存管理      │
│  files      │  └─ 日志记录 │  └─ 原子操作  │  └─ 连接池        │
│  ├─ /api/   │              │              │                  │
│  assets     │              │              │                  │
│  └─ /api/   │              │              │                  │
│  upload     │              │              │                  │
└─────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────┐
│                    数据持久层                                │
├─────────────────────────────────────────────────────────────┤
│  数据库存储 (SQLite)      │        文件存储 (File System)    │
│  ├─ subjects表           │        ├─ storage/data/          │
│  │  ├─ id (主键)         │        │  ├─ {subject_id}/       │
│  │  ├─ name (学科名)     │        │  │  ├─ 原始目录结构      │
│  │  ├─ created_at       │        │  │  ├─ *.md文件          │
│  │  └─ updated_at       │        │  │  └─ 图片文件          │
│  └─ file_nodes表        │        │  └─ temp/ (临时目录)     │
│     ├─ id (主键)         │        └─ storage/database/      │
│     ├─ subject_id       │           └─ app.db              │
│     ├─ parent_id        │                                  │
│     ├─ name (文件名)     │                                  │
│     ├─ type (文件/文件夹) │                                  │
│     ├─ path (相对路径)   │                                  │
│     └─ size (文件大小)   │                                  │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心架构特点

1. **双模式前端设计**: 访客浏览模式 + 管理后台模式，共享底层架构
2. **RESTful API设计**: 清晰的资源导向接口，支持标准HTTP方法
3. **分层架构**: 路由→控制器→服务→数据访问，职责分离
4. **原子化操作**: 临时目录+事务+移动，确保数据一致性
5. **容器化部署**: Docker多容器架构，便于扩展和维护

## 3. 数据库设计

### 3.1 数据库架构策略

采用**渐进式数据库设计**策略，支持垂直切片的分阶段实现：

- **P0阶段**: 基础表结构，支持核心CRUD操作
- **P1阶段**: 扩展字段和索引，支持复杂查询和层级关系
- **P2阶段**: 性能优化，添加缓存和统计字段

### 3.2 核心数据表设计

#### 3.2.1 subjects表 (学科表)

```sql
CREATE TABLE subjects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,           -- 学科名称，唯一约束
    description TEXT,                            -- 学科描述 (P1阶段添加)
    file_count INTEGER DEFAULT 0,               -- 文件数量统计 (P2阶段添加)
    total_size INTEGER DEFAULT 0,               -- 总文件大小 (P2阶段添加)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 索引设计
CREATE INDEX idx_subjects_name ON subjects(name);
CREATE INDEX idx_subjects_created_at ON subjects(created_at);
```

#### 3.2.2 file_nodes表 (文件节点表)

```sql
CREATE TABLE file_nodes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    subject_id INTEGER NOT NULL,                -- 所属学科ID
    parent_id INTEGER,                          -- 父节点ID，NULL表示根节点
    name VARCHAR(255) NOT NULL,                 -- 文件/文件夹名称
    type VARCHAR(10) NOT NULL,                  -- 'file' 或 'folder'
    path TEXT NOT NULL,                         -- 相对于学科根目录的路径
    size INTEGER DEFAULT 0,                     -- 文件大小(字节)，文件夹为0
    mime_type VARCHAR(100),                     -- MIME类型 (P1阶段添加)
    md5_hash VARCHAR(32),                       -- 文件MD5哈希 (P1阶段添加)
    sort_order INTEGER DEFAULT 0,              -- 排序权重 (P1阶段添加)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES file_nodes(id) ON DELETE CASCADE
);

-- 索引设计
CREATE INDEX idx_file_nodes_subject_id ON file_nodes(subject_id);
CREATE INDEX idx_file_nodes_parent_id ON file_nodes(parent_id);
CREATE INDEX idx_file_nodes_path ON file_nodes(path);
CREATE INDEX idx_file_nodes_type ON file_nodes(type);
CREATE UNIQUE INDEX idx_file_nodes_unique_path ON file_nodes(subject_id, path);
```

### 3.3 数据库操作策略

#### 3.3.1 事务处理模式
```javascript
// 原子化操作示例
const transaction = db.transaction((subjectId, fileData) => {
    // 1. 创建临时记录
    const tempNodes = insertTempFileNodes(fileData);
    
    // 2. 验证数据完整性
    validateFileStructure(tempNodes);
    
    // 3. 移动文件到正式目录
    moveFilesToFinalLocation(tempNodes);
    
    // 4. 更新正式记录
    updateFileNodes(tempNodes);
    
    // 5. 更新统计信息
    updateSubjectStats(subjectId);
});
```

#### 3.3.2 查询优化策略
- **分页查询**: 使用LIMIT和OFFSET，避免大结果集
- **索引利用**: 基于查询模式设计复合索引
- **连接查询**: 减少N+1查询问题
- **缓存策略**: 热点数据内存缓存

## 4. API接口设计

### 4.1 API设计原则

1. **RESTful风格**: 资源导向，标准HTTP方法
2. **统一响应格式**: 成功/错误响应格式一致
3. **版本控制**: URL路径版本控制 (/api/v1/)
4. **错误处理**: 标准HTTP状态码 + 详细错误信息
5. **参数验证**: 严格的输入验证和类型检查

### 4.2 API接口规范

#### 4.2.1 学科管理接口

```javascript
// 获取所有学科
GET /api/subjects
Response: {
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "数据结构",
            "file_count": 15,
            "total_size": 2048576,
            "created_at": "2025-01-27T10:00:00Z"
        }
    ]
}

// 创建学科
POST /api/subjects
Request: {
    "name": "算法设计"
}
Response: {
    "success": true,
    "data": {
        "id": 2,
        "name": "算法设计",
        "created_at": "2025-01-27T10:05:00Z"
    }
}

// 删除学科
DELETE /api/subjects/:id
Response: {
    "success": true,
    "message": "学科删除成功"
}
```

#### 4.2.2 文件管理接口

```javascript
// 获取学科文件结构
GET /api/subjects/:id/files
Response: {
    "success": true,
    "data": {
        "subject": {
            "id": 1,
            "name": "数据结构"
        },
        "files": [
            {
                "id": 1,
                "name": "第一章",
                "type": "folder",
                "children": [
                    {
                        "id": 2,
                        "name": "链表.md",
                        "type": "file",
                        "size": 1024,
                        "path": "第一章/链表.md"
                    }
                ]
            }
        ]
    }
}

// 获取单个文件内容
GET /api/files/:id
Response: {
    "success": true,
    "data": {
        "id": 2,
        "name": "链表.md",
        "content": "# 链表数据结构\n\n链表是一种...",
        "type": "file",
        "size": 1024
    }
}

// 文件上传
POST /api/subjects/:id/upload
Content-Type: multipart/form-data
Request: FormData with files
Response: {
    "success": true,
    "data": {
        "uploaded_files": 15,
        "total_size": 2048576,
        "processing_time": 1.5
    }
}
```

#### 4.2.3 静态资源接口

```javascript
// 获取图片资源
GET /api/assets/:file_node_id
Response: Binary data with appropriate Content-Type header

// 支持的图片格式: jpg, jpeg, png, gif, svg, webp
// 自动设置缓存头: Cache-Control: public, max-age=31536000
```

### 4.3 错误处理规范

```javascript
// 统一错误响应格式
{
    "success": false,
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "输入参数验证失败",
        "details": {
            "field": "name",
            "reason": "学科名称不能为空"
        }
    }
}

// 标准错误码定义
const ERROR_CODES = {
    VALIDATION_ERROR: 400,      // 参数验证错误
    UNAUTHORIZED: 401,          // 未授权访问
    FORBIDDEN: 403,             // 禁止访问
    NOT_FOUND: 404,             // 资源不存在
    CONFLICT: 409,              // 资源冲突
    PAYLOAD_TOO_LARGE: 413,     // 文件过大
    INTERNAL_ERROR: 500         // 服务器内部错误
};
```

## 5. 前端架构设计

### 5.1 前端架构模式

采用**双模式单页应用(SPA)**架构：

```
src/
├── main.ts                 # 应用入口
├── router/                 # 路由配置
│   ├── index.ts           # 主路由
│   ├── visitor.ts         # 访客路由
│   └── admin.ts           # 管理路由
├── views/                 # 页面组件
│   ├── visitor/           # 访客页面
│   │   ├── SubjectList.vue
│   │   ├── FileList.vue
│   │   └── MarkdownReader.vue
│   └── admin/             # 管理页面
│       ├── Dashboard.vue
│       ├── SubjectManagement.vue
│       └── FileUpload.vue
├── components/            # 通用组件
│   ├── MarkdownRenderer.vue
│   ├── FileTree.vue
│   ├── UploadProgress.vue
│   └── common/
├── composables/           # 组合式函数
│   ├── useApi.ts
│   ├── useFileUpload.ts
│   └── useMarkdown.ts
├── stores/                # 状态管理
│   ├── subject.ts
│   ├── file.ts
│   └── upload.ts
├── utils/                 # 工具函数
│   ├── api.ts
│   ├── file.ts
│   └── markdown.ts
└── styles/                # 样式文件
    ├── main.css
    └── responsive.css
```

### 5.2 核心组件设计

#### 5.2.1 MarkdownRenderer组件
```vue
<template>
  <div class="markdown-renderer">
    <div class="markdown-content" v-html="renderedContent"></div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  content: string;
  subjectId: number;
}

// 功能特性:
// - 支持标准Markdown语法
// - 自动替换图片路径为API路径
// - 代码语法高亮
// - 数学公式渲染(可选)
// - 目录导航生成
// - 响应式图片处理
</script>
```

#### 5.2.2 FileTree组件
```vue
<template>
  <div class="file-tree">
    <TreeNode 
      v-for="node in treeData" 
      :key="node.id"
      :node="node"
      @select="handleSelect"
    />
  </div>
</template>

<script setup lang="ts">
// 功能特性:
// - 树形结构展示
// - 展开/折叠状态管理
// - 文件/文件夹图标区分
// - 懒加载支持
// - 虚拟滚动(大量文件时)
// - 右键菜单支持
</script>
```

### 5.3 状态管理策略

使用Pinia进行状态管理，按功能模块分离：

```typescript
// stores/subject.ts
export const useSubjectStore = defineStore('subject', () => {
  const subjects = ref<Subject[]>([]);
  const currentSubject = ref<Subject | null>(null);
  
  const fetchSubjects = async () => {
    // API调用逻辑
  };
  
  const createSubject = async (name: string) => {
    // 创建学科逻辑
  };
  
  return {
    subjects,
    currentSubject,
    fetchSubjects,
    createSubject
  };
});
```

### 5.4 响应式设计策略

```css
/* 响应式断点定义 */
:root {
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
}

/* 移动端优先的响应式设计 */
.container {
  padding: 1rem;
}

@media (min-width: 768px) {
  .container {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
  }
}

/* 触摸友好的交互设计 */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}
```

## 6. 文件存储架构

### 6.1 存储策略设计

采用**按学科分离的文件系统存储**策略：

```
storage/
├── data/                   # 业务数据存储
│   ├── 1/                 # 学科ID=1的文件
│   │   ├── 第一章/
│   │   │   ├── 链表.md
│   │   │   └── images/
│   │   │       └── list.png
│   │   └── 第二章/
│   │       └── 树.md
│   ├── 2/                 # 学科ID=2的文件
│   └── temp/              # 临时上传目录
│       ├── upload_123456/ # 临时上传会话
│       └── processing/    # 处理中的文件
├── database/              # 数据库文件
│   └── app.db
└── logs/                  # 日志文件
    ├── app.log
    └── error.log
```

### 6.2 原子化上传流程

```javascript
// 原子化上传处理流程
async function atomicUpload(subjectId, files) {
    const tempDir = `storage/temp/upload_${Date.now()}`;
    const finalDir = `storage/data/${subjectId}`;
    
    try {
        // 1. 创建临时目录
        await fs.mkdir(tempDir, { recursive: true });
        
        // 2. 上传文件到临时目录
        const uploadedFiles = await processUploadFiles(files, tempDir);
        
        // 3. 验证文件完整性
        await validateFiles(uploadedFiles);
        
        // 4. 开始数据库事务
        const transaction = db.transaction(() => {
            // 5. 创建文件节点记录
            const fileNodes = createFileNodes(subjectId, uploadedFiles);
            
            // 6. 移动文件到最终位置
            moveFiles(tempDir, finalDir);
            
            // 7. 更新数据库记录
            updateDatabase(fileNodes);
        });
        
        transaction();
        
        // 8. 清理临时目录
        await fs.rm(tempDir, { recursive: true });
        
        return { success: true, files: uploadedFiles };
        
    } catch (error) {
        // 回滚操作：清理临时文件和数据库记录
        await rollbackUpload(tempDir, subjectId);
        throw error;
    }
}
```

### 6.3 文件访问优化

```javascript
// 图片资源访问优化
app.get('/api/assets/:fileNodeId', async (ctx) => {
    const { fileNodeId } = ctx.params;
    
    // 1. 查询文件信息
    const fileNode = await getFileNode(fileNodeId);
    if (!fileNode) {
        ctx.status = 404;
        return;
    }
    
    // 2. 构建文件路径
    const filePath = path.join('storage/data', fileNode.subject_id.toString(), fileNode.path);
    
    // 3. 检查文件存在性
    if (!await fs.access(filePath).then(() => true).catch(() => false)) {
        ctx.status = 404;
        return;
    }
    
    // 4. 设置缓存头
    ctx.set('Cache-Control', 'public, max-age=31536000');
    ctx.set('Content-Type', fileNode.mime_type);
    
    // 5. 流式传输文件
    ctx.body = fs.createReadStream(filePath);
});
```

## 7. 部署架构设计

### 7.1 容器化架构

```yaml
# docker-compose.yml
version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./storage/data:/var/www/storage/data:ro
    depends_on:
      - app
    networks:
      - app-network

  app:
    build: .
    environment:
      - NODE_ENV=production
      - ADMIN_URL_PATH=${ADMIN_URL_PATH}
    volumes:
      - ./storage:/app/storage
    networks:
      - app-network
    restart: unless-stopped

networks:
  app-network:
    driver: bridge

volumes:
  storage-data:
    driver: local
```

### 7.2 Nginx配置策略

```nginx
# nginx.conf
upstream app_backend {
    server app:3000;
}

server {
    listen 80;
    server_name localhost;
    
    # 前端静态资源
    location / {
        root /var/www/html;
        try_files $uri $uri/ /index.html;
    }
    
    # API接口代理
    location /api/ {
        proxy_pass http://app_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # 上传文件大小限制
        client_max_body_size 500M;
    }
    
    # 静态资源缓存
    location ~* \.(jpg|jpeg|png|gif|svg|webp)$ {
        root /var/www/storage/data;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Gzip压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml;
}
```

### 7.3 PM2进程管理

```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'final-review-platform',
    script: './dist/server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './storage/logs/pm2-error.log',
    out_file: './storage/logs/pm2-out.log',
    log_file: './storage/logs/pm2-combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
```

## 8. 性能优化策略

### 8.1 前端性能优化

1. **代码分割**: 按路由和功能模块进行代码分割
2. **懒加载**: 图片和组件的懒加载
3. **缓存策略**: 浏览器缓存和Service Worker
4. **资源压缩**: Gzip压缩和资源最小化
5. **CDN加速**: 静态资源CDN分发

### 8.2 后端性能优化

1. **数据库优化**: 索引优化和查询优化
2. **连接池**: 数据库连接池管理
3. **缓存策略**: 内存缓存热点数据
4. **异步处理**: 文件上传异步处理
5. **负载均衡**: PM2集群模式

### 8.3 存储性能优化

1. **文件分片**: 大文件分片存储
2. **压缩存储**: 文本文件压缩存储
3. **缓存策略**: 文件系统缓存
4. **清理策略**: 定期清理临时文件

## 9. 安全性设计

### 9.1 访问控制

```javascript
// 管理后台访问控制
const ADMIN_URL_PATH = process.env.ADMIN_URL_PATH || '/admin-panel-abcdef';

app.use(async (ctx, next) => {
    if (ctx.path.startsWith('/admin') && !ctx.path.startsWith(ADMIN_URL_PATH)) {
        ctx.status = 404;
        return;
    }
    await next();
});
```

### 9.2 文件安全

```javascript
// 文件类型白名单
const ALLOWED_FILE_TYPES = {
    'text/markdown': ['.md'],
    'image/jpeg': ['.jpg', '.jpeg'],
    'image/png': ['.png'],
    'image/gif': ['.gif'],
    'image/svg+xml': ['.svg'],
    'image/webp': ['.webp']
};

// 文件大小限制
const MAX_FILE_SIZE = 20 * 1024 * 1024; // 20MB
const MAX_UPLOAD_SIZE = 500 * 1024 * 1024; // 500MB

// 路径遍历攻击防护
function sanitizePath(inputPath) {
    return path.normalize(inputPath).replace(/^(\.\.[\/\\])+/, '');
}
```

### 9.3 输入验证

```javascript
// 参数验证中间件
const validateSubjectName = (ctx, next) => {
    const { name } = ctx.request.body;
    
    if (!name || typeof name !== 'string') {
        ctx.status = 400;
        ctx.body = { error: '学科名称不能为空' };
        return;
    }
    
    if (name.length > 100) {
        ctx.status = 400;
        ctx.body = { error: '学科名称不能超过100个字符' };
        return;
    }
    
    return next();
};
```

## 10. 监控和运维

### 10.1 健康检查

```javascript
// 健康检查端点
app.get('/health', async (ctx) => {
    const health = {
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        database: await checkDatabaseHealth(),
        storage: await checkStorageHealth()
    };
    
    ctx.body = health;
});
```

### 10.2 日志管理

```javascript
// 结构化日志
const logger = {
    info: (message, meta = {}) => {
        console.log(JSON.stringify({
            level: 'info',
            message,
            timestamp: new Date().toISOString(),
            ...meta
        }));
    },
    
    error: (message, error, meta = {}) => {
        console.error(JSON.stringify({
            level: 'error',
            message,
            error: error.stack,
            timestamp: new Date().toISOString(),
            ...meta
        }));
    }
};
```

### 10.3 性能监控

```javascript
// 性能指标收集
const performanceMetrics = {
    requestCount: 0,
    responseTime: [],
    errorCount: 0,
    uploadCount: 0,
    uploadSize: 0
};

// 中间件记录性能指标
app.use(async (ctx, next) => {
    const start = Date.now();
    performanceMetrics.requestCount++;
    
    try {
        await next();
    } catch (error) {
        performanceMetrics.errorCount++;
        throw error;
    } finally {
        const duration = Date.now() - start;
        performanceMetrics.responseTime.push(duration);
    }
});
```

## 11. 扩展性考虑

### 11.1 水平扩展支持

1. **无状态设计**: 应用服务无状态，支持多实例部署
2. **数据库分离**: 数据库和应用分离，支持数据库集群
3. **文件存储**: 支持对象存储(如MinIO)替换本地存储
4. **负载均衡**: Nginx负载均衡多个应用实例

### 11.2 功能扩展接口

```javascript
// 插件化架构预留
const pluginManager = {
    plugins: new Map(),
    
    register(name, plugin) {
        this.plugins.set(name, plugin);
    },
    
    execute(hook, ...args) {
        for (const plugin of this.plugins.values()) {
            if (plugin[hook]) {
                plugin[hook](...args);
            }
        }
    }
};

// 扩展点定义
const HOOKS = {
    BEFORE_UPLOAD: 'beforeUpload',
    AFTER_UPLOAD: 'afterUpload',
    BEFORE_RENDER: 'beforeRender',
    AFTER_RENDER: 'afterRender'
};
```

### 11.3 API版本控制

```javascript
// API版本控制策略
const apiVersions = {
    'v1': require('./api/v1'),
    'v2': require('./api/v2')  // 未来版本预留
};

app.use('/api/:version', async (ctx, next) => {
    const { version } = ctx.params;
    
    if (!apiVersions[version]) {
        ctx.status = 404;
        ctx.body = { error: 'API版本不存在' };
        return;
    }
    
    ctx.apiVersion = version;
    await next();
});
```

## 12. 垂直切片支持策略

### 12.1 P0阶段架构支持

**数据库**: 基础表结构，支持核心CRUD
**API**: 基础接口，标准HTTP方法
**前端**: 简单页面，基础路由
**存储**: 基本文件操作，简单上传

### 12.2 P1阶段架构扩展

**数据库**: 扩展字段，优化索引，层级关系
**API**: 高级功能，图片处理，原子操作
**前端**: 复杂组件，树形结构，Markdown渲染
**存储**: 事务处理，错误恢复，性能优化

### 12.3 P2阶段架构优化

**数据库**: 性能调优，统计字段，缓存策略
**API**: 缓存机制，限流控制，监控指标
**前端**: 响应式设计，性能优化，用户体验
**存储**: 高级特性，备份恢复，监控告警

## 13. 技术债务管理

### 13.1 已知技术债务

1. **SQLite限制**: 单文件数据库，不支持真正的并发写入
2. **本地存储**: 不支持分布式，扩展性有限
3. **简单认证**: 基于URL的访问控制，安全性有限
4. **缓存缺失**: 缺少Redis等专业缓存方案

### 13.2 债务偿还计划

**短期(P1阶段)**:
- 添加数据库连接池
- 实现基础缓存机制
- 完善错误处理

**中期(P2阶段)**:
- 考虑PostgreSQL迁移
- 引入Redis缓存
- 实现更安全的认证机制

**长期(后续版本)**:
- 微服务架构重构
- 对象存储集成
- 完整的用户权限系统

## 14. 总结

### 14.1 架构优势

1. **简单可靠**: 技术栈成熟，架构清晰
2. **渐进式**: 支持垂直切片的分阶段实现
3. **高性能**: 满足PRD定义的性能指标
4. **易维护**: 分层架构，职责分离
5. **可扩展**: 预留扩展接口，支持未来增长

### 14.2 关键设计决策

1. **SQLite选择**: 简单可靠，适合中小规模应用
2. **前后端分离**: 清晰的API边界，支持独立开发
3. **原子化操作**: 确保数据一致性，避免脏数据
4. **容器化部署**: 标准化部署，环境一致性
5. **双模式前端**: 访客和管理功能分离，用户体验优化

### 14.3 实施建议

1. **严格按照垂直切片顺序实现**: P0→P1→P2
2. **重视测试**: 每个切片都要有完整的测试覆盖
3. **性能监控**: 从第一个切片开始就要监控性能指标
4. **文档同步**: 架构变更要及时更新文档
5. **代码审查**: 确保实现符合架构设计

---

**文档状态**: ✅ 已完成
**下一步**: 等待老板审核，准备启动具体切片的详细设计
**版权声明**: 本文档版权归属于【随影】

*架构设计完成时间: 2025-01-27*
*架构师: Bob (系统架构师)*