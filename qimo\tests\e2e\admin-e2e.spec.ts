import { test, expect } from '@playwright/test'

/**
 * 管理功能端到端测试
 * 测试完整的管理操作流程和用户体验
 */

const ADMIN_URL = 'http://localhost:5173/admin-panel-abcdef'
const USER_URL = 'http://localhost:5173'

test.describe('管理功能端到端测试', () => {
  test.beforeEach(async ({ page }) => {
    // 确保每个测试开始时都有干净的状态
    await page.goto(ADMIN_URL)
    await page.waitForLoadState('networkidle')
  })

  test.describe('管理后台访问和导航', () => {
    test('应能正常访问管理后台', async ({ page }) => {
      await page.goto(ADMIN_URL)
      await page.waitForLoadState('networkidle')

      // 验证页面标题
      await expect(page).toHaveTitle(/管理后台/)

      // 验证管理后台布局元素
      await expect(page.locator('.admin-layout')).toBeVisible()
      await expect(page.locator('.admin-sider')).toBeVisible()
      await expect(page.locator('.admin-header')).toBeVisible()
      await expect(page.locator('.admin-content')).toBeVisible()

      // 验证侧边栏菜单
      await expect(page.locator('text=仪表盘')).toBeVisible()
      await expect(page.locator('text=学科管理')).toBeVisible()
      await expect(page.locator('text=系统设置')).toBeVisible()
    })

    test('侧边栏折叠功能应正常工作', async ({ page }) => {
      await page.goto(ADMIN_URL)
      await page.waitForLoadState('networkidle')

      // 点击折叠按钮
      await page.click('.trigger')
      await page.waitForTimeout(500) // 等待动画完成

      // 验证侧边栏已折叠
      await expect(page.locator('.admin-sider')).toHaveClass(/ant-layout-sider-collapsed/)

      // 再次点击展开
      await page.click('.trigger')
      await page.waitForTimeout(500)

      // 验证侧边栏已展开
      await expect(page.locator('.admin-sider')).not.toHaveClass(/ant-layout-sider-collapsed/)
    })

    test('菜单导航应正常工作', async ({ page }) => {
      await page.goto(ADMIN_URL)
      await page.waitForLoadState('networkidle')

      // 点击学科管理菜单
      await page.click('text=学科管理')
      await page.waitForLoadState('networkidle')

      // 验证导航到学科管理页面
      await expect(page).toHaveURL(`${ADMIN_URL}/subjects`)
      await expect(page.locator('h1:has-text("学科管理")')).toBeVisible()

      // 点击仪表盘菜单
      await page.click('text=仪表盘')
      await page.waitForLoadState('networkidle')

      // 验证导航到仪表盘页面
      await expect(page).toHaveURL(`${ADMIN_URL}/dashboard`)
    })
  })

  test.describe('学科管理完整流程', () => {
    test('完整的学科CRUD操作流程', async ({ page }) => {
      // 导航到学科管理页面
      await page.goto(`${ADMIN_URL}/subjects`)
      await page.waitForLoadState('networkidle')

      const testSubjectName = `E2E测试学科_${Date.now()}`
      const testSubjectDesc = '这是端到端测试创建的学科'

      // 1. 创建学科
      await page.click('text=新建学科')
      await page.waitForSelector('.ant-modal')

      await page.fill('input[placeholder="请输入学科名称"]', testSubjectName)
      await page.fill('textarea[placeholder="请输入学科描述"]', testSubjectDesc)
      
      // 提交创建
      await page.click('.ant-modal .ant-btn-primary')
      
      // 等待创建完成并验证成功消息
      await page.waitForResponse(response => 
        response.url().includes('/api/admin/subjects') && 
        response.request().method() === 'POST'
      )

      // 验证学科出现在列表中
      await expect(page.locator(`text=${testSubjectName}`)).toBeVisible()

      // 2. 搜索学科
      await page.fill('input[placeholder="搜索学科名称或描述"]', testSubjectName)
      await page.waitForTimeout(500) // 等待防抖搜索

      // 验证搜索结果
      await expect(page.locator(`text=${testSubjectName}`)).toBeVisible()
      
      // 清除搜索
      await page.click('.ant-input-clear-icon')
      await page.waitForTimeout(500)

      // 3. 编辑学科
      const editedName = `${testSubjectName}_已编辑`
      const editedDesc = `${testSubjectDesc}_已编辑`

      // 找到对应行并点击编辑按钮
      const row = page.locator(`tr:has-text("${testSubjectName}")`)
      await row.locator('button:has-text("编辑")').click()
      await page.waitForSelector('.ant-modal')

      // 修改内容
      await page.fill('input[placeholder="请输入学科名称"]', editedName)
      await page.fill('textarea[placeholder="请输入学科描述"]', editedDesc)
      
      // 提交修改
      await page.click('.ant-modal .ant-btn-primary')
      
      // 等待更新完成
      await page.waitForResponse(response => 
        response.url().includes('/api/admin/subjects') && 
        response.request().method() === 'PUT'
      )

      // 验证修改后的内容
      await expect(page.locator(`text=${editedName}`)).toBeVisible()
      await expect(page.locator(`text=${editedDesc}`)).toBeVisible()

      // 4. 状态切换
      const statusSwitch = row.locator('.ant-switch')
      await statusSwitch.click()
      await page.waitForTimeout(500)

      // 验证状态已切换
      await expect(statusSwitch).not.toBeChecked()

      // 5. 删除学科
      await row.locator('button:has-text("删除")').click()
      await page.waitForSelector('.ant-popconfirm')
      
      // 确认删除
      await page.click('.ant-popconfirm .ant-btn-primary')
      
      // 等待删除完成
      await page.waitForResponse(response => 
        response.url().includes('/api/admin/subjects') && 
        response.request().method() === 'DELETE'
      )

      // 验证学科已从列表中移除
      await expect(page.locator(`text=${editedName}`)).not.toBeVisible()
    })

    test('批量操作功能', async ({ page }) => {
      await page.goto(`${ADMIN_URL}/subjects`)
      await page.waitForLoadState('networkidle')

      // 选择多个学科
      const checkboxes = page.locator('tbody .ant-checkbox-input')
      const count = await checkboxes.count()
      
      if (count > 1) {
        // 选择前两个学科
        await checkboxes.nth(0).check()
        await checkboxes.nth(1).check()

        // 验证批量操作按钮已启用
        await expect(page.locator('button:has-text("批量启用")')).not.toBeDisabled()
        await expect(page.locator('button:has-text("批量禁用")')).not.toBeDisabled()

        // 执行批量禁用
        await page.click('button:has-text("批量禁用")')
        await page.waitForTimeout(1000)

        // 验证操作完成
        // 这里可以添加更多验证逻辑
      }
    })
  })

  test.describe('数据同步验证', () => {
    test('管理后台操作应同步到用户界面', async ({ page, context }) => {
      // 在管理后台创建学科
      await page.goto(`${ADMIN_URL}/subjects`)
      await page.waitForLoadState('networkidle')

      const syncTestName = `同步测试_${Date.now()}`
      
      await page.click('text=新建学科')
      await page.waitForSelector('.ant-modal')

      await page.fill('input[placeholder="请输入学科名称"]', syncTestName)
      await page.fill('textarea[placeholder="请输入学科描述"]', '数据同步测试学科')
      
      await page.click('.ant-modal .ant-btn-primary')
      
      // 等待创建完成
      await page.waitForResponse(response => 
        response.url().includes('/api/admin/subjects') && 
        response.request().method() === 'POST'
      )

      // 打开新标签页访问用户界面
      const userPage = await context.newPage()
      await userPage.goto(USER_URL)
      await userPage.waitForLoadState('networkidle')

      // 导航到学科列表
      await userPage.click('text=开始浏览学科')
      await userPage.waitForLoadState('networkidle')

      // 验证新创建的学科在用户界面可见
      await expect(userPage.locator(`text=${syncTestName}`)).toBeVisible()

      // 清理：删除测试学科
      await page.bringToFront()
      const row = page.locator(`tr:has-text("${syncTestName}")`)
      await row.locator('button:has-text("删除")').click()
      await page.waitForSelector('.ant-popconfirm')
      await page.click('.ant-popconfirm .ant-btn-primary')
      
      await userPage.close()
    })
  })

  test.describe('响应式设计测试', () => {
    test('移动端布局应正常显示', async ({ page }) => {
      // 设置移动端视口
      await page.setViewportSize({ width: 375, height: 667 })
      
      await page.goto(ADMIN_URL)
      await page.waitForLoadState('networkidle')

      // 验证移动端布局
      await expect(page.locator('.admin-layout')).toBeVisible()
      
      // 在移动端，侧边栏应该默认折叠
      await expect(page.locator('.admin-sider')).toHaveClass(/ant-layout-sider-collapsed/)

      // 导航到学科管理
      await page.goto(`${ADMIN_URL}/subjects`)
      await page.waitForLoadState('networkidle')

      // 验证表格在移动端的响应式表现
      await expect(page.locator('.ant-table-wrapper')).toBeVisible()
    })

    test('平板端布局应正常显示', async ({ page }) => {
      // 设置平板端视口
      await page.setViewportSize({ width: 768, height: 1024 })
      
      await page.goto(`${ADMIN_URL}/subjects`)
      await page.waitForLoadState('networkidle')

      // 验证平板端布局
      await expect(page.locator('.admin-layout')).toBeVisible()
      await expect(page.locator('.admin-sider')).toBeVisible()
      await expect(page.locator('.ant-table-wrapper')).toBeVisible()
    })
  })

  test.describe('性能测试', () => {
    test('页面加载时间应符合要求', async ({ page }) => {
      const startTime = Date.now()
      
      await page.goto(`${ADMIN_URL}/subjects`)
      await page.waitForLoadState('networkidle')
      
      const loadTime = Date.now() - startTime
      
      // 页面加载时间应小于3秒
      expect(loadTime).toBeLessThan(3000)
    })

    test('操作响应时间应符合要求', async ({ page }) => {
      await page.goto(`${ADMIN_URL}/subjects`)
      await page.waitForLoadState('networkidle')

      // 测试搜索响应时间
      const searchStartTime = Date.now()
      
      await page.fill('input[placeholder="搜索学科名称或描述"]', '数学')
      await page.waitForTimeout(500) // 等待防抖搜索完成
      
      const searchTime = Date.now() - searchStartTime
      
      // 搜索响应时间应小于1秒
      expect(searchTime).toBeLessThan(1000)
    })
  })

  test.describe('错误处理测试', () => {
    test('网络错误应有适当的用户提示', async ({ page }) => {
      await page.goto(`${ADMIN_URL}/subjects`)
      await page.waitForLoadState('networkidle')

      // 模拟网络中断
      await page.route('**/api/admin/subjects', route => {
        route.abort('failed')
      })

      // 尝试刷新数据
      await page.click('button:has-text("刷新")')
      await page.waitForTimeout(2000)

      // 验证错误提示
      // 这里可以根据实际的错误处理机制添加验证
    })

    test('表单验证应正常工作', async ({ page }) => {
      await page.goto(`${ADMIN_URL}/subjects`)
      await page.waitForLoadState('networkidle')

      // 打开创建表单
      await page.click('text=新建学科')
      await page.waitForSelector('.ant-modal')

      // 不填写必填字段直接提交
      await page.click('.ant-modal .ant-btn-primary')

      // 验证表单验证提示
      await expect(page.locator('.ant-form-item-explain-error')).toBeVisible()
    })
  })
})
