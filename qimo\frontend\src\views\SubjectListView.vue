<template>
  <div class="subject-list-view">
    <div class="page-header">
      <h1>学科列表</h1>
      <p>选择一个学科开始浏览复习资料</p>
    </div>
    
    <div class="content-container">
      <!-- 加载状态 -->
      <div v-if="subjectStore.isLoading" class="loading-container">
        <a-spin size="large" />
        <p>正在加载学科列表...</p>
      </div>
      
      <!-- 错误状态 -->
      <a-alert
        v-else-if="subjectStore.hasError"
        type="error"
        :message="subjectStore.error"
        show-icon
        closable
        @close="subjectStore.clearError"
      />
      
      <!-- 学科列表 -->
      <div v-else-if="subjectStore.hasSubjects" class="subjects-grid">
        <div
          v-for="subject in subjectStore.subjects"
          :key="subject.id"
          class="subject-card"
          @click="goToSubject(subject.id)"
        >
          <div class="subject-icon">
            {{ getSubjectIcon(subject.name) }}
          </div>
          <h3 class="subject-name">{{ subject.name }}</h3>
          <p class="subject-description">{{ subject.description }}</p>
          <div v-if="subject.file_count !== undefined" class="subject-stats">
            <span class="stat-item">
              <FileOutlined />
              {{ subject.file_count }} 个文件
            </span>
            <span v-if="subject.folder_count" class="stat-item">
              <FolderOutlined />
              {{ subject.folder_count }} 个文件夹
            </span>
          </div>
          <div class="subject-meta">
            <span class="update-time">
              更新于 {{ formatDate(subject.updated_at) }}
            </span>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else class="empty-state">
        <a-empty description="暂无学科数据">
          <a-button type="primary" @click="refreshSubjects">
            刷新列表
          </a-button>
        </a-empty>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { FileOutlined, FolderOutlined } from '@ant-design/icons-vue'
import { useSubjectStore } from '@/stores/subject'
import { apiUtils } from '@/utils/api'

const router = useRouter()
const subjectStore = useSubjectStore()

// 获取学科图标
const getSubjectIcon = (name: string): string => {
  const iconMap: Record<string, string> = {
    '数学': '📐',
    '计算机科学': '💻',
    '英语': '🇬🇧',
    '物理': '⚛️',
    '化学': '🧪',
    '生物': '🧬',
    '历史': '📜',
    '地理': '🌍',
    '政治': '🏛️',
    '语文': '📖'
  }
  
  return iconMap[name] || '📚'
}

// 格式化日期
const formatDate = (dateString: string): string => {
  return apiUtils.formatDate(dateString)
}

// 跳转到学科详情
const goToSubject = (id: number) => {
  router.push(`/subjects/${id}`)
}

// 刷新学科列表
const refreshSubjects = async () => {
  try {
    await subjectStore.fetchSubjects(true)
  } catch (error) {
    console.error('刷新学科列表失败:', error)
  }
}

// 组件挂载时获取数据
onMounted(() => {
  if (!subjectStore.hasSubjects) {
    refreshSubjects()
  }
})
</script>

<style scoped>
.subject-list-view {
  min-height: 100vh;
  background: #f8fafc;
}

.page-header {
  background: white;
  padding: 2rem;
  text-align: center;
  border-bottom: 1px solid #e2e8f0;
}

.page-header h1 {
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #2d3748;
}

.page-header p {
  color: #718096;
  font-size: 1.1rem;
}

.content-container {
  max-width: 95%;
  margin: 0 auto;
  padding: 2rem;
  width: 100%;
}

@media (min-width: 768px) {
  .content-container {
    max-width: 90%;
    padding: 2rem 3rem;
  }
}

@media (min-width: 1200px) {
  .content-container {
    max-width: 85%;
    padding: 2rem 3rem;
  }
}

@media (min-width: 1600px) {
  .content-container {
    max-width: 80%;
    padding: 2rem 4rem;
  }
}

.loading-container {
  text-align: center;
  padding: 4rem;
}

.loading-container p {
  margin-top: 1rem;
  color: #718096;
}

.subjects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
}

.subject-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
}

.subject-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #3182ce;
}

.subject-icon {
  font-size: 3rem;
  text-align: center;
  margin-bottom: 1rem;
}

.subject-name {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #2d3748;
  text-align: center;
}

.subject-description {
  color: #718096;
  line-height: 1.5;
  margin-bottom: 1rem;
  text-align: center;
}

.subject-stats {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #4a5568;
  font-size: 0.9rem;
}

.subject-meta {
  text-align: center;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.update-time {
  color: #a0aec0;
  font-size: 0.8rem;
}

.empty-state {
  text-align: center;
  padding: 4rem;
}

@media (max-width: 768px) {
  .subjects-grid {
    grid-template-columns: 1fr;
  }
  
  .content-container {
    padding: 1rem;
  }
}
</style>
