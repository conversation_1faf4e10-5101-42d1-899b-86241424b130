# API接口参考文档

## 概述

期末复习平台API基于RESTful设计原则，提供学科管理、文件管理等核心功能。API分为普通用户接口和管理员专用接口两类。

## 最新更新记录

### 2025-07-27 - 管理员API修复
- **修复** PUT `/admin/subjects/:id` 响应处理：纠正ResponseHelper.success方法调用
- **优化** 管理员学科查询：支持查询所有状态的学科（包括禁用的）
- **改进** 错误处理机制：统一API错误响应格式
- **验证** 所有管理员API端点：确保状态切换和数据同步正常工作

### 2025-07-27 - 管理员API完善
- 完成管理员学科管理的全套CRUD操作
- 新增管理员专用的数据查询权限控制
- 优化API响应性能和错误处理
- 建立管理员操作的完整API文档

## 统一响应格式

### 成功响应
```json
{
  "success": true,
  "data": {...},
  "timestamp": "2025-01-27T00:00:00.000Z",
  "requestId": "req_1234567890_abc123"
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {...}
  },
  "timestamp": "2025-01-27T00:00:00.000Z",
  "requestId": "req_1234567890_abc123"
}
```

### 分页响应
```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 100,
      "totalPages": 5,
      "hasNext": true,
      "hasPrev": false
    }
  },
  "timestamp": "2025-01-27T00:00:00.000Z",
  "requestId": "req_1234567890_abc123"
}
```

## 错误代码

- `INTERNAL_ERROR` - 服务器内部错误
- `INVALID_REQUEST` - 请求参数错误
- `VALIDATION_ERROR` - 数据验证失败
- `UNAUTHORIZED` - 未授权访问
- `FORBIDDEN` - 禁止访问
- `NOT_FOUND` - 资源不存在
- `RESOURCE_EXISTS` - 资源已存在
- `DATABASE_ERROR` - 数据库错误
- `CONSTRAINT_VIOLATION` - 约束违反
- `BUSINESS_ERROR` - 业务逻辑错误

## HTTP状态码映射

- 200: 成功
- 400: 客户端错误
- 401: 未授权
- 403: 禁止访问
- 404: 资源不存在
- 409: 资源冲突
- 422: 业务逻辑错误
- 429: 请求过于频繁
- 500: 服务器内部错误
- 502: 外部服务错误

## 学科管理API

### GET /api/subjects - 获取学科列表

**接口功能描述**: 获取所有启用状态的学科列表，按排序顺序返回

**URL路径**: `/api/subjects`
**请求方法**: GET
**请求参数**: 无

**成功响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 4,
      "name": "数学",
      "description": "高等数学、线性代数、概率论等数学相关课程",
      "created_at": "2025-07-27 07:50:41",
      "updated_at": "2025-07-27 10:36:56",
      "status": 1,
      "sort_order": 1
    }
  ],
  "timestamp": "2025-07-27T10:37:35.581Z",
  "requestId": "req_1753612655579_qx2e0j5ec"
}
```

### GET /api/subjects/:id - 获取学科详情

**接口功能描述**: 根据学科ID获取单个学科的详细信息

**URL路径**: `/api/subjects/:id`
**请求方法**: GET
**请求参数**:
- `id` (路径参数): 学科ID，必须是正整数

**成功响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 4,
    "name": "数学",
    "description": "高等数学、线性代数、概率论等数学相关课程",
    "created_at": "2025-07-27 07:50:41",
    "updated_at": "2025-07-27 10:36:56",
    "status": 1,
    "sort_order": 1
  },
  "timestamp": "2025-07-27T10:37:52.723Z",
  "requestId": "req_1753612672722_4csm2yrnv"
}
```

### POST /api/subjects - 创建学科

**接口功能描述**: 创建新的学科记录

**URL路径**: `/api/subjects`
**请求方法**: POST
**请求参数**:
```json
{
  "name": "学科名称（必填，字符串）",
  "description": "学科描述（可选，字符串）"
}
```

**成功响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 8,
    "name": "通过普通API创建的学科",
    "description": "测试激活的管理功能",
    "created_at": "2025-07-27 10:37:26",
    "updated_at": "2025-07-27 10:37:26",
    "status": 1,
    "sort_order": 0
  },
  "message": "学科创建成功",
  "timestamp": "2025-07-27T10:37:26.012Z",
  "requestId": "req_1753612646987_i160i3hb1"
}
```

### PUT /api/subjects/:id - 更新学科

**接口功能描述**: 更新指定学科的信息

**URL路径**: `/api/subjects/:id`
**请求方法**: PUT
**请求参数**:
- `id` (路径参数): 学科ID，必须是正整数
- 请求体:
```json
{
  "name": "更新后的学科名称（可选，字符串）",
  "description": "更新后的学科描述（可选，字符串）",
  "status": "学科状态（可选，0或1）",
  "sort_order": "排序顺序（可选，整数）"
}
```

### DELETE /api/subjects/:id - 删除学科

**接口功能描述**: 软删除指定的学科（标记为删除状态）

**URL路径**: `/api/subjects/:id`
**请求方法**: DELETE
**请求参数**:
- `id` (路径参数): 学科ID，必须是正整数

## 管理员专用API

管理员API支持完整的CRUD操作，并与前端管理界面实现无缝集成。所有管理员API都需要在请求头中包含 `x-admin-path: /admin-panel-abcdef` 进行权限验证。

### 前后端集成特性

1. **状态同步**: 管理操作完成后自动同步到普通用户界面
2. **错误处理**: 支持重试机制和用户友好的错误提示
3. **性能优化**: 实现防抖搜索和数据缓存
4. **实时更新**: 管理界面与用户界面数据实时同步

### GET /api/admin/subjects - 获取学科列表（管理员）

**接口功能描述**: 获取所有学科列表（包含统计信息），管理员专用

**URL路径**: `/api/admin/subjects`
**请求方法**: GET
**请求参数**:
- 请求头: `x-admin-path: /admin-panel-abcdef` (管理员权限验证)

**成功响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 4,
      "name": "数学",
      "description": "高等数学、线性代数、概率论等数学相关课程",
      "created_at": "2025-07-27 07:50:41",
      "updated_at": "2025-07-27 10:36:56",
      "status": 1,
      "sort_order": 1,
      "file_count": 5,
      "total_size": 13312,
      "fileCount": 5,
      "folderCount": 2,
      "totalSize": 13312
    }
  ],
  "timestamp": "2025-07-27T10:37:35.581Z",
  "requestId": "req_1753612655579_qx2e0j5ec"
}
```

### POST /api/admin/subjects - 创建学科（管理员）

**接口功能描述**: 创建新学科，管理员专用，支持完整参数设置

**URL路径**: `/api/admin/subjects`
**请求方法**: POST
**请求参数**:
- 请求头: `x-admin-path: /admin-panel-abcdef` (管理员权限验证)
- 请求体:
```json
{
  "name": "学科名称（必填，字符串）",
  "description": "学科描述（可选，字符串）",
  "status": "学科状态（可选，0或1，默认1）",
  "sort_order": "排序顺序（可选，整数，默认0）"
}
```

### PUT /api/admin/subjects/:id - 更新学科（管理员）

**接口功能描述**: 更新学科信息，管理员专用，支持完整参数更新

**URL路径**: `/api/admin/subjects/:id`
**请求方法**: PUT
**请求参数**:
- `id` (路径参数): 学科ID，必须是正整数
- 请求头: `x-admin-path: /admin-panel-abcdef` (管理员权限验证)

### DELETE /api/admin/subjects/:id - 删除学科（管理员）

**接口功能描述**: 删除学科，管理员专用，支持级联删除选项

**URL路径**: `/api/admin/subjects/:id`
**请求方法**: DELETE
**请求参数**:
- `id` (路径参数): 学科ID，必须是正整数
- `cascade` (查询参数，可选): 是否级联删除，值为"true"时执行硬删除
- 请求头: `x-admin-path: /admin-panel-abcdef` (管理员权限验证)

### GET /api/admin/subjects/:id/stats - 获取学科统计信息

**接口功能描述**: 获取指定学科的详细统计信息，管理员专用

**URL路径**: `/api/admin/subjects/:id/stats`
**请求方法**: GET
**请求参数**:
- `id` (路径参数): 学科ID，必须是正整数
- 请求头: `x-admin-path: /admin-panel-abcdef` (管理员权限验证)

**成功响应示例**:
```json
{
  "success": true,
  "data": {
    "fileCount": 5,
    "folderCount": 2,
    "totalSize": 13312
  },
  "timestamp": "2025-07-27T10:38:03.371Z",
  "requestId": "req_1753612683369_4wvipv46t"
}
```

## API最佳实践

### 管理员权限验证
所有管理员API都需要在请求头中包含管理员路径验证：

```http
x-admin-path: /admin-panel-abcdef
```

### 状态切换API调用
管理员状态切换的正确调用方式：

```javascript
// ✅ 正确的状态切换调用
const updateSubjectStatus = async (id, status) => {
  try {
    const response = await fetch(`/api/admin/subjects/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'x-admin-path': '/admin-panel-abcdef'
      },
      body: JSON.stringify({ status: status ? 1 : 0 })
    });

    const result = await response.json();
    if (result.success) {
      console.log('状态更新成功:', result.data);
    }
  } catch (error) {
    console.error('状态更新失败:', error);
  }
};
```

### 错误处理模式
API调用的标准错误处理：

```javascript
// ✅ 推荐的错误处理方式
try {
  const response = await apiCall();
  if (response.success) {
    // 处理成功响应
    return response.data;
  } else {
    // 处理业务错误
    throw new Error(response.error.message);
  }
} catch (error) {
  // 处理网络错误或其他异常
  console.error('API调用失败:', error.message);
  throw error;
}
```

### 数据同步机制
管理员操作后的数据同步：

```javascript
// ✅ 管理员操作后同步数据到用户store
const handleAdminUpdate = async (id, updates) => {
  // 1. 执行管理员API调用
  const result = await updateSubject(id, updates);

  // 2. 清除用户端缓存
  subjectStore.clearCache();

  // 3. 同步数据到用户store
  adminStore.syncToUserStore(result.data);

  // 4. 显示成功提示
  message.success('操作成功');
};
```

---

**文档版本**: v1.1
**创建时间**: 2025年07月27日
**最后更新**: 2025年07月27日
**作者**: Alex (Engineer)
**版权**: 随影
