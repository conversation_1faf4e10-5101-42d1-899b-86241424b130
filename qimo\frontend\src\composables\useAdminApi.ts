/**
 * 管理API组合函数
 * 基于Vue 3 Composition API模式，提供管理后台专用的API调用逻辑
 * 
 * <AUTHOR> (Engineer)
 * @date 2025-07-27
 */

import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { adminApi, apiUtils } from '@/utils/api'
import { useAdminStore } from '@/stores/admin'
import { debounce } from 'lodash-es'
import type { Subject } from '@/types/api'

// 学科管理组合函数
export const useSubjectManagement = () => {
  // 获取管理员store实例
  const adminStore = useAdminStore()

  // 响应式状态
  const loading = ref(false)
  const subjects = ref<Subject[]>([])
  const currentSubject = ref<Subject | null>(null)

  // 分页状态
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) =>
      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
  })

  // 搜索状态
  const searchState = reactive({
    keyword: '',
    status: undefined as number | undefined,
    sortField: 'sort_order',
    sortOrder: 'asc' as 'asc' | 'desc'
  })

  // 计算属性
  const filteredSubjects = computed(() => {
    let result = [...subjects.value]

    // 关键词搜索
    if (searchState.keyword) {
      const keyword = searchState.keyword.toLowerCase()
      result = result.filter(subject =>
        subject.name.toLowerCase().includes(keyword) ||
        subject.description?.toLowerCase().includes(keyword)
      )
    }

    // 状态筛选
    if (searchState.status !== undefined) {
      result = result.filter(subject => subject.status === searchState.status)
    }

    // 排序
    result.sort((a, b) => {
      const field = searchState.sortField as keyof Subject
      const aValue = a[field]
      const bValue = b[field]

      if (aValue === bValue) return 0

      const comparison = aValue > bValue ? 1 : -1
      return searchState.sortOrder === 'asc' ? comparison : -comparison
    })

    return result
  })

  // 分页数据
  const paginatedSubjects = computed(() => {
    const start = (pagination.current - 1) * pagination.pageSize
    const end = start + pagination.pageSize
    return filteredSubjects.value.slice(start, end)
  })

  // 防抖搜索函数
  const debouncedSearch = debounce(() => {
    // 重置分页到第一页
    pagination.current = 1
    // 触发重新计算过滤结果
    pagination.total = filteredSubjects.value.length
  }, 300)

  // 监听搜索状态变化，触发防抖搜索
  watch(
    () => [searchState.keyword, searchState.status],
    () => {
      debouncedSearch()
    },
    { deep: true }
  )

  // 获取学科列表（集成store）
  const fetchSubjects = async (forceRefresh = false) => {
    try {
      loading.value = true

      // 使用store获取数据，支持缓存
      const data = await adminStore.fetchAdminSubjects(forceRefresh)
      subjects.value = data || []
      pagination.total = subjects.value.length

      // 只在强制刷新时显示成功消息，避免频繁提示
      if (forceRefresh) {
        message.success('学科列表刷新成功')
      }
    } catch (error) {
      const errorMessage = apiUtils.handleError(error)
      message.error(`加载学科列表失败: ${errorMessage}`)
      console.error('Failed to fetch subjects:', error)
    } finally {
      loading.value = false
    }
  }

  // 获取学科详情
  const fetchSubjectById = async (id: number) => {
    try {
      loading.value = true
      const response = await adminApi.getSubjectById(id)
      currentSubject.value = response.data.data
      return response.data.data
    } catch (error) {
      const errorMessage = apiUtils.handleError(error)
      message.error(`加载学科详情失败: ${errorMessage}`)
      console.error('Failed to fetch subject:', error)
      return null
    } finally {
      loading.value = false
    }
  }

  // 创建学科（集成store和数据同步）
  const createSubject = async (data: {
    name: string
    description?: string
    status?: number
    sort_order?: number
  }) => {
    try {
      loading.value = true

      // 使用store创建，自动处理数据同步
      const newSubject = await adminStore.createSubject(data)

      // 更新本地状态
      subjects.value.unshift(newSubject)
      pagination.total = subjects.value.length

      message.success('学科创建成功')
      return newSubject
    } catch (error) {
      const errorMessage = apiUtils.handleError(error)
      message.error(`创建学科失败: ${errorMessage}`)
      console.error('Failed to create subject:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新学科（集成store和数据同步）
  const updateSubject = async (id: number, data: {
    name?: string
    description?: string
    status?: number
    sort_order?: number
  }) => {
    try {
      loading.value = true

      // 使用store更新，自动处理数据同步
      const updatedSubject = await adminStore.updateSubject(id, data)

      // 更新本地数据
      const index = subjects.value.findIndex(s => s.id === id)
      if (index !== -1) {
        subjects.value[index] = updatedSubject
      }

      if (currentSubject.value?.id === id) {
        currentSubject.value = updatedSubject
      }

      message.success('学科更新成功')
      return updatedSubject
    } catch (error) {
      const errorMessage = apiUtils.handleError(error)
      message.error(`更新学科失败: ${errorMessage}`)
      console.error('Failed to update subject:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除学科（集成store和数据同步）
  const deleteSubject = async (id: number, cascade = false) => {
    try {
      loading.value = true

      // 使用store删除，自动处理数据同步
      await adminStore.deleteSubject(id, cascade)

      // 从本地数据中移除
      subjects.value = subjects.value.filter(s => s.id !== id)
      pagination.total = subjects.value.length

      if (currentSubject.value?.id === id) {
        currentSubject.value = null
      }

      message.success(cascade ? '学科及相关文件删除成功' : '学科删除成功')
      return true
    } catch (error) {
      const errorMessage = apiUtils.handleError(error)
      message.error(`删除学科失败: ${errorMessage}`)
      console.error('Failed to delete subject:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取学科统计信息
  const fetchSubjectStats = async (id: number) => {
    try {
      const response = await adminApi.getSubjectStats(id)
      return response.data.data
    } catch (error) {
      const errorMessage = apiUtils.handleError(error)
      message.error(`获取统计信息失败: ${errorMessage}`)
      console.error('Failed to fetch subject stats:', error)
      return null
    }
  }

  // 批量操作
  const batchUpdateStatus = async (ids: number[], status: number) => {
    try {
      loading.value = true
      const promises = ids.map(id => updateSubject(id, { status }))
      await Promise.all(promises)
      message.success(`批量${status === 1 ? '启用' : '禁用'}成功`)
    } catch (error) {
      const errorMessage = apiUtils.handleError(error)
      message.error(`批量操作失败: ${errorMessage}`)
      console.error('Failed to batch update status:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 搜索和筛选
  const search = (keyword: string) => {
    searchState.keyword = keyword
    pagination.current = 1
  }

  const filterByStatus = (status: number | undefined) => {
    searchState.status = status
    pagination.current = 1
  }

  const sortBy = (field: string, order: 'asc' | 'desc') => {
    searchState.sortField = field
    searchState.sortOrder = order
    pagination.current = 1
  }

  // 重置搜索
  const resetSearch = () => {
    searchState.keyword = ''
    searchState.status = undefined
    searchState.sortField = 'sort_order'
    searchState.sortOrder = 'asc'
    pagination.current = 1
  }

  // 刷新数据
  const refresh = () => {
    fetchSubjects()
  }

  return {
    // 状态
    loading,
    subjects,
    currentSubject,
    pagination,
    searchState,

    // 计算属性
    filteredSubjects,
    paginatedSubjects,

    // 方法
    fetchSubjects,
    fetchSubjectById,
    createSubject,
    updateSubject,
    deleteSubject,
    fetchSubjectStats,
    batchUpdateStatus,
    search,
    filterByStatus,
    sortBy,
    resetSearch,
    refresh
  }
}

// 通用加载状态管理
export const useLoading = (initialState = false) => {
  const loading = ref(initialState)

  const setLoading = (state: boolean) => {
    loading.value = state
  }

  const withLoading = async <T>(fn: () => Promise<T>): Promise<T> => {
    try {
      loading.value = true
      return await fn()
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    setLoading,
    withLoading
  }
}

// 表单验证组合函数
export const useFormValidation = () => {
  const validateSubjectName = (name: string): string | undefined => {
    if (!name || name.trim().length === 0) {
      return '学科名称不能为空'
    }
    if (name.length > 50) {
      return '学科名称不能超过50个字符'
    }
    return undefined
  }

  const validateDescription = (description: string): string | undefined => {
    if (description && description.length > 200) {
      return '学科描述不能超过200个字符'
    }
    return undefined
  }

  const validateSortOrder = (sortOrder: number): string | undefined => {
    if (sortOrder < 0) {
      return '排序顺序不能为负数'
    }
    if (sortOrder > 9999) {
      return '排序顺序不能超过9999'
    }
    return undefined
  }

  return {
    validateSubjectName,
    validateDescription,
    validateSortOrder
  }
}

// 批量操作组合函数
export const useBatchOperations = () => {
  const adminStore = useAdminStore()
  const loading = ref(false)

  // 批量更新状态
  const batchUpdateStatus = async (ids: number[], status: 0 | 1) => {
    try {
      loading.value = true
      await adminStore.batchUpdateStatus(ids, status)

      const statusText = status === 1 ? '启用' : '禁用'
      message.success(`批量${statusText}成功，共处理 ${ids.length} 个学科`)
      return true
    } catch (error) {
      const errorMessage = apiUtils.handleError(error)
      message.error(`批量操作失败: ${errorMessage}`)
      console.error('Failed to batch update status:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 批量删除
  const batchDelete = async (ids: number[], cascade = false) => {
    try {
      loading.value = true

      // 并发删除所有学科
      const promises = ids.map(id => adminStore.deleteSubject(id, cascade))
      await Promise.all(promises)

      message.success(`批量删除成功，共删除 ${ids.length} 个学科`)
      return true
    } catch (error) {
      const errorMessage = apiUtils.handleError(error)
      message.error(`批量删除失败: ${errorMessage}`)
      console.error('Failed to batch delete:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    batchUpdateStatus,
    batchDelete
  }
}
