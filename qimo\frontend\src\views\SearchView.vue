<template>
  <div class="search-view">
    <div class="page-header">
      <h1>搜索文件</h1>
      <p>在所有学科中搜索文件和内容</p>
    </div>
    
    <div class="content-container">
      <div class="search-form">
        <a-input-search
          v-model:value="searchQuery"
          placeholder="输入关键词搜索文件名或内容..."
          size="large"
          @search="onSearch"
          :loading="fileStore.isLoading"
        />
        
        <div class="search-filters">
          <a-select
            v-model:value="selectedSubject"
            placeholder="选择学科"
            style="width: 200px"
            allow-clear
          >
            <a-select-option
              v-for="subject in subjectStore.subjects"
              :key="subject.id"
              :value="subject.id"
            >
              {{ subject.name }}
            </a-select-option>
          </a-select>
          
          <a-select
            v-model:value="selectedType"
            placeholder="文件类型"
            style="width: 120px"
            allow-clear
          >
            <a-select-option value="file">文件</a-select-option>
            <a-select-option value="folder">文件夹</a-select-option>
          </a-select>
        </div>
      </div>
      
      <!-- 搜索结果 -->
      <div v-if="fileStore.hasSearchResults" class="search-results">
        <h2>搜索结果 ({{ fileStore.searchResults.length }} 项)</h2>
        <div class="results-list">
          <div
            v-for="result in fileStore.searchResults"
            :key="result.id"
            class="result-item"
            @click="openFile(result)"
          >
            <div class="result-icon">
              <FolderOutlined v-if="result.type === 'folder'" />
              <FileOutlined v-else />
            </div>
            <div class="result-content">
              <h3 class="result-name">{{ result.name }}</h3>
              <p class="result-path">{{ result.path }}</p>
              <div class="result-meta">
                <span class="meta-item">{{ result.subject_name }}</span>
                <span class="meta-item">{{ formatFileSize(result.size) }}</span>
                <span class="meta-item">{{ formatDate(result.updated_at) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else-if="searchQuery && !fileStore.isLoading" class="empty-state">
        <a-empty description="未找到相关文件">
          <a-button type="primary" @click="clearSearch">
            清空搜索
          </a-button>
        </a-empty>
      </div>
      
      <!-- 初始状态 -->
      <div v-else-if="!searchQuery" class="initial-state">
        <div class="search-tips">
          <h3>搜索提示</h3>
          <ul>
            <li>输入文件名或内容关键词进行搜索</li>
            <li>可以按学科筛选搜索范围</li>
            <li>支持搜索文件和文件夹</li>
            <li>点击搜索结果可直接查看文件内容</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { FolderOutlined, FileOutlined } from '@ant-design/icons-vue'
import { useFileStore } from '@/stores/file'
import { useSubjectStore } from '@/stores/subject'
import { apiUtils } from '@/utils/api'
import type { SearchResult } from '@/types/api'

const router = useRouter()
const fileStore = useFileStore()
const subjectStore = useSubjectStore()

const searchQuery = ref('')
const selectedSubject = ref<number | undefined>()
const selectedType = ref<'file' | 'folder' | undefined>()

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  return apiUtils.formatFileSize(bytes)
}

// 格式化日期
const formatDate = (dateString: string): string => {
  return apiUtils.formatDate(dateString)
}

// 执行搜索
const onSearch = async () => {
  if (!searchQuery.value.trim()) return
  
  try {
    await fileStore.searchFiles(
      searchQuery.value.trim(),
      selectedSubject.value,
      selectedType.value
    )
  } catch (error) {
    console.error('搜索失败:', error)
  }
}

// 打开文件
const openFile = (result: SearchResult) => {
  if (result.type === 'file') {
    router.push(`/files/${result.id}`)
  } else {
    router.push(`/subjects/${result.subject_id}`)
  }
}

// 清空搜索
const clearSearch = () => {
  searchQuery.value = ''
  selectedSubject.value = undefined
  selectedType.value = undefined
  fileStore.clearSearchResults()
}

// 加载学科列表
onMounted(async () => {
  if (!subjectStore.hasSubjects) {
    try {
      await subjectStore.fetchSubjects()
    } catch (error) {
      console.error('加载学科列表失败:', error)
    }
  }
})
</script>

<style scoped>
.search-view {
  min-height: 100vh;
  background: #f8fafc;
}

.page-header {
  background: white;
  padding: 2rem;
  text-align: center;
  border-bottom: 1px solid #e2e8f0;
}

.page-header h1 {
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #2d3748;
}

.page-header p {
  color: #718096;
  font-size: 1.1rem;
}

.content-container {
  max-width: 95%;
  margin: 0 auto;
  padding: 2rem;
  width: 100%;
}

@media (min-width: 768px) {
  .content-container {
    max-width: 90%;
    padding: 2rem 3rem;
  }
}

@media (min-width: 1200px) {
  .content-container {
    max-width: 85%;
    padding: 2rem 3rem;
  }
}

@media (min-width: 1600px) {
  .content-container {
    max-width: 80%;
    padding: 2rem 4rem;
  }
}

.search-form {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 2rem;
}

.search-filters {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.search-results {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.search-results h2 {
  padding: 1.5rem;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  border-bottom: 1px solid #e2e8f0;
}

.results-list {
  max-height: 600px;
  overflow-y: auto;
}

.result-item {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #f7fafc;
  cursor: pointer;
  transition: background-color 0.2s;
}

.result-item:hover {
  background: #f7fafc;
}

.result-item:last-child {
  border-bottom: none;
}

.result-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
  color: #4a5568;
}

.result-content {
  flex: 1;
}

.result-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: #2d3748;
}

.result-path {
  color: #718096;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.result-meta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.meta-item {
  color: #a0aec0;
  font-size: 0.8rem;
}

.empty-state,
.initial-state {
  text-align: center;
  padding: 4rem;
}

.search-tips {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  text-align: left;
  max-width: 500px;
  margin: 0 auto;
}

.search-tips h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #2d3748;
}

.search-tips ul {
  list-style: none;
  padding: 0;
}

.search-tips li {
  padding: 0.5rem 0;
  color: #718096;
  position: relative;
  padding-left: 1.5rem;
}

.search-tips li::before {
  content: '•';
  color: #3182ce;
  position: absolute;
  left: 0;
}

@media (max-width: 768px) {
  .content-container {
    padding: 1rem;
  }
  
  .search-filters {
    flex-direction: column;
  }
  
  .search-filters .ant-select {
    width: 100% !important;
  }
}
</style>
