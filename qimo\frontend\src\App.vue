<script setup lang="ts">
import { RouterView } from 'vue-router'
import { useAppStore } from '@/stores/app'

const appStore = useAppStore()
</script>

<template>
  <div id="app" :class="{ 'dark-theme': appStore.isDarkTheme }">
    <RouterView />
  </div>
</template>

<style>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #2d3748;
  background: #ffffff;
}

#app {
  min-height: 100vh;
  transition: all 0.3s ease;
}

/* 暗色主题 */
.dark-theme {
  background: #1a202c;
  color: #e2e8f0;
}

.dark-theme .ant-layout {
  background: #1a202c;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式工具类 */
.container {
  max-width: 95%;
  margin: 0 auto;
  padding: 0 1rem;
  width: 100%;
}

@media (min-width: 768px) {
  .container {
    max-width: 90%;
    padding: 0 2rem;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 85%;
    padding: 0 3rem;
  }
}

@media (min-width: 1600px) {
  .container {
    max-width: 80%;
    padding: 0 4rem;
  }
}

/* 动画类 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}
</style>
