/**
 * 限流中间件
 * 防止API被恶意调用，保护服务器资源
 */

import { Context, Next } from 'koa';
import { config } from '../config';
import { ErrorCode } from '../utils/response';

// 限流记录接口
interface RateLimitRecord {
  count: number;
  resetTime: number;
  firstRequest: number;
}

// 内存存储（生产环境建议使用Redis）
const rateLimitStore = new Map<string, RateLimitRecord>();

/**
 * 获取客户端标识
 */
function getClientKey(ctx: Context): string {
  // 优先使用X-Forwarded-For，然后是X-Real-IP，最后是ctx.ip
  const ip = ctx.get('X-Forwarded-For') || 
             ctx.get('X-Real-IP') || 
             ctx.ip || 
             'unknown';
  
  // 可以根据需要添加更多标识符，如用户ID、API Key等
  return `rate_limit:${ip}`;
}

/**
 * 清理过期记录
 */
function cleanupExpiredRecords(): void {
  const now = Date.now();
  for (const [key, record] of rateLimitStore.entries()) {
    if (now > record.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}

/**
 * 获取或创建限流记录
 */
function getRateLimitRecord(key: string): RateLimitRecord {
  const now = Date.now();
  const existing = rateLimitStore.get(key);
  
  if (!existing || now > existing.resetTime) {
    // 创建新记录或重置过期记录
    const newRecord: RateLimitRecord = {
      count: 0,
      resetTime: now + config.security.rateLimit.windowMs,
      firstRequest: now
    };
    rateLimitStore.set(key, newRecord);
    return newRecord;
  }
  
  return existing;
}

/**
 * 检查是否应该跳过限流
 */
function shouldSkipRateLimit(ctx: Context): boolean {
  // 健康检查端点跳过限流
  if (ctx.path === '/health') {
    return true;
  }
  
  // 开发环境可以考虑跳过限流（可选）
  // if (config.app.env === 'development') {
  //   return true;
  // }
  
  return false;
}

/**
 * 限流中间件
 */
export function rateLimiter() {
  return async (ctx: Context, next: Next) => {
    // 检查是否跳过限流
    if (shouldSkipRateLimit(ctx)) {
      await next();
      return;
    }
    
    const clientKey = getClientKey(ctx);
    const record = getRateLimitRecord(clientKey);
    
    // 增加请求计数
    record.count++;
    
    // 计算剩余时间
    const now = Date.now();
    const remainingTime = Math.max(0, record.resetTime - now);
    const remainingRequests = Math.max(0, config.security.rateLimit.max - record.count);
    
    // 设置限流响应头
    ctx.set('X-RateLimit-Limit', String(config.security.rateLimit.max));
    ctx.set('X-RateLimit-Remaining', String(remainingRequests));
    ctx.set('X-RateLimit-Reset', String(Math.ceil(record.resetTime / 1000)));
    ctx.set('X-RateLimit-Window', String(config.security.rateLimit.windowMs / 1000));
    
    // 检查是否超过限制
    if (record.count > config.security.rateLimit.max) {
      // 记录限流日志
      console.warn('🚫 限流触发:', {
        clientKey,
        ip: ctx.ip,
        userAgent: ctx.get('User-Agent'),
        path: ctx.path,
        method: ctx.method,
        count: record.count,
        limit: config.security.rateLimit.max,
        resetTime: new Date(record.resetTime).toISOString()
      });
      
      // 设置Retry-After头
      ctx.set('Retry-After', String(Math.ceil(remainingTime / 1000)));
      
      // 返回限流错误
      const errorResponse = {
        success: false,
        error: {
          code: ErrorCode.INVALID_REQUEST,
          message: '请求过于频繁，请稍后再试',
          details: {
            limit: config.security.rateLimit.max,
            windowMs: config.security.rateLimit.windowMs,
            retryAfter: Math.ceil(remainingTime / 1000)
          }
        },
        timestamp: new Date().toISOString(),
        requestId: ctx.state.requestId
      };
      
      ctx.status = 429; // Too Many Requests
      ctx.body = errorResponse;
      return;
    }
    
    // 定期清理过期记录（每100个请求清理一次）
    if (Math.random() < 0.01) {
      cleanupExpiredRecords();
    }
    
    await next();
  };
}

/**
 * 获取限流统计信息
 */
export function getRateLimitStats(): {
  totalClients: number;
  activeRecords: number;
  memoryUsage: number;
} {
  cleanupExpiredRecords();
  
  return {
    totalClients: rateLimitStore.size,
    activeRecords: rateLimitStore.size,
    memoryUsage: JSON.stringify([...rateLimitStore.entries()]).length
  };
}

/**
 * 清除特定客户端的限流记录
 */
export function clearRateLimit(clientKey: string): boolean {
  return rateLimitStore.delete(clientKey);
}

/**
 * 清除所有限流记录
 */
export function clearAllRateLimits(): void {
  rateLimitStore.clear();
  console.log('🧹 已清除所有限流记录');
}