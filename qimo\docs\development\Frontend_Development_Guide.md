# 前端开发指南

## 概述

期末复习平台前端基于Vue3 + TypeScript + Vite构建，采用现代化的前端开发技术栈。本指南提供完整的前端开发环境搭建、架构设计、开发规范和最佳实践。

## 最新更新记录

### 2025-07-27 - 管理界面修复与优化
- **修复** Ant Design Vue 表格组件警告：更新为最新 v-slot 语法
- **修复** 管理界面状态切换功能：解决API调用和数据同步问题
- **优化** 表格组件实现：使用 `v-slot:bodyCell` 替代已弃用的 `column.slots`
- **验证** 管理界面所有功能：通过 Playwright 自动化测试确保稳定性

### 2025-07-27 - 管理界面集成
- 新增管理后台状态管理 (`frontend/src/stores/admin.ts`)
- 实现管理界面与用户界面的数据同步机制
- 优化防抖搜索功能，提升用户体验
- 完成前后端集成联调，确保数据一致性

## 技术栈

### 核心框架
- **Vue 3.4+**: 使用Composition API和`<script setup>`语法
- **TypeScript 5.x**: 全面的类型安全支持
- **Vite 5.x**: 快速的构建工具和开发服务器

### UI和样式
- **Ant Design Vue 4.x**: 企业级UI组件库
- **UnoCSS**: 原子化CSS引擎
- **CSS Variables**: 动态主题和样式变量

### 状态管理和路由
- **Pinia**: Vue 3官方推荐的状态管理库
- **Vue Router 4.x**: 官方路由管理器
- **Axios**: HTTP客户端库

### 开发工具
- **ESLint + Prettier**: 代码质量和格式化
- **TypeScript**: 类型检查和智能提示
- **Vite DevTools**: 开发调试工具

## 项目结构

```
frontend/
├── public/                 # 静态资源
│   ├── favicon.ico
│   └── index.html
├── src/
│   ├── main.ts            # 应用入口文件
│   ├── App.vue            # 根组件
│   ├── assets/            # 静态资源
│   │   ├── images/        # 图片资源
│   │   ├── icons/         # 图标资源
│   │   └── main.css       # 全局样式
│   ├── components/        # 通用组件
│   │   ├── common/        # 基础组件
│   │   ├── layout/        # 布局组件
│   │   └── ui/            # UI组件
│   ├── composables/       # 组合式函数
│   │   ├── useApi.ts      # API相关
│   │   ├── useAuth.ts     # 认证相关
│   │   └── useUtils.ts    # 工具函数
│   ├── layouts/           # 页面布局
│   │   ├── DefaultLayout.vue
│   │   └── AdminLayout.vue
│   ├── router/            # 路由配置
│   │   ├── index.ts       # 主路由
│   │   ├── routes.ts      # 路由定义
│   │   └── guards.ts      # 路由守卫
│   ├── stores/            # Pinia状态管理
│   │   ├── index.ts       # Store入口
│   │   ├── subject.ts     # 学科状态
│   │   ├── file.ts        # 文件状态
│   │   └── user.ts        # 用户状态
│   ├── types/             # TypeScript类型定义
│   │   ├── api.ts         # API类型
│   │   ├── common.ts      # 通用类型
│   │   └── components.ts  # 组件类型
│   ├── utils/             # 工具函数
│   │   ├── api.ts         # API工具
│   │   ├── format.ts      # 格式化工具
│   │   ├── storage.ts     # 存储工具
│   │   └── constants.ts   # 常量定义
│   └── views/             # 页面组件
│       ├── home/          # 首页
│       ├── subjects/      # 学科页面
│       ├── files/         # 文件页面
│       └── admin/         # 管理页面
├── tests/                 # 测试文件
├── package.json           # 项目配置
├── vite.config.ts         # Vite配置
├── tsconfig.json          # TypeScript配置
├── uno.config.ts          # UnoCSS配置
└── .eslintrc.js           # ESLint配置
```

## 开发环境搭建

### 1. 环境要求
- Node.js 18.0+
- npm 9.0+ 或 yarn 1.22+
- 现代浏览器（Chrome 90+, Firefox 88+, Safari 14+）

### 2. 安装依赖
```bash
cd frontend
npm install
# 或
yarn install
```

### 3. 开发服务器
```bash
# 启动开发服务器
npm run dev
# 访问 http://localhost:5173

# 类型检查
npm run type-check

# 代码检查
npm run lint

# 代码格式化
npm run format
```

### 4. 构建部署
```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview

# 分析构建包大小
npm run build:analyze
```

## 核心架构设计

### 1. 组件架构

#### 组件分层
```
Pages (页面组件)
  ↓
Layouts (布局组件)
  ↓
Features (功能组件)
  ↓
UI Components (UI组件)
  ↓
Base Components (基础组件)
```

#### 组件命名规范
- **页面组件**: `PascalCase` + `View` 后缀，如 `SubjectListView.vue`
- **布局组件**: `PascalCase` + `Layout` 后缀，如 `AdminLayout.vue`
- **功能组件**: `PascalCase`，如 `SubjectCard.vue`
- **基础组件**: `Base` + `PascalCase`，如 `BaseButton.vue`

### 2. 状态管理架构

#### Pinia Store设计
```typescript
// stores/subject.ts
export const useSubjectStore = defineStore('subject', () => {
  // 状态
  const subjects = ref<Subject[]>([])
  const currentSubject = ref<Subject | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const activeSubjects = computed(() => 
    subjects.value.filter(s => s.status === 1)
  )

  // 操作方法
  const fetchSubjects = async () => {
    loading.value = true
    try {
      const response = await subjectApi.getSubjects()
      subjects.value = response.data.data
    } catch (err) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  return {
    subjects,
    currentSubject,
    loading,
    error,
    activeSubjects,
    fetchSubjects
  }
})
```

#### 状态管理最佳实践
- **单一数据源**: 每个数据实体只在一个Store中管理
- **响应式设计**: 使用`ref`和`reactive`创建响应式状态
- **计算属性**: 使用`computed`处理派生状态
- **异步操作**: 在actions中处理异步逻辑
- **错误处理**: 统一的错误状态管理

### 3. 路由架构

#### 路由配置
```typescript
// router/routes.ts
export const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('@/layouts/DefaultLayout.vue'),
    children: [
      {
        path: '',
        name: 'Home',
        component: () => import('@/views/home/<USER>'),
        meta: { title: '首页' }
      },
      {
        path: '/subjects',
        name: 'SubjectList',
        component: () => import('@/views/subjects/SubjectListView.vue'),
        meta: { title: '学科列表' }
      }
    ]
  },
  {
    path: '/admin',
    component: () => import('@/layouts/AdminLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: 'dashboard',
        name: 'AdminDashboard',
        component: () => import('@/views/admin/DashboardView.vue'),
        meta: { title: '管理仪表盘' }
      }
    ]
  }
]
```

#### 路由守卫
```typescript
// router/guards.ts
export const setupRouterGuards = (router: Router) => {
  // 全局前置守卫
  router.beforeEach((to, from, next) => {
    // 设置页面标题
    if (to.meta?.title) {
      document.title = `${to.meta.title} - 期末复习平台`
    }

    // 权限检查
    if (to.meta?.requiresAuth) {
      const userStore = useUserStore()
      if (!userStore.isAuthenticated) {
        next('/login')
        return
      }
    }

    next()
  })

  // 全局后置钩子
  router.afterEach(() => {
    // 页面加载完成后的处理
    NProgress.done()
  })
}
```

## 开发规范

### 1. Ant Design Vue 最佳实践

#### 表格组件 (Table)
使用最新的 v-slot 语法，避免已弃用的 `column.slots` 语法：

```vue
<!-- ❌ 已弃用的写法 -->
<template>
  <a-table :columns="columns" :data-source="data" />
</template>

<script setup lang="ts">
const columns = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
    slots: { customRender: 'nameSlot' } // 已弃用
  }
]
</script>

<!-- ✅ 推荐的写法 -->
<template>
  <a-table :columns="columns" :data-source="data">
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'name'">
        <div class="custom-name">
          <span>{{ record.name }}</span>
          <a-tag v-if="record.description">{{ record.description }}</a-tag>
        </div>
      </template>
      <template v-else-if="column.key === 'status'">
        <a-switch
          :checked="record.status === 1"
          @change="handleStatusChange(record.id, $event)"
        />
      </template>
    </template>
  </a-table>
</template>

<script setup lang="ts">
const columns = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
    width: 250,
    ellipsis: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  }
]
</script>
```

#### 开关组件 (Switch)
正确处理状态切换和API调用：

```vue
<template>
  <a-switch
    :checked="record.status === 1"
    :loading="loading"
    @change="handleStatusChange(record.id, $event)"
  />
</template>

<script setup lang="ts">
const loading = ref(false)

const handleStatusChange = async (id: number, checked: boolean) => {
  loading.value = true
  try {
    await updateSubjectStatus(id, { status: checked ? 1 : 0 })
    message.success('状态更新成功')
  } catch (error) {
    message.error('状态更新失败')
  } finally {
    loading.value = false
  }
}
</script>
```

### 2. Vue 3 Composition API规范

#### 组件结构
```vue
<template>
  <div class="subject-list">
    <div v-if="loading" class="loading">
      <a-spin size="large" />
    </div>
    <div v-else-if="error" class="error">
      {{ error }}
    </div>
    <div v-else class="content">
      <subject-card
        v-for="subject in subjects"
        :key="subject.id"
        :subject="subject"
        @click="handleSubjectClick"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useSubjectStore } from '@/stores/subject'
import SubjectCard from '@/components/SubjectCard.vue'
import type { Subject } from '@/types/api'

// Props定义
interface Props {
  showAll?: boolean
}
const props = withDefaults(defineProps<Props>(), {
  showAll: false
})

// Emits定义
interface Emits {
  subjectSelect: [subject: Subject]
}
const emit = defineEmits<Emits>()

// 状态管理
const subjectStore = useSubjectStore()
const { subjects, loading, error } = storeToRefs(subjectStore)

// 事件处理
const handleSubjectClick = (subject: Subject) => {
  emit('subjectSelect', subject)
}

// 生命周期
onMounted(() => {
  subjectStore.fetchSubjects()
})
</script>

<style scoped>
.subject-list {
  @apply p-4;
}

.loading {
  @apply flex justify-center items-center h-64;
}

.error {
  @apply text-red-500 text-center p-4;
}

.content {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4;
}
</style>
```

#### 组合式函数
```typescript
// composables/useSubjectManagement.ts
export const useSubjectManagement = () => {
  const loading = ref(false)
  const subjects = ref<Subject[]>([])
  const error = ref<string | null>(null)

  const fetchSubjects = async () => {
    loading.value = true
    error.value = null
    
    try {
      const response = await subjectApi.getSubjects()
      subjects.value = response.data.data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取学科列表失败'
    } finally {
      loading.value = false
    }
  }

  const createSubject = async (data: CreateSubjectData) => {
    loading.value = true
    try {
      const response = await subjectApi.createSubject(data)
      subjects.value.push(response.data.data)
      return response.data.data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建学科失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    loading: readonly(loading),
    subjects: readonly(subjects),
    error: readonly(error),
    fetchSubjects,
    createSubject
  }
}
```

### 2. TypeScript规范

#### 类型定义
```typescript
// types/api.ts
export interface Subject {
  id: number
  name: string
  description: string
  created_at: string
  updated_at: string
  status: 0 | 1
  sort_order: number
  file_count?: number
  total_size?: number
}

export interface ApiResponse<T = any> {
  success: boolean
  data: T
  error?: {
    code: string
    message: string
  }
  timestamp: string
  requestId: string
}

export interface CreateSubjectData {
  name: string
  description?: string
  status?: 0 | 1
  sort_order?: number
}

// 工具类型
export type SubjectStatus = Subject['status']
export type SubjectWithStats = Required<Pick<Subject, 'file_count' | 'total_size'>> & Subject
```

#### 组件Props类型
```typescript
// 使用interface定义Props
interface SubjectCardProps {
  subject: Subject
  showStats?: boolean
  clickable?: boolean
}

// 使用泛型约束
interface TableColumn<T = any> {
  key: keyof T
  title: string
  dataIndex: keyof T
  sorter?: boolean
  width?: number
}
```

### 3. 样式规范

#### UnoCSS使用
```vue
<template>
  <!-- 响应式布局 -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    <!-- 卡片样式 -->
    <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
      <!-- 文本样式 -->
      <h3 class="text-lg font-semibold text-gray-900 mb-2">
        {{ subject.name }}
      </h3>
      <p class="text-gray-600 text-sm line-clamp-2">
        {{ subject.description }}
      </p>
      
      <!-- 状态指示器 -->
      <div class="flex items-center mt-4">
        <div 
          class="w-2 h-2 rounded-full mr-2"
          :class="subject.status === 1 ? 'bg-green-500' : 'bg-gray-400'"
        />
        <span class="text-xs text-gray-500">
          {{ subject.status === 1 ? '启用' : '禁用' }}
        </span>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 自定义样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
```

#### CSS变量和主题
```css
/* assets/main.css */
:root {
  /* 主色调 */
  --color-primary: #1890ff;
  --color-primary-hover: #40a9ff;
  --color-primary-active: #096dd9;
  
  /* 中性色 */
  --color-text-primary: #262626;
  --color-text-secondary: #8c8c8c;
  --color-border: #d9d9d9;
  --color-background: #f5f5f5;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* 圆角 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
}

/* 暗色主题 */
[data-theme="dark"] {
  --color-text-primary: #ffffff;
  --color-text-secondary: #a6a6a6;
  --color-border: #434343;
  --color-background: #1f1f1f;
}
```

## API集成

### 1. API工具配置

```typescript
// utils/api.ts
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios'
import type { ApiResponse } from '@/types/api'

// 创建axios实例
export const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加请求ID
    config.headers['X-Request-ID'] = generateRequestId()
    
    // 添加认证token
    const token = getAuthToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    return response
  },
  (error) => {
    // 统一错误处理
    const message = error.response?.data?.error?.message || error.message
    console.error('API Error:', message)
    
    // 根据状态码处理
    if (error.response?.status === 401) {
      // 清除认证信息，跳转登录
      clearAuthToken()
      router.push('/login')
    }
    
    return Promise.reject(new Error(message))
  }
)

// 管理员API配置
export const adminApi = {
  createAdminRequest: (config: AxiosRequestConfig = {}) => {
    const adminConfig = {
      ...config,
      headers: {
        ...config.headers,
        'x-admin-path': '/admin-panel-abcdef'
      }
    }
    return api(adminConfig)
  }
}
```

### 2. API服务层

```typescript
// utils/api/subject.ts
export const subjectApi = {
  // 获取学科列表
  getSubjects: (): Promise<AxiosResponse<ApiResponse<Subject[]>>> => {
    return api.get('/subjects')
  },

  // 获取学科详情
  getSubject: (id: number): Promise<AxiosResponse<ApiResponse<Subject>>> => {
    return api.get(`/subjects/${id}`)
  },

  // 创建学科
  createSubject: (data: CreateSubjectData): Promise<AxiosResponse<ApiResponse<Subject>>> => {
    return api.post('/subjects', data)
  },

  // 更新学科
  updateSubject: (id: number, data: Partial<CreateSubjectData>): Promise<AxiosResponse<ApiResponse<Subject>>> => {
    return api.put(`/subjects/${id}`, data)
  },

  // 删除学科
  deleteSubject: (id: number): Promise<AxiosResponse<ApiResponse<void>>> => {
    return api.delete(`/subjects/${id}`)
  }
}

// 管理员API
export const adminSubjectApi = {
  getSubjects: (): Promise<AxiosResponse<ApiResponse<Subject[]>>> => {
    return adminApi.createAdminRequest({
      method: 'GET',
      url: '/admin/subjects'
    })
  },

  createSubject: (data: CreateSubjectData): Promise<AxiosResponse<ApiResponse<Subject>>> => {
    return adminApi.createAdminRequest({
      method: 'POST',
      url: '/admin/subjects',
      data
    })
  }
}
```

## 管理界面集成

### 1. 管理后台状态管理

管理界面使用独立的状态管理store，与普通用户界面实现数据同步：

```typescript
// stores/admin.ts
import { defineStore } from 'pinia'
import { useSubjectStore } from './subject'

export const useAdminStore = defineStore('admin', () => {
  const subjects = ref<Subject[]>([])
  const loading = ref(false)
  const cache = ref<{ data: Subject[]; timestamp: number } | null>(null)
  const CACHE_DURATION = 2 * 60 * 1000 // 2分钟缓存

  // 同步数据到普通用户store
  const syncToUserStore = async () => {
    try {
      const subjectStore = useSubjectStore()
      subjectStore.clearCache()

      if (subjectStore.hasSubjects) {
        await subjectStore.fetchSubjects(false, true) // 强制刷新
      }

      console.log('🔄 Synced data to user store')
    } catch (err) {
      console.warn('⚠️ Failed to sync to user store:', err)
    }
  }

  // 创建学科
  const createSubject = async (data: CreateSubjectData) => {
    try {
      loading.value = true
      const response = await adminSubjectApi.createSubject(data)

      if (response.data.success) {
        await fetchSubjects(true) // 刷新管理数据
        await syncToUserStore() // 同步到用户store
        return response.data.data
      }
    } catch (error) {
      console.error('❌ Failed to create subject:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  return {
    subjects: readonly(subjects),
    loading: readonly(loading),
    createSubject,
    syncToUserStore
  }
})
```

### 2. 防抖搜索实现

```typescript
// composables/useAdminApi.ts
import { debounce } from 'lodash-es'

export const useAdminApi = () => {
  const searchState = reactive({
    keyword: '',
    status: 'all'
  })

  // 防抖搜索函数
  const debouncedSearch = debounce(() => {
    pagination.current = 1
    pagination.total = filteredSubjects.value.length
  }, 300)

  // 监听搜索状态变化
  watch(
    () => [searchState.keyword, searchState.status],
    () => {
      debouncedSearch()
    },
    { deep: true }
  )

  return {
    searchState,
    debouncedSearch
  }
}
```

### 3. 管理界面布局

```vue
<!-- layouts/AdminLayout.vue -->
<template>
  <div class="admin-layout">
    <a-layout class="min-h-screen">
      <!-- 侧边栏 -->
      <a-layout-sider
        v-model:collapsed="collapsed"
        :trigger="null"
        collapsible
        class="admin-sider"
      >
        <div class="admin-logo">
          <BookOutlined />
          <span v-if="!collapsed" class="logo-text">管理后台</span>
        </div>

        <a-menu
          v-model:selectedKeys="selectedKeys"
          theme="dark"
          mode="inline"
          @click="handleMenuClick"
        >
          <a-menu-item key="dashboard">
            <DashboardOutlined />
            <span>仪表盘</span>
          </a-menu-item>

          <a-sub-menu key="content">
            <template #icon>
              <FolderOutlined />
            </template>
            <template #title>内容管理</template>

            <a-menu-item key="subjects">
              <BookOutlined />
              <span>学科管理</span>
            </a-menu-item>
          </a-sub-menu>
        </a-menu>
      </a-layout-sider>

      <!-- 主内容区 -->
      <a-layout>
        <a-layout-header class="admin-header">
          <MenuFoldOutlined
            v-if="collapsed"
            class="trigger"
            @click="() => (collapsed = !collapsed)"
          />
          <MenuUnfoldOutlined
            v-else
            class="trigger"
            @click="() => (collapsed = !collapsed)"
          />

          <div class="header-right">
            <a-dropdown>
              <a-button type="text">
                <UserOutlined />
                管理员
                <DownOutlined />
              </a-button>
            </a-dropdown>
          </div>
        </a-layout-header>

        <a-layout-content class="admin-content">
          <router-view />
        </a-layout-content>
      </a-layout>
    </a-layout>
  </div>
</template>
```

### 4. 数据同步机制

管理后台与普通用户界面通过以下机制实现数据同步：

1. **状态管理同步**: admin store操作完成后自动调用user store刷新
2. **缓存管理**: 清除相关缓存确保数据一致性
3. **实时更新**: 管理操作立即反映到用户界面

```typescript
// 同步流程示例
const handleAdminOperation = async () => {
  // 1. 执行管理操作
  await adminStore.createSubject(formData)

  // 2. 刷新管理界面数据
  await adminStore.fetchSubjects(true)

  // 3. 同步到用户界面
  await adminStore.syncToUserStore()

  // 4. 用户界面自动更新
}
```

## 性能优化

### 1. 代码分割和懒加载

```typescript
// 路由级别懒加载
const routes = [
  {
    path: '/subjects',
    component: () => import('@/views/subjects/SubjectListView.vue')
  }
]

// 组件级别懒加载
const LazyComponent = defineAsyncComponent(() => import('@/components/HeavyComponent.vue'))
```

### 2. 缓存策略

```typescript
// composables/useCache.ts
export const useCache = <T>(key: string, ttl: number = 5 * 60 * 1000) => {
  const cache = new Map<string, { data: T; timestamp: number }>()

  const get = (cacheKey: string): T | null => {
    const item = cache.get(cacheKey)
    if (!item) return null

    if (Date.now() - item.timestamp > ttl) {
      cache.delete(cacheKey)
      return null
    }

    return item.data
  }

  const set = (cacheKey: string, data: T) => {
    cache.set(cacheKey, { data, timestamp: Date.now() })
  }

  const clear = () => {
    cache.clear()
  }

  return { get, set, clear }
}
```

### 3. 虚拟滚动

```vue
<template>
  <div class="virtual-list" ref="containerRef">
    <div 
      class="virtual-list-phantom" 
      :style="{ height: totalHeight + 'px' }"
    />
    <div 
      class="virtual-list-content"
      :style="{ transform: `translateY(${offsetY}px)` }"
    >
      <div
        v-for="item in visibleItems"
        :key="item.id"
        class="virtual-list-item"
        :style="{ height: itemHeight + 'px' }"
      >
        <slot :item="item" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 虚拟滚动实现
const useVirtualList = (items: Ref<any[]>, itemHeight: number) => {
  const containerRef = ref<HTMLElement>()
  const scrollTop = ref(0)
  const containerHeight = ref(0)

  const visibleCount = computed(() => 
    Math.ceil(containerHeight.value / itemHeight) + 2
  )

  const startIndex = computed(() => 
    Math.floor(scrollTop.value / itemHeight)
  )

  const visibleItems = computed(() => 
    items.value.slice(startIndex.value, startIndex.value + visibleCount.value)
  )

  const totalHeight = computed(() => 
    items.value.length * itemHeight
  )

  const offsetY = computed(() => 
    startIndex.value * itemHeight
  )

  return {
    containerRef,
    visibleItems,
    totalHeight,
    offsetY
  }
}
</script>
```

## 测试策略

### 1. 单元测试

```typescript
// tests/components/SubjectCard.test.ts
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import SubjectCard from '@/components/SubjectCard.vue'

describe('SubjectCard', () => {
  const mockSubject = {
    id: 1,
    name: '数学',
    description: '高等数学课程',
    status: 1,
    created_at: '2025-01-01',
    updated_at: '2025-01-01',
    sort_order: 1
  }

  it('renders subject information correctly', () => {
    const wrapper = mount(SubjectCard, {
      props: { subject: mockSubject }
    })

    expect(wrapper.find('.subject-name').text()).toBe('数学')
    expect(wrapper.find('.subject-description').text()).toBe('高等数学课程')
  })

  it('emits click event when clicked', async () => {
    const wrapper = mount(SubjectCard, {
      props: { subject: mockSubject, clickable: true }
    })

    await wrapper.trigger('click')
    expect(wrapper.emitted('click')).toBeTruthy()
  })
})
```

### 2. E2E测试

```typescript
// tests/e2e/subject-management.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Subject Management', () => {
  test('should display subject list', async ({ page }) => {
    await page.goto('/subjects')
    
    // 等待数据加载
    await page.waitForSelector('.subject-card')
    
    // 验证学科卡片显示
    const subjectCards = page.locator('.subject-card')
    await expect(subjectCards).toHaveCountGreaterThan(0)
    
    // 验证学科名称显示
    const firstCard = subjectCards.first()
    await expect(firstCard.locator('.subject-name')).toBeVisible()
  })

  test('should navigate to subject detail', async ({ page }) => {
    await page.goto('/subjects')
    
    // 点击第一个学科卡片
    await page.locator('.subject-card').first().click()
    
    // 验证导航到详情页
    await expect(page).toHaveURL(/\/subjects\/\d+/)
  })
})
```

## 部署配置

### 1. 环境变量

```bash
# .env.development
VITE_API_BASE_URL=http://localhost:3000/api
VITE_APP_TITLE=期末复习平台
VITE_APP_VERSION=1.0.0

# .env.production
VITE_API_BASE_URL=https://api.example.com/api
VITE_APP_TITLE=期末复习平台
VITE_APP_VERSION=1.0.0
```

### 2. 构建优化

```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          antd: ['ant-design-vue'],
          utils: ['axios', 'dayjs']
        }
      }
    },
    chunkSizeWarningLimit: 1000
  },
  optimizeDeps: {
    include: ['vue', 'vue-router', 'pinia', 'ant-design-vue']
  }
})
```

## 故障排除

### 1. 常见开发问题

#### 构建错误
```bash
# 清除缓存
rm -rf node_modules/.vite
rm -rf dist

# 重新安装依赖
npm ci

# 重新构建
npm run build
```

#### 类型错误
```bash
# 重新生成类型声明
npm run type-check

# 检查TypeScript配置
npx tsc --showConfig
```

#### 样式问题
```bash
# 重新生成UnoCSS
npx unocss --watch

# 检查样式冲突
npm run dev -- --debug
```

### 2. 性能问题诊断

#### Bundle分析
```bash
# 分析构建包大小
npm run build:analyze

# 查看依赖关系
npx vite-bundle-analyzer dist
```

#### 运行时性能
```javascript
// 使用Vue DevTools
// 启用性能追踪
app.config.performance = true

// 组件性能监控
const { onUpdated } = Vue
onUpdated(() => {
  console.time('component-update')
  // 组件更新逻辑
  console.timeEnd('component-update')
})
```

### 3. 调试技巧

#### Vue DevTools
- 安装Vue DevTools浏览器扩展
- 在开发环境中启用调试模式
- 使用组件检查器和状态管理器

#### 网络调试
```typescript
// API请求调试
api.interceptors.request.use(config => {
  console.log('Request:', config)
  return config
})

api.interceptors.response.use(
  response => {
    console.log('Response:', response)
    return response
  },
  error => {
    console.error('API Error:', error)
    return Promise.reject(error)
  }
)
```

## 最佳实践

### 1. 代码组织
- **单一职责**: 每个组件只负责一个功能
- **组合优于继承**: 使用组合式函数而非混入
- **明确的接口**: 定义清晰的Props和Emits
- **适当的抽象**: 避免过度抽象和过早优化

### 2. 性能优化
- **懒加载**: 路由和组件按需加载
- **缓存策略**: 合理使用缓存减少重复请求
- **虚拟化**: 大列表使用虚拟滚动
- **防抖节流**: 用户输入和滚动事件优化

### 3. 用户体验
- **加载状态**: 提供明确的加载反馈
- **错误处理**: 友好的错误提示和恢复机制
- **响应式设计**: 适配不同设备和屏幕尺寸
- **无障碍访问**: 遵循WCAG无障碍指南

### 4. 代码质量
- **类型安全**: 充分利用TypeScript类型系统
- **代码审查**: 建立代码审查流程
- **自动化测试**: 单元测试和E2E测试覆盖
- **持续集成**: 自动化构建和部署流程

## 团队协作

### 1. 开发流程
1. **需求分析**: 理解产品需求和设计稿
2. **技术设计**: 制定技术实现方案
3. **编码实现**: 按照规范编写代码
4. **自测验证**: 本地测试和自我检查
5. **代码审查**: 团队成员代码审查
6. **集成测试**: 与后端接口联调
7. **部署上线**: 发布到生产环境

### 2. 沟通协作
- **定期同步**: 每日站会和周例会
- **文档维护**: 及时更新技术文档
- **知识分享**: 技术分享和最佳实践交流
- **问题反馈**: 及时反馈和解决技术问题

### 3. 版本管理
```bash
# 功能分支开发
git checkout -b feature/subject-management
git add .
git commit -m "feat(subject): add subject management page"
git push origin feature/subject-management

# 代码审查后合并
git checkout main
git merge feature/subject-management
git push origin main
```

## 扩展指南

### 1. 添加新页面
1. 在`views`目录创建页面组件
2. 在`router/routes.ts`中添加路由配置
3. 如需要，创建对应的Store
4. 添加相关的类型定义
5. 编写单元测试和E2E测试

### 2. 添加新组件
1. 在`components`目录创建组件
2. 定义Props和Emits接口
3. 编写组件文档和使用示例
4. 添加单元测试
5. 在Storybook中添加组件故事

### 3. 集成第三方库
1. 评估库的必要性和影响
2. 安装依赖并配置
3. 创建封装层或适配器
4. 更新类型定义
5. 编写使用文档和示例

---

**文档版本**: v1.0
**创建时间**: 2025年07月27日
**作者**: Alex (Engineer)
**版权**: 随影
