import { db } from '../database/connection.js'
import type { FileNode } from '../types/index.js'

export class FileNodeModel {
  // 根据父节点ID获取文件列表（支持分页和搜索，优化版）
  static findByParent(
    subjectId: string,
    parentId: string | null,
    search?: string,
    page: number = 1,
    limit: number = 20
  ): { files: FileNode[]; total: number } {
    const offset = (page - 1) * limit

    // 构建查询条件
    let whereClause = 'WHERE subject_id = ?'
    let params: any[] = [subjectId]

    if (parentId === null) {
      whereClause += ' AND parent_id IS NULL'
    } else {
      whereClause += ' AND parent_id = ?'
      params.push(parentId)
    }

    if (search && search.trim()) {
      whereClause += ' AND name LIKE ?'
      params.push(`%${search.trim()}%`)
    }

    // 优化：使用CTE进行单次查询获取总数和数据
    const sql = `
      WITH filtered_files AS (
        SELECT * FROM file_nodes ${whereClause}
      ),
      total_count AS (
        SELECT COUNT(*) as count FROM filtered_files
      )
      SELECT
        f.*,
        t.count as total_count
      FROM filtered_files f
      CROSS JOIN total_count t
      ORDER BY
        CASE WHEN f.type = 'folder' THEN 0 ELSE 1 END,
        f.name ASC
      LIMIT ? OFFSET ?
    `
    params.push(limit, offset)

    const results = db.prepare(sql).all(...params) as (FileNode & { total_count: number })[]

    if (results.length === 0) {
      // 如果没有结果，单独查询总数
      const countSql = `SELECT COUNT(*) as count FROM file_nodes ${whereClause}`
      const countResult = db.prepare(countSql).get(...params.slice(0, -2)) as { count: number }
      return { files: [], total: countResult.count }
    }

    const total = results[0].total_count
    const files = results.map(({ total_count, ...file }) => file)

    return { files, total }
  }

  // 根据ID查找文件节点
  static findById(id: string): FileNode | null {
    const sql = 'SELECT * FROM file_nodes WHERE id = ?'
    return db.prepare(sql).get(id) as FileNode | null
  }

  // 根据路径查找文件节点
  static findByPath(subjectId: string, path: string): FileNode | null {
    const sql = 'SELECT * FROM file_nodes WHERE subject_id = ? AND path = ?'
    return db.prepare(sql).get(subjectId, path) as FileNode | null
  }

  // 搜索文件（支持分页和类型过滤，优化版）
  static search(
    subjectId: string,
    query: string,
    type?: 'file' | 'folder' | 'all',
    page: number = 1,
    limit: number = 20
  ): { files: FileNode[]; total: number } {
    const offset = (page - 1) * limit

    // 构建查询条件
    let whereClause = 'WHERE subject_id = ? AND name LIKE ?'
    let params: any[] = [subjectId, `%${query.trim()}%`]

    if (type && type !== 'all') {
      whereClause += ' AND type = ?'
      params.push(type)
    }

    // 优化：使用CTE进行单次查询获取总数和数据
    const sql = `
      WITH search_results AS (
        SELECT * FROM file_nodes ${whereClause}
      ),
      total_count AS (
        SELECT COUNT(*) as count FROM search_results
      )
      SELECT
        s.*,
        t.count as total_count
      FROM search_results s
      CROSS JOIN total_count t
      ORDER BY
        CASE WHEN s.name = ? THEN 0 ELSE 1 END,
        CASE WHEN s.type = 'folder' THEN 0 ELSE 1 END,
        s.name ASC
      LIMIT ? OFFSET ?
    `
    params.push(query.trim(), limit, offset)

    const results = db.prepare(sql).all(...params) as (FileNode & { total_count: number })[]

    if (results.length === 0) {
      // 如果没有结果，单独查询总数
      const countSql = `SELECT COUNT(*) as count FROM file_nodes ${whereClause}`
      const countResult = db.prepare(countSql).get(...params.slice(0, -3)) as { count: number }
      return { files: [], total: countResult.count }
    }

    const total = results[0].total_count
    const files = results.map(({ total_count, ...file }) => file)

    return { files, total }
  }

  // 获取面包屑路径（优化的递归CTE查询）
  static getBreadcrumbPath(subjectId: string, nodeId: string | null): FileNode[] {
    if (!nodeId) {
      return [] // 根目录没有面包屑
    }

    // 使用递归CTE一次性获取完整路径
    const sql = `
      WITH RECURSIVE breadcrumb_path AS (
        -- 基础情况：目标节点
        SELECT id, subject_id, name, type, path, parent_id, size, created_at, 0 as level
        FROM file_nodes
        WHERE id = ? AND subject_id = ?

        UNION ALL

        -- 递归情况：查找父节点
        SELECT f.id, f.subject_id, f.name, f.type, f.path, f.parent_id, f.size, f.created_at, bp.level + 1
        FROM file_nodes f
        INNER JOIN breadcrumb_path bp ON f.id = bp.parent_id
        WHERE f.subject_id = ?
      )
      SELECT id, subject_id, name, type, path, parent_id, size, created_at
      FROM breadcrumb_path
      ORDER BY level DESC
    `

    const results = db.prepare(sql).all(nodeId, subjectId, subjectId) as FileNode[]
    return results
  }

  // 检查节点是否存在
  static exists(id: string): boolean {
    const sql = 'SELECT 1 FROM file_nodes WHERE id = ?'
    const result = db.prepare(sql).get(id)
    return !!result
  }

  // 检查节点是否属于指定学科
  static belongsToSubject(id: string, subjectId: string): boolean {
    const sql = 'SELECT 1 FROM file_nodes WHERE id = ? AND subject_id = ?'
    const result = db.prepare(sql).get(id, subjectId)
    return !!result
  }

  // 创建文件节点
  static create(data: Omit<FileNode, 'created_at'> & { created_at?: number }): FileNode {
    const now = Date.now()
    const fileNode: FileNode = {
      ...data,
      created_at: data.created_at || now
    }

    const sql = `
      INSERT INTO file_nodes (id, subject_id, name, type, path, parent_id, size, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `

    db.prepare(sql).run(
      fileNode.id,
      fileNode.subject_id,
      fileNode.name,
      fileNode.type,
      fileNode.path,
      fileNode.parent_id,
      fileNode.size,
      fileNode.created_at
    )

    return fileNode
  }

  // 删除文件节点
  static delete(id: string): boolean {
    const sql = 'DELETE FROM file_nodes WHERE id = ?'
    const result = db.prepare(sql).run(id)
    return result.changes > 0
  }

  // 获取所有文件节点（用于测试清理）
  static findAll(): FileNode[] {
    const sql = 'SELECT * FROM file_nodes ORDER BY created_at DESC'
    return db.prepare(sql).all() as FileNode[]
  }
}
