/**
 * 学科业务逻辑层 (Service)
 * 负责学科相关的业务逻辑处理
 * 
 * <AUTHOR> (Engineer)
 * @date 2025-01-27
 */

import { subjectDao } from '../dao/subjectDao';
import { Subject } from '../types';

export interface SubjectWithStats extends Subject {
  fileCount?: number;
  folderCount?: number;
  totalSize?: number;
}

export class SubjectService {
  /**
   * 获取所有学科列表
   * @param includeStats 是否包含统计信息
   * @returns 学科列表
   */
  async getAllSubjects(includeStats: boolean = false): Promise<SubjectWithStats[]> {
    try {
      const subjects = subjectDao.findAll();

      if (!includeStats) {
        return subjects;
      }

      // 添加统计信息
      const subjectsWithStats: SubjectWithStats[] = [];
      for (const subject of subjects) {
        const stats = subjectDao.getStats(subject.id);
        subjectsWithStats.push({
          ...subject,
          ...stats
        });
      }

      return subjectsWithStats;
    } catch (error) {
      console.error('SubjectService.getAllSubjects error:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取学科详情
   * @param id 学科ID
   * @param includeStats 是否包含统计信息
   * @returns 学科详情或null
   */
  async getSubjectById(id: number, includeStats: boolean = false): Promise<SubjectWithStats | null> {
    try {
      // 参数验证
      if (!id || id <= 0) {
        throw new Error('学科ID必须是有效的正整数');
      }

      const subject = subjectDao.findById(id, true); // 管理员可以查看所有学科
      if (!subject) {
        return null;
      }

      if (!includeStats) {
        return subject;
      }

      // 添加统计信息
      const stats = subjectDao.getStats(subject.id);
      return {
        ...subject,
        ...stats
      };
    } catch (error) {
      console.error('SubjectService.getSubjectById error:', error);
      throw error;
    }
  }

  /**
   * 根据名称获取学科
   * @param name 学科名称
   * @returns 学科信息或null
   */
  async getSubjectByName(name: string): Promise<Subject | null> {
    try {
      // 参数验证
      if (!name || name.trim().length === 0) {
        throw new Error('学科名称不能为空');
      }

      return subjectDao.findByName(name.trim());
    } catch (error) {
      console.error('SubjectService.getSubjectByName error:', error);
      throw error;
    }
  }

  /**
   * 创建新学科
   * @param subjectData 学科数据
   * @returns 创建的学科ID
   */
  async createSubject(subjectData: Omit<Subject, 'id' | 'created_at' | 'updated_at'>): Promise<number> {
    try {
      // 参数验证
      if (!subjectData.name || subjectData.name.trim().length === 0) {
        throw new Error('学科名称不能为空');
      }

      if (subjectData.name.length > 100) {
        throw new Error('学科名称不能超过100个字符');
      }

      // 检查名称是否已存在
      const existingSubject = await this.getSubjectByName(subjectData.name);
      if (existingSubject) {
        throw new Error('学科名称已存在');
      }

      // 创建学科
      const subjectId = subjectDao.create({
        ...subjectData,
        name: subjectData.name.trim(),
        description: subjectData.description?.trim() || ''
      });

      return subjectId;
    } catch (error) {
      console.error('SubjectService.createSubject error:', error);
      throw error;
    }
  }

  /**
   * 更新学科信息
   * @param id 学科ID
   * @param updates 更新的字段
   * @returns 是否更新成功
   */
  async updateSubject(id: number, updates: Partial<Omit<Subject, 'id' | 'created_at' | 'updated_at'>>): Promise<boolean> {
    try {
      // 参数验证
      if (!id || id <= 0) {
        throw new Error('学科ID必须是有效的正整数');
      }

      if (Object.keys(updates).length === 0) {
        throw new Error('更新数据不能为空');
      }

      // 检查学科是否存在
      const existingSubject = await this.getSubjectById(id);
      if (!existingSubject) {
        throw new Error('学科不存在');
      }

      // 验证更新字段
      if (updates.name !== undefined) {
        if (!updates.name || updates.name.trim().length === 0) {
          throw new Error('学科名称不能为空');
        }
        if (updates.name.length > 100) {
          throw new Error('学科名称不能超过100个字符');
        }
        updates.name = updates.name.trim();

        // 检查名称是否与其他学科冲突
        const conflictSubject = await this.getSubjectByName(updates.name);
        if (conflictSubject && conflictSubject.id !== id) {
          throw new Error('学科名称已存在');
        }
      }

      if (updates.description !== undefined) {
        updates.description = updates.description?.trim() || '';
      }

      return subjectDao.update(id, updates);
    } catch (error) {
      console.error('SubjectService.updateSubject error:', error);
      throw error;
    }
  }

  /**
   * 删除学科（软删除）
   * @param id 学科ID
   * @returns 是否删除成功
   */
  async deleteSubject(id: number): Promise<boolean> {
    try {
      // 参数验证
      if (!id || id <= 0) {
        throw new Error('学科ID必须是有效的正整数');
      }

      // 检查学科是否存在
      const existingSubject = await this.getSubjectById(id);
      if (!existingSubject) {
        throw new Error('学科不存在');
      }

      return subjectDao.softDelete(id);
    } catch (error) {
      console.error('SubjectService.deleteSubject error:', error);
      throw error;
    }
  }

  /**
   * 硬删除学科（管理员专用，级联删除）
   * @param id 学科ID
   * @returns 是否删除成功
   */
  async hardDeleteSubject(id: number): Promise<boolean> {
    try {
      // 参数验证
      if (!id || id <= 0) {
        throw new Error('学科ID必须是有效的正整数');
      }

      // 检查学科是否存在
      const existingSubject = await this.getSubjectById(id);
      if (!existingSubject) {
        throw new Error('学科不存在');
      }

      // 硬删除（数据库外键约束会自动级联删除相关文件节点）
      return subjectDao.hardDelete(id);
    } catch (error) {
      console.error('SubjectService.hardDeleteSubject error:', error);
      throw error;
    }
  }

  /**
   * 批量获取学科（管理员专用）
   * @param ids 学科ID数组
   * @param includeStats 是否包含统计信息
   * @returns 学科列表
   */
  async getSubjectsByIds(ids: number[], includeStats: boolean = false): Promise<SubjectWithStats[]> {
    try {
      // 参数验证
      if (!Array.isArray(ids) || ids.length === 0) {
        return [];
      }

      // 验证所有ID都是有效的正整数
      for (const id of ids) {
        if (!id || id <= 0) {
          throw new Error('所有学科ID必须是有效的正整数');
        }
      }

      const subjects = subjectDao.findByIds(ids);

      if (!includeStats) {
        return subjects;
      }

      // 添加统计信息
      const subjectsWithStats: SubjectWithStats[] = [];
      for (const subject of subjects) {
        const stats = subjectDao.getStats(subject.id);
        subjectsWithStats.push({
          ...subject,
          ...stats
        });
      }

      return subjectsWithStats;
    } catch (error) {
      console.error('SubjectService.getSubjectsByIds error:', error);
      throw error;
    }
  }

  /**
   * 批量更新学科状态（管理员专用）
   * @param ids 学科ID数组
   * @param status 新状态
   * @returns 更新成功的数量
   */
  async batchUpdateStatus(ids: number[], status: number): Promise<number> {
    try {
      // 参数验证
      if (!Array.isArray(ids) || ids.length === 0) {
        throw new Error('学科ID数组不能为空');
      }

      if (![0, 1].includes(status)) {
        throw new Error('状态值必须是0或1');
      }

      // 验证所有ID都是有效的正整数
      for (const id of ids) {
        if (!id || id <= 0) {
          throw new Error('所有学科ID必须是有效的正整数');
        }
      }

      return subjectDao.batchUpdateStatus(ids, status);
    } catch (error) {
      console.error('SubjectService.batchUpdateStatus error:', error);
      throw error;
    }
  }

  /**
   * 获取学科统计信息
   * @param id 学科ID
   * @returns 统计信息
   */
  async getSubjectStats(id: number): Promise<{ fileCount: number; folderCount: number; totalSize: number }> {
    try {
      // 参数验证
      if (!id || id <= 0) {
        throw new Error('学科ID必须是有效的正整数');
      }

      // 检查学科是否存在
      const existingSubject = await this.getSubjectById(id);
      if (!existingSubject) {
        throw new Error('学科不存在');
      }

      return subjectDao.getStats(id);
    } catch (error) {
      console.error('SubjectService.getSubjectStats error:', error);
      throw error;
    }
  }

  /**
   * 验证学科ID是否有效
   * @param id 学科ID
   * @returns 是否有效
   */
  async isValidSubjectId(id: number): Promise<boolean> {
    try {
      if (!id || id <= 0) {
        return false;
      }

      const subject = await this.getSubjectById(id);
      return subject !== null;
    } catch (error) {
      console.error('SubjectService.isValidSubjectId error:', error);
      return false;
    }
  }
}

// 导出单例实例
export const subjectService = new SubjectService();