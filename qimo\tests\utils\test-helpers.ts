import { Page, expect } from '@playwright/test'

/**
 * 测试辅助工具
 * 提供通用的测试方法和断言
 */

export class TestHelpers {
  constructor(private page: Page) {}

  /**
   * 等待页面加载完成
   */
  async waitForPageLoad(): Promise<void> {
    await this.page.waitForLoadState('networkidle')
    await this.page.waitForLoadState('domcontentloaded')
  }

  /**
   * 等待API请求完成
   */
  async waitForApiResponse(urlPattern: string | RegExp): Promise<void> {
    await this.page.waitForResponse(response => {
      const url = response.url()
      if (typeof urlPattern === 'string') {
        return url.includes(urlPattern)
      }
      return urlPattern.test(url)
    })
  }

  /**
   * 检查页面标题
   */
  async checkPageTitle(expectedTitle: string): Promise<void> {
    await expect(this.page).toHaveTitle(expectedTitle)
  }

  /**
   * 检查页面URL
   */
  async checkPageUrl(expectedUrl: string | RegExp): Promise<void> {
    await expect(this.page).toHaveURL(expectedUrl)
  }

  /**
   * 检查元素是否可见
   */
  async checkElementVisible(selector: string): Promise<void> {
    await expect(this.page.locator(selector)).toBeVisible()
  }

  /**
   * 检查元素文本内容
   */
  async checkElementText(selector: string, expectedText: string | RegExp): Promise<void> {
    await expect(this.page.locator(selector)).toHaveText(expectedText)
  }

  /**
   * 检查元素数量
   */
  async checkElementCount(selector: string, expectedCount: number): Promise<void> {
    await expect(this.page.locator(selector)).toHaveCount(expectedCount)
  }

  /**
   * 点击元素并等待导航
   */
  async clickAndWaitForNavigation(selector: string): Promise<void> {
    await Promise.all([
      this.page.waitForNavigation(),
      this.page.click(selector)
    ])
  }

  /**
   * 滚动到元素位置
   */
  async scrollToElement(selector: string): Promise<void> {
    await this.page.locator(selector).scrollIntoViewIfNeeded()
  }

  /**
   * 等待元素出现
   */
  async waitForElement(selector: string, timeout: number = 5000): Promise<void> {
    await this.page.waitForSelector(selector, { timeout })
  }

  /**
   * 检查加载状态
   */
  async checkLoadingState(isLoading: boolean = false): Promise<void> {
    if (isLoading) {
      await expect(this.page.locator('.ant-spin')).toBeVisible()
    } else {
      await expect(this.page.locator('.ant-spin')).not.toBeVisible()
    }
  }

  /**
   * 检查错误消息
   */
  async checkErrorMessage(message?: string): Promise<void> {
    const errorSelector = '.ant-message-error, .ant-alert-error'
    if (message) {
      await expect(this.page.locator(errorSelector)).toContainText(message)
    } else {
      await expect(this.page.locator(errorSelector)).toBeVisible()
    }
  }

  /**
   * 检查成功消息
   */
  async checkSuccessMessage(message?: string): Promise<void> {
    const successSelector = '.ant-message-success, .ant-alert-success'
    if (message) {
      await expect(this.page.locator(successSelector)).toContainText(message)
    } else {
      await expect(this.page.locator(successSelector)).toBeVisible()
    }
  }

  /**
   * 截图对比 (视觉回归测试)
   */
  async compareScreenshot(name: string, options?: {
    fullPage?: boolean
    clip?: { x: number; y: number; width: number; height: number }
  }): Promise<void> {
    await expect(this.page).toHaveScreenshot(`${name}.png`, {
      fullPage: options?.fullPage || false,
      clip: options?.clip,
      threshold: 0.2, // 允许20%的像素差异
      maxDiffPixels: 1000 // 最大差异像素数
    })
  }

  /**
   * 检查响应式设计
   */
  async checkResponsiveDesign(): Promise<void> {
    // 桌面端
    await this.page.setViewportSize({ width: 1920, height: 1080 })
    await this.waitForPageLoad()
    await this.compareScreenshot('desktop')

    // 平板端
    await this.page.setViewportSize({ width: 768, height: 1024 })
    await this.waitForPageLoad()
    await this.compareScreenshot('tablet')

    // 移动端
    await this.page.setViewportSize({ width: 375, height: 667 })
    await this.waitForPageLoad()
    await this.compareScreenshot('mobile')

    // 恢复默认尺寸
    await this.page.setViewportSize({ width: 1280, height: 720 })
  }

  /**
   * 模拟网络延迟
   */
  async simulateSlowNetwork(): Promise<void> {
    await this.page.route('**/*', route => {
      setTimeout(() => route.continue(), 1000) // 1秒延迟
    })
  }

  /**
   * 模拟网络错误
   */
  async simulateNetworkError(urlPattern: string | RegExp): Promise<void> {
    await this.page.route(urlPattern, route => {
      route.abort('failed')
    })
  }

  /**
   * 检查控制台错误
   */
  async checkConsoleErrors(): Promise<void> {
    const errors: string[] = []
    
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text())
      }
    })

    // 等待一段时间收集错误
    await this.page.waitForTimeout(1000)

    if (errors.length > 0) {
      throw new Error(`控制台错误: ${errors.join(', ')}`)
    }
  }

  /**
   * 性能测试
   */
  async measurePerformance(): Promise<{
    loadTime: number
    domContentLoaded: number
    firstContentfulPaint: number
  }> {
    const performanceMetrics = await this.page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      const paint = performance.getEntriesByType('paint')
      
      return {
        loadTime: navigation.loadEventEnd - navigation.loadEventStart,
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0
      }
    })

    return performanceMetrics
  }
}

/**
 * 创建测试辅助工具实例
 */
export function createTestHelpers(page: Page): TestHelpers {
  return new TestHelpers(page)
}
