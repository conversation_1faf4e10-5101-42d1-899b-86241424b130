import type { Context } from 'koa'
import { FileService } from '../services/FileService.js'
import {
  uuidSchema,
  getFilesQuerySchema,
  searchFilesQuerySchema,
  getBreadcrumbQuerySchema
} from '../utils/validation.js'
import type { ApiResponse } from '../types/index.js'

export class FileController {
  // 获取指定父节点下的文件列表
  static async getFiles(ctx: Context) {
    try {
      // 验证学科ID
      const { error: idError, value: subjectId } = uuidSchema.validate(ctx.params.id)
      if (idError) {
        ctx.status = 400
        ctx.body = {
          success: false,
          message: idError.details[0].message,
          error: 'VALIDATION_ERROR'
        }
        return
      }

      // 验证查询参数
      const { error: queryError, value: queryParams } = getFilesQuerySchema.validate(ctx.query)
      if (queryError) {
        ctx.status = 400
        ctx.body = {
          success: false,
          message: queryError.details[0].message,
          error: 'VALIDATION_ERROR'
        }
        return
      }

      // 构建Service请求参数
      const serviceParams = {
        subject_id: subjectId,
        parent_id: queryParams.parent_id || undefined,
        search: queryParams.search || undefined,
        page: queryParams.page,
        limit: queryParams.limit
      }

      const result = await FileService.getFilesByParent(serviceParams)

      ctx.status = 200
      ctx.body = {
        success: true,
        data: {
          files: result.files,
          total: result.total,
          page: result.page,
          limit: result.limit,
          has_more: result.has_more
        },
        message: '获取文件列表成功'
      }
    } catch (error: any) {
      console.error('Get files controller error:', error)

      if (error.message.includes('不存在')) {
        ctx.status = 404
        ctx.body = {
          success: false,
          message: error.message,
          error: 'NOT_FOUND'
        }
      } else if (error.message.includes('不属于')) {
        ctx.status = 403
        ctx.body = {
          success: false,
          message: error.message,
          error: 'FORBIDDEN'
        }
      } else {
        ctx.status = 500
        ctx.body = {
          success: false,
          message: '获取文件列表失败',
          error: 'INTERNAL_ERROR'
        }
      }
    }
  }

  // 获取面包屑导航路径
  static async getBreadcrumb(ctx: Context) {
    try {
      // 验证节点ID（路径参数）
      const nodeId = ctx.params.id
      if (!nodeId) {
        ctx.status = 400
        ctx.body = {
          success: false,
          message: '节点ID不能为空',
          error: 'VALIDATION_ERROR'
        }
        return
      }

      // 验证查询参数
      const { error: queryError, value: queryParams } = getBreadcrumbQuerySchema.validate(ctx.query)
      if (queryError) {
        ctx.status = 400
        ctx.body = {
          success: false,
          message: queryError.details[0].message,
          error: 'VALIDATION_ERROR'
        }
        return
      }

      // 构建Service请求参数
      const serviceParams = {
        subject_id: queryParams.subject_id,
        node_id: nodeId === 'root' ? undefined : nodeId
      }

      const result = await FileService.getBreadcrumb(serviceParams)

      ctx.status = 200
      ctx.body = {
        success: true,
        data: {
          breadcrumb: result.breadcrumb
        },
        message: '面包屑路径获取成功'
      }
    } catch (error: any) {
      console.error('Get breadcrumb controller error:', error)

      if (error.message.includes('不存在')) {
        ctx.status = 404
        ctx.body = {
          success: false,
          message: error.message,
          error: 'NOT_FOUND'
        }
      } else if (error.message.includes('不属于')) {
        ctx.status = 403
        ctx.body = {
          success: false,
          message: error.message,
          error: 'FORBIDDEN'
        }
      } else {
        ctx.status = 500
        ctx.body = {
          success: false,
          message: '获取面包屑导航失败',
          error: 'INTERNAL_ERROR'
        }
      }
    }
  }
  // 搜索文件
  static async searchFiles(ctx: Context) {
    try {
      // 验证学科ID
      const { error: idError, value: subjectId } = uuidSchema.validate(ctx.params.id)
      if (idError) {
        ctx.status = 400
        ctx.body = {
          success: false,
          message: idError.details[0].message,
          error: 'VALIDATION_ERROR'
        }
        return
      }

      // 验证查询参数
      const { error: queryError, value: queryParams } = searchFilesQuerySchema.validate(ctx.query)
      if (queryError) {
        ctx.status = 400
        ctx.body = {
          success: false,
          message: queryError.details[0].message,
          error: 'VALIDATION_ERROR'
        }
        return
      }

      // 构建Service请求参数
      const serviceParams = {
        subject_id: subjectId,
        query: queryParams.query,
        type: queryParams.type,
        page: queryParams.page,
        limit: queryParams.limit
      }

      const result = await FileService.searchFiles(serviceParams)

      ctx.status = 200
      ctx.body = {
        success: true,
        data: {
          files: result.files,
          total: result.total,
          page: result.page,
          limit: result.limit,
          has_more: result.has_more,
          query: result.query
        },
        message: '搜索文件成功'
      }
    } catch (error: any) {
      console.error('Search files controller error:', error)

      if (error.message.includes('不存在')) {
        ctx.status = 404
        ctx.body = {
          success: false,
          message: error.message,
          error: 'NOT_FOUND'
        }
      } else {
        ctx.status = 500
        ctx.body = {
          success: false,
          message: '搜索文件失败',
          error: 'INTERNAL_ERROR'
        }
      }
    }
  }
}
