/**
 * 数据库模块主导出文件
 * 统一导出数据库相关的类、接口和实例
 */

// 导出连接相关
export { DatabaseConnection, dbConnection } from './connection';
export type { DatabaseConfig } from './connection';

// 导出迁移相关
export { MigrationManager, migrationManager } from './migrate';

// 数据库初始化函数
export async function initializeDatabase(): Promise<void> {
  const { dbConnection, migrationManager } = await import('./index');
  
  try {
    // 初始化数据库连接
    await dbConnection.initialize();
    
    // 执行数据库迁移
    await migrationManager.runMigrations();
    
    // 执行健康检查
    const health = dbConnection.healthCheck();
    if (health.status !== 'healthy') {
      throw new Error(`数据库健康检查失败: ${JSON.stringify(health.details)}`);
    }
    
    console.log('🎉 数据库初始化完成');
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    throw error;
  }
}

// 数据库清理函数
export function closeDatabase(): void {
  const { dbConnection } = require('./connection');
  dbConnection.close();
}

// 获取数据库实例的便捷函数
export function getDB() {
  const { dbConnection } = require('./connection');
  return dbConnection.getDatabase();
}

// 执行事务的便捷函数
export function withTransaction<T>(fn: (db: any) => T): T {
  const { dbConnection } = require('./connection');
  return dbConnection.transaction(fn);
}