/**
 * 全局类型定义
 * 统一管理应用中使用的所有TypeScript类型
 */

// 数据库实体类型
export interface Subject {
  id: number;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
  status: number;
  sort_order: number;
}

export interface FileNode {
  id: number;
  subject_id: number;
  parent_id: number | null;
  name: string;
  type: 'file' | 'folder';
  path: string;
  size: number;
  mime_type?: string;
  content?: string;
  created_at: string;
  updated_at: string;
  children?: FileNode[];
}

// API请求/响应类型
export interface CreateSubjectRequest {
  name: string;
  description: string;
  sort_order?: number;
}

export interface UpdateSubjectRequest {
  name?: string;
  description?: string;
  sort_order?: number;
  status?: number;
}

export interface UploadFileRequest {
  subject_id: number;
  parent_id?: number;
  name: string;
  type: 'file' | 'folder';
  content?: string;
}

export interface SearchFilesRequest {
  q: string;
  subject_id?: number;
  type?: 'file' | 'folder';
  limit?: number;
  offset?: number;
}

// 搜索结果类型
export interface SearchResult {
  id: number;
  subject_id: number;
  name: string;
  path: string;
  type: 'file' | 'folder';
  highlight?: string;
  created_at: string;
}

export interface SearchResponse {
  query: string;
  subject_id?: number;
  total: number;
  results: SearchResult[];
}

// 分页类型
export interface PaginationParams {
  page?: number;
  pageSize?: number;
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// 文件上传类型
export interface FileUploadInfo {
  originalName: string;
  filename: string;
  mimetype: string;
  size: number;
  path: string;
}

// 错误类型
export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

// 健康检查类型
export interface HealthInfo {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  version: string;
  environment: string;
  uptime: number;
  memory: NodeJS.MemoryUsage;
  database: string;
}

// API信息类型
export interface ApiInfo {
  name: string;
  version: string;
  description: string;
  environment: string;
  endpoints: Record<string, string>;
  documentation: string;
  timestamp: string;
}

// 数据库查询选项
export interface QueryOptions {
  limit?: number;
  offset?: number;
  orderBy?: string;
  orderDirection?: 'ASC' | 'DESC';
  where?: Record<string, any>;
}

// 文件树节点类型
export interface FileTreeNode extends FileNode {
  children: FileTreeNode[];
  level: number;
  expanded?: boolean;
}

// 统计信息类型
export interface SubjectStats {
  id: number;
  name: string;
  totalFiles: number;
  totalFolders: number;
  totalSize: number;
  lastUpdated: string;
}

export interface SystemStats {
  totalSubjects: number;
  totalFiles: number;
  totalFolders: number;
  totalSize: number;
  databaseSize: number;
  uptime: number;
}

// 导出所有类型
export * from './koa-extensions';