# Sprint-03: P0-基础文件上传功能 - 详细执行清单

## 文档信息
- **Sprint编号**: Sprint-03
- **切片名称**: P0-基础文件上传功能
- **任务ID**: `1dd94e9a-7177-46cb-8986-5c7295b2d96e`
- **创建时间**: 2025-01-27
- **规划人员**: <PERSON> (架构师) + <PERSON> (团队领袖)
- **执行人员**: Alex (工程师)
- **基于文档**: PRD_Final-Review-Platform_v1.0.md, 任务规划_期末复习平台_垂直切片.md, Overall_Architecture_期末复习平台.md
- **版权归属**: 随影

## 1. Sprint概述

### 1.1 用户价值
管理员能够上传包含Markdown和图片的文件夹，系统自动解析目录结构并存储，为平台提供内容管理的核心功能。

### 1.2 技术范围
- **文件上传层**: 支持文件夹拖拽上传，文件类型过滤，大小限制控制
- **后端处理层**: POST /api/admin/subjects/{id}/upload接口，文件流处理，目录结构解析
- **数据存储层**: file_nodes表层级关系建立，文件系统存储管理
- **前端界面层**: 文件上传组件，进度显示，文件预览和管理界面
- **集成测试层**: 完整上传流程验证，性能测试，错误处理测试

### 1.3 验收标准
管理员能够选择文件夹进行拖拽上传，系统正确过滤文件类型（支持.md, .jpg, .png, .gif, .svg, .webp），显示实时上传进度，上传完成后能在前台正确浏览文件内容和目录结构。单次上传支持最大500MB，单文件最大50MB。

### 1.4 总体工期
**预估工期**: 6-8个工作日
**里程碑**: 完成文件上传核心功能，为平台内容管理奠定基础

### 1.5 依赖关系
- **前置依赖**: 任务2（P0-管理员学科管理）必须完成
- **数据库依赖**: 基于现有的subjects表和file_nodes表结构
- **技术依赖**: 现有的管理后台框架和API基础设施

## 2. 子任务详细分解

### 任务3.1: 文件上传后端API开发

#### 基本信息
- **任务编号**: 3.1
- **任务名称**: 文件上传后端API开发
- **负责人**: Alex (工程师)
- **预估工期**: 2个工作日
- **优先级**: P0 (最高优先级)
- **依赖关系**: 依赖任务2（管理员学科管理）

#### 技术实现要求

**3.1.1 文件上传中间件配置**
```javascript
// 文件上传配置 (src/middleware/upload.js)
const multer = require('multer')
const path = require('path')
const fs = require('fs').promises

// 支持的文件类型
const ALLOWED_FILE_TYPES = {
  markdown: ['.md', '.markdown'],
  images: ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp'],
  get all() {
    return [...this.markdown, ...this.images]
  }
}

// 文件大小限制
const FILE_SIZE_LIMITS = {
  maxFileSize: 50 * 1024 * 1024,      // 单文件最大50MB
  maxTotalSize: 500 * 1024 * 1024,    // 总大小最大500MB
  maxFiles: 1000                       // 最大文件数量
}

// 临时存储配置
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const tempDir = path.join(process.cwd(), 'storage', 'temp', req.uploadId)
    await fs.mkdir(tempDir, { recursive: true })
    cb(null, tempDir)
  },
  filename: (req, file, cb) => {
    // 保持原始文件名，处理中文和特殊字符
    const originalName = Buffer.from(file.originalname, 'latin1').toString('utf8')
    cb(null, originalName)
  }
})

// 文件过滤器
const fileFilter = (req, file, cb) => {
  const originalName = Buffer.from(file.originalname, 'latin1').toString('utf8')
  const ext = path.extname(originalName).toLowerCase()
  
  if (ALLOWED_FILE_TYPES.all.includes(ext)) {
    cb(null, true)
  } else {
    cb(new Error(`不支持的文件类型: ${ext}`), false)
  }
}

// 创建上传中间件
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: FILE_SIZE_LIMITS.maxFileSize,
    files: FILE_SIZE_LIMITS.maxFiles
  }
})

// 上传预处理中间件
const uploadPreprocess = async (ctx, next) => {
  // 生成唯一的上传ID
  ctx.uploadId = `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  ctx.request.uploadId = ctx.uploadId
  
  // 初始化上传状态
  ctx.uploadStatus = {
    id: ctx.uploadId,
    startTime: Date.now(),
    totalFiles: 0,
    processedFiles: 0,
    totalSize: 0,
    processedSize: 0,
    errors: []
  }
  
  await next()
}

module.exports = {
  upload,
  uploadPreprocess,
  ALLOWED_FILE_TYPES,
  FILE_SIZE_LIMITS
}
```

**3.1.2 文件上传API接口**
```javascript
// 文件上传控制器 (src/controllers/admin/FileUploadController.js)
const FileUploadService = require('../../services/admin/FileUploadService')
const { successResponse, errorResponse } = require('../../utils/response')

class FileUploadController {
  // 文件上传接口
  static async uploadFiles(ctx) {
    try {
      const { id: subjectId } = ctx.params
      const files = ctx.request.files
      const uploadId = ctx.uploadId
      
      if (!files || files.length === 0) {
        ctx.body = errorResponse('NO_FILES', '没有选择文件')
        ctx.status = 400
        return
      }
      
      // 验证学科是否存在
      const subject = await FileUploadService.getSubjectById(subjectId)
      if (!subject) {
        ctx.body = errorResponse('SUBJECT_NOT_FOUND', '学科不存在')
        ctx.status = 404
        return
      }
      
      // 处理文件上传
      const result = await FileUploadService.processFileUpload({
        subjectId,
        files,
        uploadId,
        onProgress: (progress) => {
          // 通过WebSocket发送进度更新（可选）
          ctx.app.emit('upload-progress', { uploadId, progress })
        }
      })
      
      ctx.body = successResponse(result, {
        message: '文件上传成功',
        uploadId,
        timestamp: new Date().toISOString()
      })
      ctx.status = 201
      
    } catch (error) {
      console.error('文件上传失败:', error)
      
      // 清理临时文件
      if (ctx.uploadId) {
        await FileUploadService.cleanupTempFiles(ctx.uploadId)
      }
      
      if (error.code === 'LIMIT_FILE_SIZE') {
        ctx.body = errorResponse('FILE_TOO_LARGE', '文件大小超出限制')
        ctx.status = 413
      } else if (error.code === 'LIMIT_FILE_COUNT') {
        ctx.body = errorResponse('TOO_MANY_FILES', '文件数量超出限制')
        ctx.status = 413
      } else {
        ctx.body = errorResponse('UPLOAD_FAILED', '文件上传失败')
        ctx.status = 500
      }
    }
  }
  
  // 获取上传进度
  static async getUploadProgress(ctx) {
    try {
      const { uploadId } = ctx.params
      const progress = await FileUploadService.getUploadProgress(uploadId)
      
      if (!progress) {
        ctx.body = errorResponse('UPLOAD_NOT_FOUND', '上传任务不存在')
        ctx.status = 404
        return
      }
      
      ctx.body = successResponse(progress)
    } catch (error) {
      console.error('获取上传进度失败:', error)
      ctx.body = errorResponse('INTERNAL_ERROR', '获取上传进度失败')
      ctx.status = 500
    }
  }
  
  // 取消上传
  static async cancelUpload(ctx) {
    try {
      const { uploadId } = ctx.params
      await FileUploadService.cancelUpload(uploadId)
      
      ctx.body = successResponse({ cancelled: true }, {
        message: '上传已取消',
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error('取消上传失败:', error)
      ctx.body = errorResponse('INTERNAL_ERROR', '取消上传失败')
      ctx.status = 500
    }
  }
}

module.exports = FileUploadController
```

**3.1.3 文件上传路由配置**
```javascript
// 文件上传路由 (src/routes/admin.js 扩展)
const FileUploadController = require('../controllers/admin/FileUploadController')
const { upload, uploadPreprocess } = require('../middleware/upload')

// 文件上传相关路由
router.post('/subjects/:id/upload', 
  uploadPreprocess,
  upload.array('files', 1000),
  FileUploadController.uploadFiles
)

router.get('/upload/:uploadId/progress', FileUploadController.getUploadProgress)
router.delete('/upload/:uploadId', FileUploadController.cancelUpload)
```

#### 交付物清单
1. **上传中间件**: `src/middleware/upload.js`
2. **上传控制器**: `src/controllers/admin/FileUploadController.js`
3. **路由配置**: `src/routes/admin.js`扩展
4. **配置文件**: 文件类型和大小限制配置
5. **错误处理**: 统一的上传错误处理机制
6. **API文档**: `docs/api/file-upload-api.md`

#### 验收标准
- [ ] 文件上传API接口功能完整，支持多文件上传
- [ ] 文件类型过滤正确，只允许指定格式文件
- [ ] 文件大小限制有效，超出限制时正确拒绝
- [ ] 临时文件处理机制完善，上传失败时自动清理
- [ ] 错误处理完善，所有异常情况都有适当的错误响应
- [ ] 上传进度跟踪功能正常
- [ ] API响应时间符合要求（文件处理≤10秒）
- [ ] 内存使用稳定，支持大文件上传

#### 技术规范
- 使用multer中间件处理文件上传
- 支持流式文件处理，减少内存占用
- 实现完善的错误处理和回滚机制
- 使用临时目录暂存上传文件
- 统一的文件类型和大小验证
- 清晰的日志记录便于问题排查

---

### 任务3.2: 文件存储和目录结构处理

#### 基本信息
- **任务编号**: 3.2
- **任务名称**: 文件存储和目录结构处理
- **负责人**: Alex (工程师)
- **预估工期**: 1.5个工作日
- **优先级**: P0 (最高优先级)
- **依赖关系**: 依赖任务3.1（文件上传后端API开发）

#### 技术实现要求

**3.2.1 文件上传服务核心逻辑**
```javascript
// 文件上传服务 (src/services/admin/FileUploadService.js)
const fs = require('fs').promises
const path = require('path')
const Database = require('better-sqlite3')
const { v4: uuidv4 } = require('uuid')

class FileUploadService {
  constructor() {
    this.db = new Database(process.env.DB_PATH || './data/database.db')
    this.uploadProgress = new Map() // 存储上传进度
  }

  // 处理文件上传主流程
  async processFileUpload({ subjectId, files, uploadId, onProgress }) {
    const transaction = this.db.transaction(async () => {
      try {
        // 1. 初始化上传进度
        const progress = {
          uploadId,
          subjectId,
          totalFiles: files.length,
          processedFiles: 0,
          totalSize: files.reduce((sum, file) => sum + file.size, 0),
          processedSize: 0,
          status: 'processing',
          startTime: Date.now(),
          files: [],
          errors: []
        }
        this.uploadProgress.set(uploadId, progress)

        // 2. 创建学科存储目录
        const subjectStoragePath = path.join(
          process.cwd(), 'storage', 'subjects', subjectId.toString()
        )
        await fs.mkdir(subjectStoragePath, { recursive: true })

        // 3. 解析文件目录结构
        const fileTree = await this.parseFileStructure(files, uploadId)

        // 4. 处理每个文件
        const processedFiles = []
        for (const fileInfo of fileTree) {
          try {
            const result = await this.processFile(fileInfo, subjectId, subjectStoragePath)
            processedFiles.push(result)

            // 更新进度
            progress.processedFiles++
            progress.processedSize += fileInfo.size
            progress.files.push(result)

            if (onProgress) {
              onProgress({
                ...progress,
                percentage: Math.round((progress.processedSize / progress.totalSize) * 100)
              })
            }

          } catch (error) {
            console.error(`处理文件失败: ${fileInfo.originalPath}`, error)
            progress.errors.push({
              file: fileInfo.originalPath,
              error: error.message
            })
          }
        }

        // 5. 更新学科统计信息
        await this.updateSubjectStats(subjectId)

        // 6. 清理临时文件
        await this.cleanupTempFiles(uploadId)

        progress.status = 'completed'
        progress.endTime = Date.now()

        return {
          uploadId,
          totalFiles: processedFiles.length,
          errors: progress.errors,
          duration: progress.endTime - progress.startTime,
          files: processedFiles
        }

      } catch (error) {
        // 回滚操作
        await this.rollbackUpload(uploadId, subjectId)
        throw error
      }
    })

    return transaction()
  }

  // 解析文件目录结构
  async parseFileStructure(files, uploadId) {
    const fileTree = []
    const pathMap = new Map() // 用于建立路径关系

    // 按路径深度排序，确保父目录先处理
    files.sort((a, b) => {
      const aDepth = a.originalname.split('/').length
      const bDepth = b.originalname.split('/').length
      return aDepth - bDepth
    })

    for (const file of files) {
      const originalPath = Buffer.from(file.originalname, 'latin1').toString('utf8')
      const pathParts = originalPath.split('/').filter(part => part.length > 0)

      // 构建文件信息
      const fileInfo = {
        id: uuidv4(),
        originalPath,
        filename: pathParts[pathParts.length - 1],
        relativePath: pathParts.slice(0, -1).join('/'),
        fullPath: file.path,
        size: file.size,
        mimetype: file.mimetype,
        type: this.getFileType(pathParts[pathParts.length - 1]),
        depth: pathParts.length - 1,
        parentPath: pathParts.slice(0, -1).join('/'),
        uploadId
      }

      fileTree.push(fileInfo)
      pathMap.set(originalPath, fileInfo)
    }

    return fileTree
  }

  // 处理单个文件
  async processFile(fileInfo, subjectId, subjectStoragePath) {
    // 1. 确定最终存储路径
    const finalPath = path.join(subjectStoragePath, fileInfo.relativePath, fileInfo.filename)
    const finalDir = path.dirname(finalPath)

    // 2. 创建目录
    await fs.mkdir(finalDir, { recursive: true })

    // 3. 移动文件到最终位置
    await fs.rename(fileInfo.fullPath, finalPath)

    // 4. 计算相对路径（用于数据库存储）
    const relativePath = path.relative(
      path.join(process.cwd(), 'storage'),
      finalPath
    ).replace(/\\/g, '/')

    // 5. 插入数据库记录
    const fileRecord = {
      id: fileInfo.id,
      subject_id: subjectId,
      name: fileInfo.filename,
      path: relativePath,
      relative_path: fileInfo.relativePath || '',
      size: fileInfo.size,
      type: fileInfo.type,
      mime_type: fileInfo.mimetype,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    // 插入file_nodes表
    const insertStmt = this.db.prepare(`
      INSERT INTO file_nodes (
        id, subject_id, name, path, relative_path, size, type, mime_type, created_at, updated_at
      ) VALUES (
        @id, @subject_id, @name, @path, @relative_path, @size, @type, @mime_type, @created_at, @updated_at
      )
    `)

    insertStmt.run(fileRecord)

    return {
      id: fileInfo.id,
      name: fileInfo.filename,
      path: relativePath,
      relativePath: fileInfo.relativePath,
      size: fileInfo.size,
      type: fileInfo.type,
      status: 'success'
    }
  }

  // 获取文件类型
  getFileType(filename) {
    const ext = path.extname(filename).toLowerCase()

    if (['.md', '.markdown'].includes(ext)) {
      return 'markdown'
    } else if (['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp'].includes(ext)) {
      return 'image'
    }

    return 'unknown'
  }

  // 更新学科统计信息
  async updateSubjectStats(subjectId) {
    const statsStmt = this.db.prepare(`
      UPDATE subjects
      SET
        file_count = (
          SELECT COUNT(*) FROM file_nodes WHERE subject_id = ?
        ),
        total_size = (
          SELECT COALESCE(SUM(size), 0) FROM file_nodes WHERE subject_id = ?
        ),
        updated_at = ?
      WHERE id = ?
    `)

    const now = new Date().toISOString()
    statsStmt.run(subjectId, subjectId, now, subjectId)
  }

  // 清理临时文件
  async cleanupTempFiles(uploadId) {
    try {
      const tempDir = path.join(process.cwd(), 'storage', 'temp', uploadId)
      await fs.rm(tempDir, { recursive: true, force: true })
    } catch (error) {
      console.error('清理临时文件失败:', error)
    }
  }

  // 回滚上传操作
  async rollbackUpload(uploadId, subjectId) {
    try {
      // 1. 删除数据库记录
      const deleteStmt = this.db.prepare(`
        DELETE FROM file_nodes
        WHERE subject_id = ? AND created_at > datetime('now', '-1 hour')
      `)
      deleteStmt.run(subjectId)

      // 2. 清理临时文件
      await this.cleanupTempFiles(uploadId)

      // 3. 更新上传状态
      const progress = this.uploadProgress.get(uploadId)
      if (progress) {
        progress.status = 'failed'
        progress.endTime = Date.now()
      }

    } catch (error) {
      console.error('回滚上传操作失败:', error)
    }
  }

  // 获取上传进度
  async getUploadProgress(uploadId) {
    return this.uploadProgress.get(uploadId) || null
  }

  // 取消上传
  async cancelUpload(uploadId) {
    const progress = this.uploadProgress.get(uploadId)
    if (progress) {
      progress.status = 'cancelled'
      progress.endTime = Date.now()

      // 清理临时文件
      await this.cleanupTempFiles(uploadId)

      // 如果已经开始处理，需要回滚
      if (progress.processedFiles > 0) {
        await this.rollbackUpload(uploadId, progress.subjectId)
      }
    }
  }

  // 获取学科信息
  async getSubjectById(subjectId) {
    const stmt = this.db.prepare('SELECT * FROM subjects WHERE id = ?')
    return stmt.get(subjectId)
  }
}

module.exports = new FileUploadService()
```

**3.2.2 数据库表结构扩展**
```sql
-- 扩展file_nodes表结构 (database/migrations/003_extend_file_nodes.sql)
-- 添加文件上传相关字段

ALTER TABLE file_nodes ADD COLUMN relative_path TEXT DEFAULT '';
ALTER TABLE file_nodes ADD COLUMN mime_type TEXT;
ALTER TABLE file_nodes ADD COLUMN upload_id TEXT;

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_file_nodes_subject_id ON file_nodes(subject_id);
CREATE INDEX IF NOT EXISTS idx_file_nodes_relative_path ON file_nodes(relative_path);
CREATE INDEX IF NOT EXISTS idx_file_nodes_type ON file_nodes(type);
CREATE INDEX IF NOT EXISTS idx_file_nodes_upload_id ON file_nodes(upload_id);

-- 创建文件上传日志表
CREATE TABLE IF NOT EXISTS upload_logs (
  id TEXT PRIMARY KEY,
  subject_id INTEGER NOT NULL,
  upload_id TEXT NOT NULL,
  total_files INTEGER DEFAULT 0,
  processed_files INTEGER DEFAULT 0,
  total_size INTEGER DEFAULT 0,
  processed_size INTEGER DEFAULT 0,
  status TEXT DEFAULT 'pending', -- pending, processing, completed, failed, cancelled
  error_message TEXT,
  started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  completed_at DATETIME,
  FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS idx_upload_logs_subject_id ON upload_logs(subject_id);
CREATE INDEX IF NOT EXISTS idx_upload_logs_upload_id ON upload_logs(upload_id);
CREATE INDEX IF NOT EXISTS idx_upload_logs_status ON upload_logs(status);
```

#### 交付物清单
1. **文件上传服务**: `src/services/admin/FileUploadService.js`
2. **数据库迁移**: `database/migrations/003_extend_file_nodes.sql`
3. **存储目录结构**: `storage/subjects/{id}/` 目录规范
4. **文件处理工具**: 文件类型识别和路径处理工具
5. **错误回滚机制**: 完整的事务回滚和清理机制
6. **技术文档**: `docs/storage/file-storage-design.md`

#### 验收标准
- [ ] 文件目录结构正确解析和保存
- [ ] 数据库中正确记录文件层级关系
- [ ] 文件系统存储路径规范，支持中文文件名
- [ ] 上传失败时完整回滚，不留垃圾数据
- [ ] 学科统计信息自动更新准确
- [ ] 支持大量文件的批量处理（1000+文件）
- [ ] 内存使用稳定，无内存泄漏
- [ ] 文件完整性验证通过

#### 技术规范
- 使用数据库事务确保数据一致性
- 文件路径使用相对路径存储
- 支持Unicode文件名和路径
- 实现完整的错误处理和回滚机制
- 优化数据库查询性能
- 合理的目录结构设计

---

### 任务3.3: 前端文件上传组件开发

#### 基本信息
- **任务编号**: 3.3
- **任务名称**: 前端文件上传组件开发
- **负责人**: Alex (工程师)
- **预估工期**: 2个工作日
- **优先级**: P0 (最高优先级)
- **依赖关系**: 依赖任务3.2（文件存储和目录结构处理）

#### 技术实现要求

**3.3.1 文件上传组合函数**
```typescript
// 文件上传组合函数 (src/composables/useFileUpload.ts)
import { ref, reactive, computed } from 'vue'
import { message } from 'ant-design-vue'
import { useAdminApi } from './useAdminApi'

export interface UploadFile {
  id: string
  name: string
  size: number
  type: string
  relativePath: string
  file: File
  status: 'pending' | 'uploading' | 'success' | 'error'
  progress: number
  error?: string
}

export interface UploadProgress {
  uploadId: string
  totalFiles: number
  processedFiles: number
  totalSize: number
  processedSize: number
  percentage: number
  status: 'pending' | 'uploading' | 'success' | 'error' | 'cancelled'
  errors: Array<{ file: string; error: string }>
}

export function useFileUpload() {
  const { adminApi } = useAdminApi()

  // 上传状态
  const uploading = ref(false)
  const uploadProgress = reactive<UploadProgress>({
    uploadId: '',
    totalFiles: 0,
    processedFiles: 0,
    totalSize: 0,
    processedSize: 0,
    percentage: 0,
    status: 'pending',
    errors: []
  })

  // 文件列表
  const fileList = ref<UploadFile[]>([])
  const selectedFiles = ref<File[]>([])

  // 支持的文件类型
  const ALLOWED_TYPES = {
    markdown: ['.md', '.markdown'],
    images: ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp']
  }

  const ALLOWED_EXTENSIONS = [
    ...ALLOWED_TYPES.markdown,
    ...ALLOWED_TYPES.images
  ]

  // 文件大小限制
  const FILE_SIZE_LIMITS = {
    maxFileSize: 50 * 1024 * 1024,      // 50MB
    maxTotalSize: 500 * 1024 * 1024,    // 500MB
    maxFiles: 1000
  }

  // 计算属性
  const canUpload = computed(() => {
    return fileList.value.length > 0 &&
           !uploading.value &&
           fileList.value.every(f => f.status !== 'error')
  })

  const totalSize = computed(() => {
    return fileList.value.reduce((sum, file) => sum + file.size, 0)
  })

  const hasErrors = computed(() => {
    return fileList.value.some(f => f.status === 'error') || uploadProgress.errors.length > 0
  })

  // 文件选择处理
  const handleFileSelect = (files: FileList | File[]) => {
    const newFiles: UploadFile[] = []
    const errors: string[] = []

    Array.from(files).forEach((file, index) => {
      // 检查文件类型
      const ext = getFileExtension(file.name)
      if (!ALLOWED_EXTENSIONS.includes(ext)) {
        errors.push(`文件 "${file.name}" 类型不支持`)
        return
      }

      // 检查文件大小
      if (file.size > FILE_SIZE_LIMITS.maxFileSize) {
        errors.push(`文件 "${file.name}" 大小超出限制 (最大50MB)`)
        return
      }

      // 获取相对路径（用于文件夹上传）
      const relativePath = getRelativePath(file)

      const uploadFile: UploadFile = {
        id: `file_${Date.now()}_${index}`,
        name: file.name,
        size: file.size,
        type: getFileType(file.name),
        relativePath,
        file,
        status: 'pending',
        progress: 0
      }

      newFiles.push(uploadFile)
    })

    // 检查总文件数量
    if (fileList.value.length + newFiles.length > FILE_SIZE_LIMITS.maxFiles) {
      errors.push(`文件数量超出限制 (最大${FILE_SIZE_LIMITS.maxFiles}个)`)
      return
    }

    // 检查总大小
    const newTotalSize = totalSize.value + newFiles.reduce((sum, f) => sum + f.size, 0)
    if (newTotalSize > FILE_SIZE_LIMITS.maxTotalSize) {
      errors.push(`总文件大小超出限制 (最大500MB)`)
      return
    }

    if (errors.length > 0) {
      errors.forEach(error => message.error(error))
      return
    }

    fileList.value.push(...newFiles)
    message.success(`已选择 ${newFiles.length} 个文件`)
  }

  // 文件夹选择处理
  const handleDirectorySelect = (event: Event) => {
    const input = event.target as HTMLInputElement
    if (input.files) {
      handleFileSelect(input.files)
    }
  }

  // 拖拽上传处理
  const handleDrop = (event: DragEvent) => {
    event.preventDefault()

    const items = event.dataTransfer?.items
    if (!items) return

    const files: File[] = []

    Array.from(items).forEach(item => {
      if (item.kind === 'file') {
        const file = item.getAsFile()
        if (file) files.push(file)
      }
    })

    if (files.length > 0) {
      handleFileSelect(files)
    }
  }

  // 开始上传
  const startUpload = async (subjectId: number) => {
    if (!canUpload.value) return

    uploading.value = true

    try {
      // 准备FormData
      const formData = new FormData()
      fileList.value.forEach(uploadFile => {
        formData.append('files', uploadFile.file)
      })

      // 重置进度
      Object.assign(uploadProgress, {
        uploadId: '',
        totalFiles: fileList.value.length,
        processedFiles: 0,
        totalSize: totalSize.value,
        processedSize: 0,
        percentage: 0,
        status: 'uploading',
        errors: []
      })

      // 发起上传请求
      const response = await adminApi.post(`/subjects/${subjectId}/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const percentage = Math.round((progressEvent.loaded / progressEvent.total) * 100)
            uploadProgress.percentage = percentage
            uploadProgress.processedSize = progressEvent.loaded
          }
        }
      })

      // 处理上传结果
      const result = response.data.data
      uploadProgress.uploadId = result.uploadId
      uploadProgress.status = 'success'
      uploadProgress.processedFiles = result.totalFiles

      // 更新文件状态
      fileList.value.forEach(file => {
        file.status = 'success'
        file.progress = 100
      })

      message.success(`文件上传成功！共上传 ${result.totalFiles} 个文件`)

      return result

    } catch (error: any) {
      console.error('文件上传失败:', error)

      uploadProgress.status = 'error'

      // 更新文件状态
      fileList.value.forEach(file => {
        file.status = 'error'
        file.error = error.response?.data?.error?.message || '上传失败'
      })

      message.error('文件上传失败')
      throw error

    } finally {
      uploading.value = false
    }
  }

  // 取消上传
  const cancelUpload = async () => {
    if (uploadProgress.uploadId) {
      try {
        await adminApi.delete(`/upload/${uploadProgress.uploadId}`)
        uploadProgress.status = 'cancelled'
        message.info('上传已取消')
      } catch (error) {
        console.error('取消上传失败:', error)
      }
    }

    uploading.value = false
  }

  // 清除文件列表
  const clearFiles = () => {
    fileList.value = []
    selectedFiles.value = []
    Object.assign(uploadProgress, {
      uploadId: '',
      totalFiles: 0,
      processedFiles: 0,
      totalSize: 0,
      processedSize: 0,
      percentage: 0,
      status: 'pending',
      errors: []
    })
  }

  // 移除单个文件
  const removeFile = (fileId: string) => {
    const index = fileList.value.findIndex(f => f.id === fileId)
    if (index > -1) {
      fileList.value.splice(index, 1)
    }
  }

  // 工具函数
  const getFileExtension = (filename: string): string => {
    return filename.toLowerCase().substring(filename.lastIndexOf('.'))
  }

  const getFileType = (filename: string): string => {
    const ext = getFileExtension(filename)
    if (ALLOWED_TYPES.markdown.includes(ext)) return 'markdown'
    if (ALLOWED_TYPES.images.includes(ext)) return 'image'
    return 'unknown'
  }

  const getRelativePath = (file: File): string => {
    // 从webkitRelativePath获取相对路径
    const webkitPath = (file as any).webkitRelativePath
    if (webkitPath) {
      const pathParts = webkitPath.split('/')
      return pathParts.slice(0, -1).join('/')
    }
    return ''
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return {
    // 状态
    uploading,
    uploadProgress,
    fileList,
    selectedFiles,

    // 计算属性
    canUpload,
    totalSize,
    hasErrors,

    // 方法
    handleFileSelect,
    handleDirectorySelect,
    handleDrop,
    startUpload,
    cancelUpload,
    clearFiles,
    removeFile,
    formatFileSize,

    // 常量
    ALLOWED_EXTENSIONS,
    FILE_SIZE_LIMITS
  }
}
```

**3.3.2 文件上传组件**
```vue
<!-- 文件上传组件 (src/components/admin/FileUpload.vue) -->
<template>
  <div class="file-upload">
    <!-- 上传区域 -->
    <div
      class="upload-area"
      :class="{
        'drag-over': isDragOver,
        'has-files': fileList.length > 0,
        'uploading': uploading
      }"
      @drop="handleDrop"
      @dragover.prevent="isDragOver = true"
      @dragleave.prevent="isDragOver = false"
      @dragenter.prevent
    >
      <div class="upload-content">
        <div class="upload-icon">
          <cloud-upload-outlined v-if="!uploading" />
          <loading-outlined v-else spin />
        </div>

        <div class="upload-text">
          <h3 v-if="!uploading">拖拽文件夹到此处上传</h3>
          <h3 v-else>正在上传文件...</h3>
          <p v-if="!uploading">
            支持 .md, .jpg, .png, .gif, .svg, .webp 格式<br>
            单文件最大 50MB，总大小最大 500MB
          </p>
          <div v-else class="upload-progress-info">
            <p>{{ uploadProgress.processedFiles }} / {{ uploadProgress.totalFiles }} 个文件</p>
            <p>{{ formatFileSize(uploadProgress.processedSize) }} / {{ formatFileSize(uploadProgress.totalSize) }}</p>
          </div>
        </div>

        <div class="upload-actions" v-if="!uploading">
          <a-button type="primary" @click="selectFiles">
            <template #icon>
              <folder-open-outlined />
            </template>
            选择文件夹
          </a-button>

          <a-button @click="selectSingleFiles">
            <template #icon>
              <file-outlined />
            </template>
            选择文件
          </a-button>
        </div>

        <div class="upload-actions" v-else>
          <a-button danger @click="cancelUpload">
            <template #icon>
              <stop-outlined />
            </template>
            取消上传
          </a-button>
        </div>
      </div>

      <!-- 隐藏的文件输入 -->
      <input
        ref="directoryInput"
        type="file"
        webkitdirectory
        multiple
        style="display: none"
        @change="handleDirectorySelect"
      />

      <input
        ref="fileInput"
        type="file"
        multiple
        :accept="acceptedTypes"
        style="display: none"
        @change="handleFileSelect"
      />
    </div>

    <!-- 上传进度 -->
    <div class="upload-progress" v-if="uploading || uploadProgress.status === 'success'">
      <a-progress
        :percent="uploadProgress.percentage"
        :status="uploadProgress.status === 'error' ? 'exception' : 'normal'"
        :stroke-color="uploadProgress.status === 'success' ? '#52c41a' : '#1890ff'"
      />

      <div class="progress-details">
        <span>{{ uploadProgress.processedFiles }} / {{ uploadProgress.totalFiles }} 个文件</span>
        <span>{{ formatFileSize(uploadProgress.processedSize) }} / {{ formatFileSize(uploadProgress.totalSize) }}</span>
      </div>
    </div>

    <!-- 文件列表 -->
    <div class="file-list" v-if="fileList.length > 0">
      <div class="file-list-header">
        <h4>已选择文件 ({{ fileList.length }})</h4>
        <div class="file-list-actions">
          <a-button size="small" @click="clearFiles" :disabled="uploading">
            清空列表
          </a-button>

          <a-button
            type="primary"
            size="small"
            @click="startUpload"
            :disabled="!canUpload"
            :loading="uploading"
          >
            <template #icon>
              <upload-outlined />
            </template>
            开始上传
          </a-button>
        </div>
      </div>

      <div class="file-list-content">
        <a-table
          :columns="fileColumns"
          :data-source="fileList"
          :pagination="false"
          size="small"
          row-key="id"
          :scroll="{ y: 300 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div class="file-info">
                <file-markdown-outlined v-if="record.type === 'markdown'" class="file-icon markdown" />
                <file-image-outlined v-else-if="record.type === 'image'" class="file-icon image" />
                <file-outlined v-else class="file-icon" />

                <div class="file-details">
                  <div class="file-name">{{ record.name }}</div>
                  <div class="file-path" v-if="record.relativePath">{{ record.relativePath }}</div>
                </div>
              </div>
            </template>

            <template v-else-if="column.key === 'size'">
              {{ formatFileSize(record.size) }}
            </template>

            <template v-else-if="column.key === 'status'">
              <a-tag v-if="record.status === 'pending'" color="default">待上传</a-tag>
              <a-tag v-else-if="record.status === 'uploading'" color="blue">
                <loading-outlined spin />
                上传中
              </a-tag>
              <a-tag v-else-if="record.status === 'success'" color="green">
                <check-circle-outlined />
                成功
              </a-tag>
              <a-tag v-else-if="record.status === 'error'" color="red">
                <close-circle-outlined />
                失败
              </a-tag>
            </template>

            <template v-else-if="column.key === 'actions'">
              <a-button
                type="link"
                size="small"
                danger
                @click="removeFile(record.id)"
                :disabled="uploading"
              >
                删除
              </a-button>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 错误信息 -->
    <div class="upload-errors" v-if="hasErrors">
      <a-alert
        message="上传过程中发现错误"
        type="error"
        show-icon
        closable
      >
        <template #description>
          <ul>
            <li v-for="error in uploadProgress.errors" :key="error.file">
              {{ error.file }}: {{ error.error }}
            </li>
          </ul>
        </template>
      </a-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  CloudUploadOutlined,
  FolderOpenOutlined,
  FileOutlined,
  UploadOutlined,
  LoadingOutlined,
  StopOutlined,
  FileMarkdownOutlined,
  FileImageOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons-vue'
import { useFileUpload } from '@/composables/useFileUpload'

interface Props {
  subjectId: number
}

const props = defineProps<Props>()

const emit = defineEmits<{
  uploadSuccess: [result: any]
  uploadError: [error: any]
}>()

// 使用文件上传组合函数
const {
  uploading,
  uploadProgress,
  fileList,
  canUpload,
  hasErrors,
  handleDrop: originalHandleDrop,
  startUpload: originalStartUpload,
  cancelUpload,
  clearFiles,
  removeFile,
  formatFileSize,
  ALLOWED_EXTENSIONS
} = useFileUpload()

// 本地状态
const isDragOver = ref(false)
const directoryInput = ref<HTMLInputElement>()
const fileInput = ref<HTMLInputElement>()

// 计算属性
const acceptedTypes = computed(() => {
  return ALLOWED_EXTENSIONS.join(',')
})

// 表格列配置
const fileColumns = [
  {
    title: '文件名',
    key: 'name',
    width: '40%'
  },
  {
    title: '大小',
    key: 'size',
    width: '15%'
  },
  {
    title: '状态',
    key: 'status',
    width: '20%'
  },
  {
    title: '操作',
    key: 'actions',
    width: '15%'
  }
]

// 方法
const selectFiles = () => {
  directoryInput.value?.click()
}

const selectSingleFiles = () => {
  fileInput.value?.click()
}

const handleDirectorySelect = (event: Event) => {
  const input = event.target as HTMLInputElement
  if (input.files) {
    handleFileSelect(Array.from(input.files))
  }
}

const handleFileSelect = (files: File[]) => {
  // 使用组合函数的文件选择处理
  const { handleFileSelect: composableHandleFileSelect } = useFileUpload()
  composableHandleFileSelect(files)
}

const handleDrop = (event: DragEvent) => {
  isDragOver.value = false
  originalHandleDrop(event)
}

const startUpload = async () => {
  try {
    const result = await originalStartUpload(props.subjectId)
    emit('uploadSuccess', result)
  } catch (error) {
    emit('uploadError', error)
  }
}
</script>

<style scoped>
.file-upload {
  width: 100%;
}

.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  background: #fafafa;
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-area:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.upload-area.drag-over {
  border-color: #1890ff;
  background: #e6f7ff;
  transform: scale(1.02);
}

.upload-area.has-files {
  border-color: #52c41a;
  background: #f6ffed;
}

.upload-area.uploading {
  border-color: #1890ff;
  background: #f0f8ff;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.upload-icon {
  font-size: 48px;
  color: #1890ff;
}

.upload-text h3 {
  margin: 0;
  color: #262626;
  font-size: 18px;
}

.upload-text p {
  margin: 8px 0 0 0;
  color: #8c8c8c;
  font-size: 14px;
  line-height: 1.5;
}

.upload-progress-info {
  color: #1890ff;
  font-weight: 500;
}

.upload-actions {
  display: flex;
  gap: 12px;
}

.upload-progress {
  margin-top: 16px;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 12px;
  color: #8c8c8c;
}

.file-list {
  margin-top: 24px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
}

.file-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.file-list-header h4 {
  margin: 0;
  color: #262626;
}

.file-list-actions {
  display: flex;
  gap: 8px;
}

.file-list-content {
  background: white;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.file-icon.markdown {
  color: #1890ff;
}

.file-icon.image {
  color: #52c41a;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 500;
  color: #262626;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-path {
  font-size: 12px;
  color: #8c8c8c;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.upload-errors {
  margin-top: 16px;
}

.upload-errors ul {
  margin: 0;
  padding-left: 20px;
}

.upload-errors li {
  margin: 4px 0;
  font-size: 12px;
}
</style>
```

#### 交付物清单
1. **文件上传组合函数**: `src/composables/useFileUpload.ts`
2. **文件上传组件**: `src/components/admin/FileUpload.vue`
3. **文件类型图标**: 文件类型识别和图标显示
4. **拖拽上传**: 支持文件夹拖拽上传功能
5. **进度显示**: 实时上传进度和状态显示
6. **错误处理**: 完善的前端错误处理和用户反馈
7. **组件文档**: `docs/components/file-upload-component.md`

#### 验收标准
- [ ] 支持文件夹拖拽上传，保持目录结构
- [ ] 文件类型过滤正确，不支持的文件类型被拒绝
- [ ] 文件大小限制有效，超出限制时给出明确提示
- [ ] 上传进度实时显示，用户体验良好
- [ ] 支持取消上传和重新上传
- [ ] 错误处理完善，所有异常情况都有用户友好的提示
- [ ] 响应式设计，在不同设备上正常工作
- [ ] 组件可复用，接口设计合理

#### 技术规范
- 使用Vue3 Composition API和TypeScript
- 支持文件夹上传（webkitdirectory）
- 实现拖拽上传功能
- 合理的文件预检查和验证
- 清晰的用户界面和交互反馈
- 组件化设计，便于维护和扩展

---

### 任务3.4: 文件预览和管理界面

#### 基本信息
- **任务编号**: 3.4
- **任务名称**: 文件预览和管理界面
- **负责人**: Alex (工程师)
- **预估工期**: 1.5个工作日
- **优先级**: P0 (最高优先级)
- **依赖关系**: 依赖任务3.3（前端文件上传组件开发）

#### 技术实现要求

**3.4.1 文件管理页面组件**
```vue
<!-- 文件管理页面 (src/views/admin/FileManagement.vue) -->
<template>
  <div class="file-management">
    <div class="page-header">
      <div class="header-content">
        <h2>{{ subject?.name }} - 文件管理</h2>
        <p>管理学科下的所有文件，支持上传、预览和删除操作</p>
      </div>
      <div class="header-actions">
        <a-button @click="refreshFiles">
          <template #icon>
            <reload-outlined />
          </template>
          刷新
        </a-button>

        <a-button type="primary" @click="showUploadModal">
          <template #icon>
            <upload-outlined />
          </template>
          上传文件
        </a-button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-cards">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="总文件数"
              :value="stats.totalFiles"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <file-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>

        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="Markdown文件"
              :value="stats.markdownFiles"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <file-markdown-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>

        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="图片文件"
              :value="stats.imageFiles"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <file-image-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>

        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="总大小"
              :value="formatFileSize(stats.totalSize)"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <cloud-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 文件树形结构 -->
    <a-card class="file-tree-card" title="文件结构">
      <template #extra>
        <a-space>
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="搜索文件..."
            style="width: 200px"
            @search="handleSearch"
          />

          <a-select
            v-model:value="fileTypeFilter"
            placeholder="文件类型"
            style="width: 120px"
            @change="handleFilterChange"
          >
            <a-select-option value="">全部</a-select-option>
            <a-select-option value="markdown">Markdown</a-select-option>
            <a-select-option value="image">图片</a-select-option>
          </a-select>
        </a-space>
      </template>

      <div class="file-tree-container">
        <a-tree
          v-model:expandedKeys="expandedKeys"
          v-model:selectedKeys="selectedKeys"
          :tree-data="fileTreeData"
          :load-data="loadTreeData"
          show-icon
          @select="handleFileSelect"
        >
          <template #icon="{ dataRef }">
            <folder-outlined v-if="dataRef.isDirectory" />
            <file-markdown-outlined v-else-if="dataRef.type === 'markdown'" />
            <file-image-outlined v-else-if="dataRef.type === 'image'" />
            <file-outlined v-else />
          </template>

          <template #title="{ dataRef }">
            <div class="tree-node-title">
              <span class="node-name">{{ dataRef.title }}</span>
              <span class="node-size" v-if="!dataRef.isDirectory">
                {{ formatFileSize(dataRef.size) }}
              </span>
              <div class="node-actions" v-if="!dataRef.isDirectory">
                <a-button
                  type="link"
                  size="small"
                  @click.stop="previewFile(dataRef)"
                >
                  预览
                </a-button>
                <a-button
                  type="link"
                  size="small"
                  danger
                  @click.stop="deleteFile(dataRef)"
                >
                  删除
                </a-button>
              </div>
            </div>
          </template>
        </a-tree>
      </div>
    </a-card>

    <!-- 文件预览模态框 -->
    <a-modal
      v-model:open="previewModalVisible"
      :title="previewFile?.name"
      width="80%"
      :footer="null"
      :destroy-on-close="true"
    >
      <div class="file-preview">
        <!-- Markdown预览 -->
        <div v-if="previewFile?.type === 'markdown'" class="markdown-preview">
          <div v-html="markdownContent" class="markdown-content"></div>
        </div>

        <!-- 图片预览 -->
        <div v-else-if="previewFile?.type === 'image'" class="image-preview">
          <img
            :src="getFileUrl(previewFile)"
            :alt="previewFile.name"
            style="max-width: 100%; height: auto;"
          />
        </div>

        <!-- 其他文件类型 -->
        <div v-else class="unsupported-preview">
          <a-result
            status="info"
            title="暂不支持预览此文件类型"
            :sub-title="`文件: ${previewFile?.name}`"
          >
            <template #extra>
              <a-button type="primary" @click="downloadFile(previewFile)">
                下载文件
              </a-button>
            </template>
          </a-result>
        </div>
      </div>
    </a-modal>

    <!-- 文件上传模态框 -->
    <a-modal
      v-model:open="uploadModalVisible"
      title="上传文件"
      width="800px"
      :footer="null"
      :destroy-on-close="true"
    >
      <FileUpload
        :subject-id="subjectId"
        @upload-success="handleUploadSuccess"
        @upload-error="handleUploadError"
      />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import {
  ReloadOutlined,
  UploadOutlined,
  FileOutlined,
  FileMarkdownOutlined,
  FileImageOutlined,
  FolderOutlined,
  CloudOutlined
} from '@ant-design/icons-vue'
import { useAdminApi } from '@/composables/useAdminApi'
import { formatFileSize } from '@/utils/format'
import FileUpload from '@/components/admin/FileUpload.vue'
import { marked } from 'marked'

interface FileNode {
  id: string
  name: string
  path: string
  relativePath: string
  size: number
  type: 'markdown' | 'image' | 'directory'
  isDirectory: boolean
  children?: FileNode[]
  parent?: string
}

const route = useRoute()
const { adminApi } = useAdminApi()

// 基础数据
const subjectId = computed(() => parseInt(route.params.id as string))
const subject = ref<any>(null)
const files = ref<FileNode[]>([])
const loading = ref(false)

// 统计信息
const stats = reactive({
  totalFiles: 0,
  markdownFiles: 0,
  imageFiles: 0,
  totalSize: 0
})

// 树形结构状态
const fileTreeData = ref<any[]>([])
const expandedKeys = ref<string[]>([])
const selectedKeys = ref<string[]>([])

// 搜索和过滤
const searchKeyword = ref('')
const fileTypeFilter = ref('')

// 预览相关
const previewModalVisible = ref(false)
const previewFile = ref<FileNode | null>(null)
const markdownContent = ref('')

// 上传相关
const uploadModalVisible = ref(false)

// 获取学科信息
const fetchSubject = async () => {
  try {
    const response = await adminApi.get(`/subjects/${subjectId.value}`)
    subject.value = response.data.data
  } catch (error) {
    message.error('获取学科信息失败')
  }
}

// 获取文件列表
const fetchFiles = async () => {
  loading.value = true
  try {
    const response = await adminApi.get(`/subjects/${subjectId.value}/files`)
    files.value = response.data.data

    // 更新统计信息
    updateStats()

    // 构建树形结构
    buildFileTree()

  } catch (error) {
    message.error('获取文件列表失败')
  } finally {
    loading.value = false
  }
}

// 更新统计信息
const updateStats = () => {
  stats.totalFiles = files.value.length
  stats.markdownFiles = files.value.filter(f => f.type === 'markdown').length
  stats.imageFiles = files.value.filter(f => f.type === 'image').length
  stats.totalSize = files.value.reduce((sum, f) => sum + f.size, 0)
}

// 构建文件树
const buildFileTree = () => {
  const tree: any[] = []
  const pathMap = new Map()

  // 创建根目录节点
  files.value.forEach(file => {
    const pathParts = file.relativePath ? file.relativePath.split('/') : []
    let currentPath = ''
    let currentLevel = tree

    // 创建目录节点
    pathParts.forEach((part, index) => {
      currentPath = currentPath ? `${currentPath}/${part}` : part

      let dirNode = currentLevel.find(node => node.key === currentPath)
      if (!dirNode) {
        dirNode = {
          key: currentPath,
          title: part,
          isDirectory: true,
          children: []
        }
        currentLevel.push(dirNode)
      }

      currentLevel = dirNode.children
    })

    // 添加文件节点
    currentLevel.push({
      key: file.id,
      title: file.name,
      isDirectory: false,
      type: file.type,
      size: file.size,
      path: file.path,
      relativePath: file.relativePath,
      ...file
    })
  })

  fileTreeData.value = tree
}

// 处理文件选择
const handleFileSelect = (selectedKeys: string[], info: any) => {
  const node = info.node.dataRef
  if (!node.isDirectory) {
    previewFile.value = node
    previewModalVisible.value = true
    loadFileContent(node)
  }
}

// 加载文件内容
const loadFileContent = async (file: FileNode) => {
  if (file.type === 'markdown') {
    try {
      const response = await adminApi.get(`/files/${file.id}/content`)
      const content = response.data.data.content
      markdownContent.value = marked(content)
    } catch (error) {
      message.error('加载文件内容失败')
    }
  }
}

// 预览文件
const previewFile = (file: FileNode) => {
  previewFile.value = file
  previewModalVisible.value = true
  loadFileContent(file)
}

// 删除文件
const deleteFile = (file: FileNode) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除文件"${file.name}"吗？此操作不可恢复。`,
    okText: '确认删除',
    okType: 'danger',
    cancelText: '取消',
    onOk: async () => {
      try {
        await adminApi.delete(`/files/${file.id}`)
        message.success('文件删除成功')
        await fetchFiles()
      } catch (error) {
        message.error('文件删除失败')
      }
    }
  })
}

// 获取文件URL
const getFileUrl = (file: FileNode) => {
  return `/api/files/${file.id}/download`
}

// 下载文件
const downloadFile = (file: FileNode) => {
  window.open(getFileUrl(file), '_blank')
}

// 搜索处理
const handleSearch = (value: string) => {
  // 实现文件搜索逻辑
  console.log('搜索:', value)
}

// 过滤处理
const handleFilterChange = (value: string) => {
  // 实现文件类型过滤逻辑
  console.log('过滤:', value)
}

// 刷新文件列表
const refreshFiles = () => {
  fetchFiles()
}

// 显示上传模态框
const showUploadModal = () => {
  uploadModalVisible.value = true
}

// 上传成功处理
const handleUploadSuccess = (result: any) => {
  message.success(`文件上传成功！共上传 ${result.totalFiles} 个文件`)
  uploadModalVisible.value = false
  fetchFiles()
}

// 上传错误处理
const handleUploadError = (error: any) => {
  console.error('上传失败:', error)
}

// 树形数据懒加载
const loadTreeData = (treeNode: any) => {
  return new Promise<void>((resolve) => {
    // 实现懒加载逻辑
    resolve()
  })
}

// 初始化
onMounted(async () => {
  await fetchSubject()
  await fetchFiles()
})
</script>

<style scoped>
.file-management {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-content h2 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #262626;
}

.header-content p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.stats-cards {
  margin-bottom: 24px;
}

.file-tree-card {
  border-radius: 8px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
}

.file-tree-container {
  max-height: 600px;
  overflow-y: auto;
}

.tree-node-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.node-name {
  flex: 1;
  margin-right: 8px;
}

.node-size {
  font-size: 12px;
  color: #8c8c8c;
  margin-right: 8px;
}

.node-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.tree-node-title:hover .node-actions {
  opacity: 1;
}

.file-preview {
  max-height: 70vh;
  overflow-y: auto;
}

.markdown-content {
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
  line-height: 1.6;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  color: #262626;
}

.markdown-content p {
  margin-bottom: 16px;
  color: #595959;
}

.markdown-content code {
  background: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.markdown-content pre {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 6px;
  overflow-x: auto;
}

.image-preview {
  text-align: center;
  padding: 16px;
}

.unsupported-preview {
  padding: 40px;
}
</style>
```

#### 交付物清单
1. **文件管理页面**: `src/views/admin/FileManagement.vue`
2. **文件预览组件**: 支持Markdown和图片预览
3. **文件树形结构**: 层级目录展示和操作
4. **统计信息**: 文件数量和大小统计
5. **搜索过滤**: 文件名搜索和类型过滤
6. **文件操作**: 预览、下载、删除功能
7. **界面文档**: `docs/admin/file-management-guide.md`

#### 验收标准
- [ ] 文件树形结构正确显示目录层级关系
- [ ] Markdown文件预览功能正常，格式渲染正确
- [ ] 图片文件预览功能正常，支持常见图片格式
- [ ] 文件统计信息准确，实时更新
- [ ] 文件搜索和过滤功能有效
- [ ] 文件删除功能正常，有确认提示
- [ ] 界面响应式设计，适配不同屏幕尺寸
- [ ] 用户体验良好，操作流畅

#### 技术规范
- 使用Vue3 Composition API和TypeScript
- 树形组件展示文件结构
- Markdown渲染使用marked库
- 图片预览支持缩放和下载
- 合理的加载状态和错误处理
- 统一的文件操作接口

---

### 任务3.5: 前后端集成联调和测试

#### 基本信息
- **任务编号**: 3.5
- **任务名称**: 前后端集成联调和测试
- **负责人**: Alex (工程师)
- **预估工期**: 1个工作日
- **优先级**: P0 (最高优先级)
- **依赖关系**: 依赖任务3.4（文件预览和管理界面）

#### 技术实现要求

**3.5.1 文件上传集成测试**
```javascript
// 文件上传集成测试 (tests/integration/file-upload-integration.spec.js)
const { test, expect } = require('@playwright/test')
const fs = require('fs')
const path = require('path')

test.describe('文件上传集成测试', () => {
  const adminPath = process.env.ADMIN_PANEL_PATH || 'admin-panel-abcdef'
  const adminUrl = `/${adminPath}`

  let testSubjectId

  test.beforeEach(async ({ page }) => {
    // 创建测试学科
    await page.goto(adminUrl)
    await page.waitForSelector('.admin-layout')

    await page.click('text=学科管理')
    await page.click('button:has-text("创建学科")')
    await page.waitForSelector('.ant-modal')

    const testSubjectName = `文件上传测试学科_${Date.now()}`
    await page.fill('input[placeholder="请输入学科名称"]', testSubjectName)
    await page.click('.ant-modal button:has-text("确定")')
    await page.waitForSelector('.ant-message-success')

    // 获取学科ID（从URL或其他方式）
    testSubjectId = await page.evaluate(() => {
      const rows = document.querySelectorAll('.ant-table-tbody tr')
      const lastRow = rows[rows.length - 1]
      return lastRow?.getAttribute('data-row-key') || '1'
    })
  })

  test('单文件上传测试', async ({ page }) => {
    // 进入文件管理页面
    await page.goto(`${adminUrl}/subjects/${testSubjectId}/files`)
    await page.waitForSelector('.file-management')

    // 点击上传按钮
    await page.click('button:has-text("上传文件")')
    await page.waitForSelector('.file-upload')

    // 创建测试文件
    const testContent = '# 测试Markdown文件\n\n这是一个测试文件。'
    const testFilePath = path.join(__dirname, 'temp', 'test.md')
    await fs.promises.mkdir(path.dirname(testFilePath), { recursive: true })
    await fs.promises.writeFile(testFilePath, testContent)

    // 选择文件
    const fileInput = page.locator('input[type="file"]:not([webkitdirectory])')
    await fileInput.setInputFiles(testFilePath)

    // 验证文件出现在列表中
    await expect(page.locator('text=test.md')).toBeVisible()

    // 开始上传
    await page.click('button:has-text("开始上传")')

    // 等待上传完成
    await page.waitForSelector('.ant-message-success', { timeout: 30000 })

    // 验证上传成功
    await expect(page.locator('.ant-tag:has-text("成功")')).toBeVisible()

    // 清理测试文件
    await fs.promises.unlink(testFilePath)
  })

  test('文件夹上传测试', async ({ page }) => {
    // 创建测试文件夹结构
    const testDir = path.join(__dirname, 'temp', 'test-folder')
    await fs.promises.mkdir(path.join(testDir, 'images'), { recursive: true })
    await fs.promises.mkdir(path.join(testDir, 'docs'), { recursive: true })

    // 创建测试文件
    await fs.promises.writeFile(
      path.join(testDir, 'README.md'),
      '# 项目说明\n\n这是项目的说明文档。'
    )
    await fs.promises.writeFile(
      path.join(testDir, 'docs', 'guide.md'),
      '# 使用指南\n\n详细的使用说明。'
    )

    // 创建测试图片（简单的1x1像素PNG）
    const pngBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,
      0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
      0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53,
      0xDE, 0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41,
      0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
      0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44,
      0xAE, 0x42, 0x60, 0x82
    ])
    await fs.promises.writeFile(path.join(testDir, 'images', 'test.png'), pngBuffer)

    // 进入文件管理页面
    await page.goto(`${adminUrl}/subjects/${testSubjectId}/files`)
    await page.waitForSelector('.file-management')

    // 点击上传按钮
    await page.click('button:has-text("上传文件")')
    await page.waitForSelector('.file-upload')

    // 选择文件夹（模拟webkitdirectory）
    const directoryInput = page.locator('input[webkitdirectory]')

    // 由于Playwright不能直接设置文件夹，我们模拟文件选择
    const files = [
      { name: 'README.md', path: 'README.md' },
      { name: 'guide.md', path: 'docs/guide.md' },
      { name: 'test.png', path: 'images/test.png' }
    ]

    // 使用JavaScript模拟文件夹上传
    await page.evaluate((files) => {
      const fileList = files.map(file => {
        const mockFile = new File(['content'], file.name, { type: 'text/plain' })
        Object.defineProperty(mockFile, 'webkitRelativePath', {
          value: file.path,
          writable: false
        })
        return mockFile
      })

      const event = new Event('change')
      const input = document.querySelector('input[webkitdirectory]')
      Object.defineProperty(input, 'files', {
        value: fileList,
        writable: false
      })
      input.dispatchEvent(event)
    }, files)

    // 验证文件出现在列表中
    await expect(page.locator('text=README.md')).toBeVisible()
    await expect(page.locator('text=guide.md')).toBeVisible()
    await expect(page.locator('text=test.png')).toBeVisible()

    // 开始上传
    await page.click('button:has-text("开始上传")')

    // 等待上传完成
    await page.waitForSelector('.ant-message-success', { timeout: 30000 })

    // 清理测试文件夹
    await fs.promises.rm(testDir, { recursive: true, force: true })
  })

  test('文件预览测试', async ({ page }) => {
    // 先上传一个测试文件
    await page.goto(`${adminUrl}/subjects/${testSubjectId}/files`)
    await page.waitForSelector('.file-management')

    // 假设已有文件，点击预览
    const previewButton = page.locator('button:has-text("预览")').first()
    if (await previewButton.isVisible()) {
      await previewButton.click()

      // 验证预览模态框打开
      await expect(page.locator('.ant-modal')).toBeVisible()

      // 验证预览内容
      await expect(page.locator('.file-preview')).toBeVisible()
    }
  })

  test('文件删除测试', async ({ page }) => {
    await page.goto(`${adminUrl}/subjects/${testSubjectId}/files`)
    await page.waitForSelector('.file-management')

    // 假设已有文件，点击删除
    const deleteButton = page.locator('button:has-text("删除")').first()
    if (await deleteButton.isVisible()) {
      await deleteButton.click()

      // 确认删除
      await page.waitForSelector('.ant-modal')
      await page.click('.ant-modal button:has-text("确认删除")')

      // 验证删除成功
      await page.waitForSelector('.ant-message-success')
    }
  })

  test('大文件上传性能测试', async ({ page }) => {
    // 创建大文件（10MB）
    const largeContent = 'A'.repeat(10 * 1024 * 1024)
    const largeFilePath = path.join(__dirname, 'temp', 'large-file.md')
    await fs.promises.mkdir(path.dirname(largeFilePath), { recursive: true })
    await fs.promises.writeFile(largeFilePath, largeContent)

    await page.goto(`${adminUrl}/subjects/${testSubjectId}/files`)
    await page.waitForSelector('.file-management')

    await page.click('button:has-text("上传文件")')
    await page.waitForSelector('.file-upload')

    const startTime = Date.now()

    // 选择大文件
    const fileInput = page.locator('input[type="file"]:not([webkitdirectory])')
    await fileInput.setInputFiles(largeFilePath)

    // 开始上传
    await page.click('button:has-text("开始上传")')

    // 等待上传完成
    await page.waitForSelector('.ant-message-success', { timeout: 60000 })

    const uploadTime = Date.now() - startTime
    console.log(`大文件上传时间: ${uploadTime}ms`)

    // 验证上传时间在合理范围内（60秒内）
    expect(uploadTime).toBeLessThan(60000)

    // 清理大文件
    await fs.promises.unlink(largeFilePath)
  })

  test('并发上传测试', async ({ page, context }) => {
    // 创建多个测试文件
    const testFiles = []
    for (let i = 0; i < 5; i++) {
      const filePath = path.join(__dirname, 'temp', `concurrent-test-${i}.md`)
      await fs.promises.mkdir(path.dirname(filePath), { recursive: true })
      await fs.promises.writeFile(filePath, `# 并发测试文件 ${i}\n\n内容 ${i}`)
      testFiles.push(filePath)
    }

    await page.goto(`${adminUrl}/subjects/${testSubjectId}/files`)
    await page.waitForSelector('.file-management')

    await page.click('button:has-text("上传文件")')
    await page.waitForSelector('.file-upload')

    // 选择多个文件
    const fileInput = page.locator('input[type="file"]:not([webkitdirectory])')
    await fileInput.setInputFiles(testFiles)

    // 验证所有文件都在列表中
    for (let i = 0; i < 5; i++) {
      await expect(page.locator(`text=concurrent-test-${i}.md`)).toBeVisible()
    }

    // 开始上传
    await page.click('button:has-text("开始上传")')

    // 等待上传完成
    await page.waitForSelector('.ant-message-success', { timeout: 30000 })

    // 清理测试文件
    for (const filePath of testFiles) {
      await fs.promises.unlink(filePath)
    }
  })

  test('错误处理测试', async ({ page }) => {
    await page.goto(`${adminUrl}/subjects/${testSubjectId}/files`)
    await page.waitForSelector('.file-management')

    await page.click('button:has-text("上传文件")')
    await page.waitForSelector('.file-upload')

    // 测试不支持的文件类型
    const unsupportedFilePath = path.join(__dirname, 'temp', 'test.txt')
    await fs.promises.mkdir(path.dirname(unsupportedFilePath), { recursive: true })
    await fs.promises.writeFile(unsupportedFilePath, 'unsupported content')

    const fileInput = page.locator('input[type="file"]:not([webkitdirectory])')
    await fileInput.setInputFiles(unsupportedFilePath)

    // 验证错误提示
    await expect(page.locator('.ant-message-error')).toBeVisible()

    // 清理测试文件
    await fs.promises.unlink(unsupportedFilePath)
  })

  test.afterEach(async ({ page }) => {
    // 清理测试学科
    if (testSubjectId) {
      try {
        await page.goto(`${adminUrl}/subjects`)
        await page.waitForSelector('.subject-management')

        const deleteButton = page.locator(`tr[data-row-key="${testSubjectId}"] button:has-text("删除")`)
        if (await deleteButton.isVisible()) {
          await deleteButton.click()
          await page.waitForSelector('.ant-modal')
          await page.click('.ant-modal button:has-text("确认删除")')
          await page.waitForSelector('.ant-message-success')
        }
      } catch (error) {
        console.error('清理测试学科失败:', error)
      }
    }
  })
})
```

**3.5.2 API性能测试**
```javascript
// API性能测试 (tests/performance/file-upload-performance.spec.js)
const { test, expect } = require('@playwright/test')

test.describe('文件上传性能测试', () => {
  const baseURL = 'http://localhost:3000/api/admin'
  const adminPath = process.env.ADMIN_PANEL_PATH || 'admin-panel-abcdef'

  test('文件上传API响应时间测试', async ({ request }) => {
    // 创建测试文件
    const testContent = Buffer.from('测试文件内容'.repeat(1000))

    const formData = new FormData()
    formData.append('files', new Blob([testContent], { type: 'text/markdown' }), 'performance-test.md')

    const startTime = Date.now()

    const response = await request.post(`${baseURL}/subjects/1/upload`, {
      headers: {
        'x-admin-path': adminPath
      },
      multipart: {
        files: {
          name: 'performance-test.md',
          mimeType: 'text/markdown',
          buffer: testContent
        }
      }
    })

    const responseTime = Date.now() - startTime

    expect(response.status()).toBe(201)
    expect(responseTime).toBeLessThan(5000) // 5秒内响应

    console.log(`文件上传API响应时间: ${responseTime}ms`)
  })

  test('大量文件上传性能测试', async ({ request }) => {
    const files = []

    // 创建100个小文件
    for (let i = 0; i < 100; i++) {
      files.push({
        name: `batch-test-${i}.md`,
        content: `# 批量测试文件 ${i}\n\n内容 ${i}`
      })
    }

    const startTime = Date.now()

    const formData = new FormData()
    files.forEach(file => {
      formData.append('files', new Blob([file.content], { type: 'text/markdown' }), file.name)
    })

    const response = await request.post(`${baseURL}/subjects/1/upload`, {
      headers: {
        'x-admin-path': adminPath
      },
      multipart: formData
    })

    const responseTime = Date.now() - startTime

    expect(response.status()).toBe(201)
    expect(responseTime).toBeLessThan(30000) // 30秒内完成

    console.log(`批量文件上传时间: ${responseTime}ms`)
  })
})
```

#### 交付物清单
1. **集成测试套件**: `tests/integration/file-upload-integration.spec.js`
2. **性能测试**: `tests/performance/file-upload-performance.spec.js`
3. **错误场景测试**: 各种异常情况的测试用例
4. **并发测试**: 多文件同时上传的测试
5. **端到端测试**: 完整用户流程的自动化测试
6. **测试报告**: HTML格式的详细测试报告
7. **性能基准**: 上传性能的基准数据和优化建议

#### 验收标准
- [ ] 所有集成测试用例通过，覆盖正常和异常流程
- [ ] 文件上传性能符合要求（单文件≤5秒，批量≤30秒）
- [ ] 大文件上传稳定，支持50MB文件上传
- [ ] 并发上传功能正常，支持多文件同时处理
- [ ] 错误处理测试通过，异常情况有适当提示
- [ ] 前后端数据同步正确，上传后立即可见
- [ ] 内存使用稳定，无内存泄漏
- [ ] 测试覆盖率达到90%以上

#### 技术规范
- 使用Playwright进行端到端测试
- 测试数据自动生成和清理
- 性能测试包含响应时间和吞吐量
- 错误场景覆盖完整
- 测试环境隔离，避免相互影响
- 详细的测试报告和日志

---

## 3. Sprint执行计划

### 3.1 任务依赖关系
```
任务3.1 (文件上传后端API开发) [2天]
    ↓
任务3.2 (文件存储和目录结构处理) [1.5天]
    ↓
任务3.3 (前端文件上传组件开发) [2天]
    ↓
任务3.4 (文件预览和管理界面) [1.5天]
    ↓
任务3.5 (前后端集成联调和测试) [1天]
```

### 3.2 时间安排
- **第1-2天**: 任务3.1 - 文件上传后端API开发
- **第2.5-4天**: 任务3.2 - 文件存储和目录结构处理
- **第4-6天**: 任务3.3 - 前端文件上传组件开发
- **第6-7.5天**: 任务3.4 - 文件预览和管理界面
- **第8天**: 任务3.5 - 前后端集成联调和测试

### 3.3 质量检查点
- **第2天**: 后端API基础功能验证
- **第4天**: 文件存储机制验证
- **第6天**: 前端上传组件功能验证
- **第7.5天**: 文件管理界面完整性验证
- **第8天**: 完整功能集成验证

### 3.4 风险缓解
- **技术风险**: 大文件上传可能遇到超时问题，实现分片上传机制
- **性能风险**: 大量文件处理可能影响性能，优化文件处理流程
- **兼容性风险**: 文件夹上传在不同浏览器的兼容性，提供降级方案

## 4. 验收标准总结

### 4.1 功能验收
- [ ] 管理员能够上传包含Markdown和图片的文件夹
- [ ] 系统正确过滤文件类型，拒绝不支持的格式
- [ ] 文件大小限制有效，超出限制时给出明确提示
- [ ] 上传进度实时显示，用户体验良好
- [ ] 文件预览功能正常，支持Markdown渲染和图片显示
- [ ] 文件管理界面完整，支持查看、删除等操作

### 4.2 技术验收
- [ ] 文件上传API功能完整，支持多文件和文件夹上传
- [ ] 文件存储机制稳定，目录结构正确保存
- [ ] 数据库记录准确，文件层级关系正确
- [ ] 前端组件功能完整，交互体验良好
- [ ] 错误处理完善，异常情况有适当的用户反馈

### 4.3 性能验收
- [ ] 单文件上传响应时间 ≤ 5秒
- [ ] 批量文件上传时间 ≤ 30秒（100个文件）
- [ ] 支持最大50MB单文件上传
- [ ] 支持最大500MB总文件大小上传
- [ ] 内存使用稳定，无内存泄漏
- [ ] 并发上传支持，多用户同时使用

### 4.4 用户体验验收
- [ ] 文件上传界面直观易用，支持拖拽操作
- [ ] 上传进度清晰显示，用户不会困惑
- [ ] 错误提示明确，用户能理解问题所在
- [ ] 文件预览功能便捷，支持常见格式
- [ ] 响应式设计，适配不同设备

## 5. 交付清单

### 5.1 代码交付物
- [ ] 完整的文件上传后端API服务
- [ ] 完整的文件存储和管理系统
- [ ] 完整的前端文件上传组件
- [ ] 完整的文件预览和管理界面
- [ ] 完整的自动化测试套件

### 5.2 文档交付物
- [ ] 文件上传API接口文档
- [ ] 文件存储设计文档
- [ ] 前端组件使用文档
- [ ] 文件管理操作指南
- [ ] 测试用例和性能报告

### 5.3 配置交付物
- [ ] 文件上传配置参数
- [ ] 存储目录结构规范
- [ ] 文件类型和大小限制配置
- [ ] 测试环境配置
- [ ] 部署配置更新

---

**文档状态**: ✅ 已完成
**下一步**: 等待老板审核，准备启动Sprint-03开发工作
**版权声明**: 本文档版权归属于【随影】

*详细执行清单完成时间: 2025-01-27*
*规划人员: Bob (架构师) + Mike (团队领袖)*
