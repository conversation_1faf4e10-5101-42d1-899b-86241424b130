import type { Context } from 'koa'

export * from './subject'
export * from './file'

// Koa Context扩展
export interface AppContext extends Context {
  // 可以在这里扩展Context类型
}

// 环境变量类型
export interface AppConfig {
  port: number
  dbPath: string
  storagePath: string
  corsOrigin: string | string[]
}

// 错误类型
export interface AppError extends Error {
  status?: number
  code?: string
  details?: any
}

// 中间件类型
export type Middleware = (ctx: AppContext, next: () => Promise<any>) => Promise<any>

// 数据库连接类型
export interface DatabaseConnection {
  prepare: (sql: string) => any
  exec: (sql: string) => any
  close: () => void
}