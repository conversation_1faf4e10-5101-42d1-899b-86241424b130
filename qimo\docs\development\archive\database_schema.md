# 数据库设计文档

## 概述

期末复习平台使用SQLite数据库，采用better-sqlite3库提供同步API支持。数据库设计遵循关系型数据库范式，支持学科管理和文件树形结构存储。

## 技术选型

- **数据库**: SQLite 3.x
- **驱动**: better-sqlite3 (同步API)
- **特性**: WAL模式、外键约束、事务支持
- **部署**: 单文件数据库，便于备份和迁移

## 数据表设计

### 1. subjects (学科表)

存储所有学科的基本信息。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | 学科唯一标识 |
| name | TEXT | NOT NULL UNIQUE | 学科名称，唯一约束 |
| description | TEXT | DEFAULT '' | 学科描述（预留字段） |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 更新时间 |
| status | INTEGER | DEFAULT 1 | 状态：1=启用，0=禁用（预留） |
| sort_order | INTEGER | DEFAULT 0 | 排序权重（预留） |

**索引**:
- `idx_subjects_name`: 学科名称索引
- `idx_subjects_status`: 状态索引
- `idx_subjects_created_at`: 创建时间索引

### 2. file_nodes (文件节点表)

存储文件和文件夹的树形结构，支持无限层级嵌套。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | 节点唯一标识 |
| subject_id | INTEGER | NOT NULL, FK | 所属学科ID |
| parent_id | INTEGER | DEFAULT NULL, FK | 父节点ID，NULL表示根节点 |
| name | TEXT | NOT NULL | 文件/文件夹名称 |
| type | TEXT | NOT NULL, CHECK | 节点类型：'file'或'folder' |
| path | TEXT | NOT NULL | 相对路径 |
| size | INTEGER | DEFAULT 0 | 文件大小（字节），文件夹为0 |
| mime_type | TEXT | DEFAULT '' | MIME类型（预留字段） |
| content_hash | TEXT | DEFAULT '' | 内容哈希值（预留字段） |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 更新时间 |
| is_deleted | INTEGER | DEFAULT 0 | 软删除标记（预留字段） |

**外键约束**:
- `subject_id` → `subjects(id)` ON DELETE CASCADE
- `parent_id` → `file_nodes(id)` ON DELETE CASCADE

**索引**:
- `idx_file_nodes_subject_id`: 学科ID索引
- `idx_file_nodes_parent_id`: 父节点ID索引
- `idx_file_nodes_path`: 路径索引
- `idx_file_nodes_type`: 类型索引
- `idx_file_nodes_name`: 名称索引
- `idx_file_nodes_is_deleted`: 删除标记索引
- `idx_file_nodes_subject_parent`: 复合索引(学科+父节点)
- `idx_file_nodes_subject_type`: 复合索引(学科+类型)
- `idx_file_nodes_parent_type`: 复合索引(父节点+类型)

### 3. schema_migrations (迁移记录表)

系统自动创建，用于跟踪数据库迁移版本。

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | 记录ID |
| filename | TEXT | NOT NULL UNIQUE | 迁移文件名 |
| executed_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 执行时间 |
| checksum | TEXT | NOT NULL | 文件校验和 |

## 数据库配置

### 性能优化配置

```sql
PRAGMA journal_mode = WAL;        -- 启用WAL模式提升并发性能
PRAGMA synchronous = NORMAL;      -- 平衡性能和安全性
PRAGMA cache_size = 1000;         -- 设置缓存大小
PRAGMA temp_store = memory;       -- 临时表存储在内存中
PRAGMA foreign_keys = ON;         -- 启用外键约束
```

### 触发器

自动更新`updated_at`字段的触发器：

```sql
-- 学科表更新触发器
CREATE TRIGGER trigger_subjects_updated_at
    AFTER UPDATE ON subjects
    FOR EACH ROW
BEGIN
    UPDATE subjects SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 文件节点表更新触发器
CREATE TRIGGER trigger_file_nodes_updated_at
    AFTER UPDATE ON file_nodes
    FOR EACH ROW
BEGIN
    UPDATE file_nodes SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
```

## 查询模式

### 常用查询示例

1. **获取所有学科列表**
```sql
SELECT id, name, created_at, updated_at 
FROM subjects 
WHERE status = 1 
ORDER BY sort_order, name;
```

2. **获取学科的根文件夹**
```sql
SELECT * FROM file_nodes 
WHERE subject_id = ? AND parent_id IS NULL AND is_deleted = 0
ORDER BY type DESC, name;
```

3. **获取文件夹的子节点**
```sql
SELECT * FROM file_nodes 
WHERE parent_id = ? AND is_deleted = 0
ORDER BY type DESC, name;
```

4. **获取文件的完整路径**
```sql
WITH RECURSIVE file_path AS (
  SELECT id, parent_id, name, 0 as level
  FROM file_nodes WHERE id = ?
  UNION ALL
  SELECT fn.id, fn.parent_id, fn.name, fp.level + 1
  FROM file_nodes fn
  JOIN file_path fp ON fn.id = fp.parent_id
)
SELECT GROUP_CONCAT(name, '/') as full_path
FROM (SELECT name FROM file_path ORDER BY level DESC);
```

## 扩展性设计

### 预留字段

为支持后续P1、P2功能，预留了以下字段：

**subjects表**:
- `description`: 学科描述
- `status`: 启用/禁用状态
- `sort_order`: 自定义排序

**file_nodes表**:
- `mime_type`: 文件MIME类型
- `content_hash`: 文件内容哈希
- `is_deleted`: 软删除支持

### 未来扩展

1. **用户权限系统**: 可添加users、roles、permissions表
2. **文件版本控制**: 可添加file_versions表
3. **访问日志**: 可添加access_logs表
4. **标签系统**: 可添加tags、file_tags表
5. **全文搜索**: 可集成FTS5扩展

## 备份和恢复

### 备份策略

```bash
# 在线备份（推荐）
sqlite3 platform.db ".backup backup.db"

# 文件复制备份
cp platform.db platform_backup_$(date +%Y%m%d_%H%M%S).db
```

### 恢复策略

```bash
# 从备份恢复
sqlite3 platform.db ".restore backup.db"
```

## 性能监控

### 关键指标

- 查询响应时间
- 数据库文件大小
- WAL文件大小
- 索引使用情况

### 监控查询

```sql
-- 检查索引使用情况
EXPLAIN QUERY PLAN SELECT * FROM file_nodes WHERE subject_id = 1;

-- 检查数据库统计
SELECT * FROM sqlite_stat1;

-- 检查表大小
SELECT name, COUNT(*) as row_count FROM sqlite_master 
WHERE type='table' GROUP BY name;
```

---

**文档版本**: v1.0  
**创建时间**: 2025-01-27  
**作者**: Alex (Engineer)  
**版权**: 随影