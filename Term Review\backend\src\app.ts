import Koa from 'koa'
import bodyParser from 'koa-bodyparser'
import koaStatic from 'koa-static'
import { join } from 'path'
import { initDatabase } from './database/connection.js'
import router from './routes/index.js'
import {
  errorHandler,
  requestLogger,
  cors,
  responseTime,
  securityHeaders
} from './middleware/index.js'

// 简化路径处理，避免import.meta问题
const appDir = process.cwd()

// 创建Koa应用
const app = new Koa()

// 应用配置
const PORT = process.env.PORT || 3000
const NODE_ENV = process.env.NODE_ENV || 'development'

// 初始化数据库
console.log('🔧 Initializing database...')
initDatabase()

// 中间件注册（顺序很重要）
app.use(errorHandler)                // 错误处理（最先）
app.use(responseTime)                // 响应时间
app.use(requestLogger)               // 请求日志

// CORS配置
app.use(cors({
  origin: NODE_ENV === 'development'
    ? ['http://localhost:5173', 'http://127.0.0.1:5173']
    : process.env.FRONTEND_URL?.split(',') || '*',
  credentials: true,
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization', 'Accept', 'X-Requested-With']
}))

app.use(securityHeaders)             // 安全头

// 请求体解析
app.use(bodyParser({
  jsonLimit: '10mb',
  textLimit: '10mb',
  enableTypes: ['json', 'form', 'text']
}))

// 静态文件服务（用于文件上传后的访问）
const staticPath = join(appDir, 'storage')
app.use(koaStatic(staticPath))

// 路由注册
app.use(router.routes())
app.use(router.allowedMethods())

// 404处理
app.use(async (ctx) => {
  ctx.status = 404
  ctx.body = {
    success: false,
    message: 'API路径不存在',
    error: 'NOT_FOUND'
  }
})

// 全局错误事件监听
app.on('error', (error, ctx) => {
  console.error('App error:', error)
  if (ctx) {
    console.error('Request URL:', ctx.url)
    console.error('Request Method:', ctx.method)
  }
})

// 启动服务器
const server = app.listen(PORT, () => {
  console.log(`🚀 Term Review Backend Server is running!`)
  console.log(`📍 Local:            http://localhost:${PORT}`)
  console.log(`🌍 Environment:      ${NODE_ENV}`)
  console.log(`📊 Health Check:     http://localhost:${PORT}/api/health`)
  console.log(`📁 Static Files:     http://localhost:${PORT}/static`)
})

// 优雅关闭
const gracefulShutdown = (signal: string) => {
  console.log(`\n💡 Received ${signal}, shutting down gracefully...`)

  server.close(() => {
    console.log('✅ HTTP server closed')
    process.exit(0)
  })

  // 强制关闭
  setTimeout(() => {
    console.error('❌ Could not close connections in time, forcefully shutting down')
    process.exit(1)
  }, 10000)
}

// 监听关闭信号
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'))
process.on('SIGINT', () => gracefulShutdown('SIGINT'))

// 未捕获异常处理
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
  process.exit(1)
})

export default app