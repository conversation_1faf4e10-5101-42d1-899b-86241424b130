# API Reference

## Overview

This document provides comprehensive reference for all API endpoints, request/response formats, and integration guidelines.

## Table of Contents

- [Authentication](#authentication)
- [Base URLs](#base-urls)
- [Common Response Formats](#common-response-formats)
- [Error Handling](#error-handling)
- [API Endpoints](#api-endpoints)
- [Rate Limiting](#rate-limiting)
- [Versioning](#versioning)

## Authentication

TODO: Define authentication mechanisms

## Base URLs

### 环境配置
- **开发环境**: `http://localhost:3000/api`
- **生产环境**: `https://your-domain.com/api`

### 健康检查
- **URL**: `GET /api/health`
- **用途**: 服务状态检查
- **响应**: 
```json
{
  "success": true,
  "message": "Term Review API is running",
  "timestamp": "2025-01-31T12:00:00.000Z",
  "version": "0.1.0"
}
```

## Common Response Formats

### 成功响应格式
```typescript
interface SuccessResponse<T = any> {
  success: true;
  data: T;              // 响应数据
  message?: string;     // 操作成功消息
  total?: number;       // 总数（适用于列表接口）
}
```

### 错误响应格式
```typescript
interface ErrorResponse {
  success: false;
  message: string;      // 用户友好错误消息
  error: string;        // 错误类型代码
  details?: any;        // 详细错误信息（仅开发环境）
}
```

### HTTP状态码规范
| 状态码 | 说明 | 使用场景 |
|--------|------|----------|
| `200` | OK | 成功获取资源 |
| `201` | Created | 成功创建资源 |
| `400` | Bad Request | 请求参数验证失败 |
| `404` | Not Found | 请求的资源不存在 |
| `409` | Conflict | 资源冲突（如重复创建） |
| `500` | Internal Server Error | 服务器内部错误 |

## Error Handling

### 错误类型代码
| 错误代码 | 说明 | HTTP状态码 |
|----------|------|------------|
| `VALIDATION_ERROR` | 请求参数验证失败 | 400 |
| `NOT_FOUND` | 资源不存在 | 404 |
| `CONFLICT` | 资源冲突 | 409 |
| `INTERNAL_ERROR` | 服务器内部错误 | 500 |

### 错误响应示例
```json
{
  "success": false,
  "message": "学科名称已存在",
  "error": "CONFLICT"
}
```

## API Endpoints

### 学科管理接口

#### 1. 创建学科

**POST** `/api/subjects`

创建新的学科分类。

**请求参数**
```typescript
interface CreateSubjectRequest {
  name: string;  // 学科名称，1-50字符，不可重复
}
```

**请求示例**
```bash
curl -X POST http://localhost:3000/api/subjects \
  -H "Content-Type: application/json" \
  -d '{"name": "计算机科学"}'
```

**成功响应** (201 Created)
```json
{
  "success": true,
  "data": {
    "id": "550e8400-e29b-41d4-a716-************",
    "name": "计算机科学",
    "createdAt": "2025-01-31T12:00:00.000Z",
    "updatedAt": "2025-01-31T12:00:00.000Z",
    "fileCount": 0
  },
  "message": "学科创建成功"
}
```

**错误响应示例**
```json
// 名称重复 (409 Conflict)
{
  "success": false,
  "message": "学科名称已存在",
  "error": "CONFLICT"
}

// 参数验证失败 (400 Bad Request)
{
  "success": false,
  "message": "学科名称不能包含特殊字符 < > : \" / \\ | ? *",
  "error": "VALIDATION_ERROR"
}
```

---

#### 2. 获取学科列表

**GET** `/api/subjects`

获取所有学科的列表，按创建时间倒序排列。

**请求示例**
```bash
curl -X GET http://localhost:3000/api/subjects
```

**成功响应** (200 OK)
```json
{
  "success": true,
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "计算机科学",
      "createdAt": "2025-01-31T12:00:00.000Z",
      "updatedAt": "2025-01-31T12:00:00.000Z",
      "fileCount": 5
    },
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "数学",
      "createdAt": "2025-01-30T12:00:00.000Z",
      "updatedAt": "2025-01-30T12:00:00.000Z",
      "fileCount": 3
    }
  ],
  "total": 2,
  "message": "获取学科列表成功"
}
```

---

#### 3. 获取学科详情

**GET** `/api/subjects/:id`

根据学科ID获取单个学科的详细信息。

**路径参数**
- `id` (string): 学科ID，UUID格式

**请求示例**
```bash
curl -X GET http://localhost:3000/api/subjects/550e8400-e29b-41d4-a716-************
```

**成功响应** (200 OK)
```json
{
  "success": true,
  "data": {
    "id": "550e8400-e29b-41d4-a716-************",
    "name": "计算机科学",
    "createdAt": "2025-01-31T12:00:00.000Z",
    "updatedAt": "2025-01-31T12:00:00.000Z",
    "fileCount": 5
  },
  "message": "获取学科详情成功"
}
```

**错误响应示例**
```json
// 学科不存在 (404 Not Found)
{
  "success": false,
  "message": "学科不存在",
  "error": "NOT_FOUND"
}

// 无效的UUID格式 (400 Bad Request)
{
  "success": false,
  "message": "无效的学科ID格式",
  "error": "VALIDATION_ERROR"
}
```

---

#### 4. 更新学科

**PUT** `/api/subjects/:id`

更新指定学科的信息。

**路径参数**
- `id` (string): 学科ID，UUID格式

**请求参数**
```typescript
interface UpdateSubjectRequest {
  name?: string;  // 新的学科名称（可选）
}
```

**请求示例**
```bash
curl -X PUT http://localhost:3000/api/subjects/550e8400-e29b-41d4-a716-************ \
  -H "Content-Type: application/json" \
  -d '{"name": "计算机科学与技术"}'
```

**成功响应** (200 OK)
```json
{
  "success": true,
  "data": {
    "id": "550e8400-e29b-41d4-a716-************",
    "name": "计算机科学与技术",
    "createdAt": "2025-01-31T12:00:00.000Z",
    "updatedAt": "2025-01-31T12:30:00.000Z",
    "fileCount": 5
  },
  "message": "学科更新成功"
}
```

---

#### 5. 删除学科

**DELETE** `/api/subjects/:id`

删除指定的学科。注意：此操作会级联删除该学科下的所有文件。

**路径参数**
- `id` (string): 学科ID，UUID格式

**请求示例**
```bash
curl -X DELETE http://localhost:3000/api/subjects/550e8400-e29b-41d4-a716-************
```

**成功响应** (200 OK)
```json
{
  "success": true,
  "message": "学科删除成功"
}
```

**错误响应示例**
```json
// 学科不存在 (404 Not Found)
{
  "success": false,
  "message": "学科不存在",
  "error": "NOT_FOUND"
}
```

### 文件浏览接口

#### 1. 获取文件列表

**请求**
- **URL**: `GET /api/subjects/{subjectId}/files`
- **方法**: GET
- **路径参数**:
  - `subjectId` (string, required): 学科ID，UUID格式

**查询参数**:
```typescript
interface GetFilesQuery {
  parent_id?: string;    // 父节点ID，为空时获取根目录
  search?: string;       // 搜索关键词
  page?: number;         // 页码，默认1
  limit?: number;        // 每页数量，默认20，最大100
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "files": [
      {
        "id": "file-001",
        "subject_id": "550e8400-e29b-41d4-a716-************",
        "name": "第一章",
        "type": "folder",
        "path": "/第一章",
        "parent_id": null,
        "size": null,
        "created_at": "2025-01-31T12:00:00.000Z"
      },
      {
        "id": "file-002",
        "subject_id": "550e8400-e29b-41d4-a716-************",
        "name": "概述.pdf",
        "type": "file",
        "path": "/概述.pdf",
        "parent_id": null,
        "size": 1024000,
        "created_at": "2025-01-31T12:00:00.000Z"
      }
    ],
    "total": 2,
    "page": 1,
    "limit": 20,
    "has_more": false
  },
  "message": "获取文件列表成功"
}
```

#### 2. 获取面包屑导航

**请求**
- **URL**: `GET /api/files/{nodeId}/breadcrumb`
- **方法**: GET
- **路径参数**:
  - `nodeId` (string, required): 节点ID，使用"root"表示根目录

**查询参数**:
```typescript
interface GetBreadcrumbQuery {
  subject_id: string;    // 学科ID，必需
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "breadcrumb": [
      {
        "id": "folder-001",
        "name": "第一章",
        "path": "/第一章"
      },
      {
        "id": "folder-002",
        "name": "基础概念",
        "path": "/第一章/基础概念"
      }
    ]
  },
  "message": "面包屑路径获取成功"
}
```

#### 3. 搜索文件

**请求**
- **URL**: `GET /api/subjects/{subjectId}/search`
- **方法**: GET
- **路径参数**:
  - `subjectId` (string, required): 学科ID，UUID格式

**查询参数**:
```typescript
interface SearchFilesQuery {
  query: string;         // 搜索关键词，必需，最大100字符
  type?: 'file' | 'folder' | 'all';  // 文件类型过滤，默认'all'
  page?: number;         // 页码，默认1
  limit?: number;        // 每页数量，默认20，最大100
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "files": [
      {
        "id": "file-003",
        "subject_id": "550e8400-e29b-41d4-a716-************",
        "name": "概述文档.pdf",
        "type": "file",
        "path": "/第一章/概述文档.pdf",
        "parent_id": "folder-001",
        "size": 2048000,
        "created_at": "2025-01-31T12:00:00.000Z"
      }
    ],
    "total": 1,
    "page": 1,
    "limit": 20,
    "has_more": false,
    "query": "概述"
  },
  "message": "搜索文件成功"
}
```

### 数据类型定义

#### Subject对象
```typescript
interface Subject {
  id: string;           // UUID格式的唯一标识符
  name: string;         // 学科名称
  createdAt: Date;      // 创建时间（ISO 8601格式）
  updatedAt: Date;      // 最后更新时间（ISO 8601格式）
  fileCount: number;    // 该学科下的文件数量
}
```

#### FileNode对象
```typescript
interface FileNode {
  id: string;           // UUID格式的唯一标识符
  subject_id: string;   // 所属学科ID
  name: string;         // 文件/文件夹名称
  type: 'file' | 'folder';  // 节点类型
  path: string;         // 完整路径
  parent_id: string | null;  // 父节点ID，根节点为null
  size: number | null;  // 文件大小（字节），文件夹为null
  created_at: Date;     // 创建时间（ISO 8601格式）
}
```

#### BreadcrumbItem对象
```typescript
interface BreadcrumbItem {
  id: string;           // 节点ID
  name: string;         // 显示名称
  path: string;         // 节点路径
}
```

## Rate Limiting

TODO: Define rate limiting policies

## Versioning

TODO: Define API versioning strategy

---

*This document is part of the project's living documentation and should be updated as the API evolves.*