import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 状态
  const loading = ref(false)
  const error = ref<string | null>(null)
  const theme = ref<'light' | 'dark'>('light')
  const sidebarCollapsed = ref(false)
  
  // 计算属性
  const isLoading = computed(() => loading.value)
  const hasError = computed(() => !!error.value)
  const isDarkTheme = computed(() => theme.value === 'dark')
  
  // 方法
  const setLoading = (value: boolean) => {
    loading.value = value
  }
  
  const setError = (message: string | null) => {
    error.value = message
  }
  
  const clearError = () => {
    error.value = null
  }
  
  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
  }
  
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }
  
  const setSidebarCollapsed = (collapsed: boolean) => {
    sidebarCollapsed.value = collapsed
  }
  
  // 全局错误处理
  const handleError = (err: any) => {
    console.error('App Error:', err)
    const message = err?.response?.data?.error?.message || err?.message || '发生未知错误'
    setError(message)
  }
  
  // 全局成功提示
  const showSuccess = (message: string) => {
    // 这里可以集成消息提示组件
    console.log('Success:', message)
  }
  
  return {
    // 状态
    loading,
    error,
    theme,
    sidebarCollapsed,
    
    // 计算属性
    isLoading,
    hasError,
    isDarkTheme,
    
    // 方法
    setLoading,
    setError,
    clearError,
    toggleTheme,
    toggleSidebar,
    setSidebarCollapsed,
    handleError,
    showSuccess
  }
})
