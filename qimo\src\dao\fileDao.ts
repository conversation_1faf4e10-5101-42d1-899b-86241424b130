/**
 * 文件数据访问层 (DAO)
 * 负责文件节点相关的数据库操作
 * 
 * <AUTHOR> (Engineer)
 * @date 2025-01-27
 */

import { Database } from 'better-sqlite3';
import { getDatabase } from '../database/connection';
import { FileNode } from '../types';
import * as fs from 'fs';
import * as path from 'path';

export class FileDao {
  private db: Database | null = null;

  constructor() {
    // 延迟初始化数据库连接
  }

  private getDb(): Database {
    if (!this.db) {
      this.db = getDatabase();
    }
    return this.db;
  }

  /**
   * 根据学科ID获取文件树结构
   * @param subjectId 学科ID
   * @param parentId 父节点ID，null表示根节点
   * @returns 文件节点列表
   */
  findBySubjectId(subjectId: number, parentId: number | null = null): FileNode[] {
    try {
      const sql = `
        SELECT * FROM file_nodes 
        WHERE subject_id = ? AND parent_id ${parentId === null ? 'IS NULL' : '= ?'} AND is_deleted = 0
        ORDER BY type DESC, name ASC
      `;
      const stmt = this.getDb().prepare(sql);
      const params = parentId === null ? [subjectId] : [subjectId, parentId];
      const rows = stmt.all(...params) as FileNode[];

      return rows;
    } catch (error) {
      console.error('FileDao.findBySubjectId error:', error);
      throw new Error('获取文件列表失败');
    }
  }

  /**
   * 获取完整的文件树结构（递归）
   * @param subjectId 学科ID
   * @returns 带有children的文件树
   */
  getFileTree(subjectId: number): FileNode[] {
    try {
      // 获取所有文件节点
      const sql = 'SELECT * FROM file_nodes WHERE subject_id = ? AND is_deleted = 0 ORDER BY parent_id, type DESC, name ASC';
      const stmt = this.getDb().prepare(sql);
      const allNodes = stmt.all(subjectId) as FileNode[];

      // 构建树形结构
      const nodeMap = new Map<number, FileNode & { children: FileNode[] }>();
      const rootNodes: (FileNode & { children: FileNode[] })[] = [];

      // 初始化所有节点
      allNodes.forEach(node => {
        nodeMap.set(node.id, { ...node, children: [] });
      });

      // 构建父子关系
      allNodes.forEach(node => {
        const nodeWithChildren = nodeMap.get(node.id)!;
        if (node.parent_id === null) {
          rootNodes.push(nodeWithChildren);
        } else {
          const parent = nodeMap.get(node.parent_id);
          if (parent) {
            parent.children.push(nodeWithChildren);
          }
        }
      });

      return rootNodes;
    } catch (error) {
      console.error('FileDao.getFileTree error:', error);
      throw new Error('获取文件树失败');
    }
  }

  /**
   * 根据ID获取单个文件节点
   * @param id 文件节点ID
   * @returns 文件节点信息或null
   */
  findById(id: number): FileNode | null {
    try {
      const sql = 'SELECT * FROM file_nodes WHERE id = ? AND is_deleted = 0';
      const stmt = this.getDb().prepare(sql);
      const row = stmt.get(id) as FileNode | undefined;

      return row || null;
    } catch (error) {
      console.error('FileDao.findById error:', error);
      throw new Error('获取文件信息失败');
    }
  }

  /**
   * 根据路径获取文件节点
   * @param subjectId 学科ID
   * @param filePath 文件路径
   * @returns 文件节点信息或null
   */
  findByPath(subjectId: number, filePath: string): FileNode | null {
    try {
      const sql = 'SELECT * FROM file_nodes WHERE subject_id = ? AND path = ? AND is_deleted = 0';
      const stmt = this.getDb().prepare(sql);
      const row = stmt.get(subjectId, filePath) as FileNode | undefined;

      return row || null;
    } catch (error) {
      console.error('FileDao.findByPath error:', error);
      throw new Error('获取文件信息失败');
    }
  }

  /**
   * 创建新文件节点
   * @param fileNode 文件节点信息
   * @returns 创建的文件节点ID
   */
  create(fileNode: Omit<FileNode, 'id' | 'created_at' | 'updated_at'>): number {
    try {
      const sql = `
        INSERT INTO file_nodes (subject_id, parent_id, name, type, path, size, mime_type, content_hash, is_deleted)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      const stmt = this.getDb().prepare(sql);
      const result = stmt.run(
        fileNode.subject_id,
        fileNode.parent_id,
        fileNode.name,
        fileNode.type,
        fileNode.path,
        fileNode.size || 0,
        fileNode.mime_type || '',
        fileNode.content_hash || '',
        fileNode.is_deleted || 0
      );

      return result.lastInsertRowid as number;
    } catch (error) {
      console.error('FileDao.create error:', error);
      throw new Error('创建文件节点失败');
    }
  }

  /**
   * 更新文件节点信息
   * @param id 文件节点ID
   * @param updates 更新的字段
   * @returns 是否更新成功
   */
  update(id: number, updates: Partial<Omit<FileNode, 'id' | 'created_at' | 'updated_at'>>): boolean {
    try {
      const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
      const values = Object.values(updates);

      const sql = `UPDATE file_nodes SET ${fields} WHERE id = ?`;
      const stmt = this.getDb().prepare(sql);
      const result = stmt.run(...values, id);

      return result.changes > 0;
    } catch (error) {
      console.error('FileDao.update error:', error);
      throw new Error('更新文件节点失败');
    }
  }

  /**
   * 软删除文件节点
   * @param id 文件节点ID
   * @returns 是否删除成功
   */
  softDelete(id: number): boolean {
    try {
      const sql = 'UPDATE file_nodes SET is_deleted = 1 WHERE id = ?';
      const stmt = this.getDb().prepare(sql);
      const result = stmt.run(id);

      return result.changes > 0;
    } catch (error) {
      console.error('FileDao.softDelete error:', error);
      throw new Error('删除文件节点失败');
    }
  }

  /**
   * 搜索文件
   * @param subjectId 学科ID，可选
   * @param keyword 搜索关键词
   * @param fileType 文件类型过滤，可选
   * @returns 搜索结果
   */
  search(keyword: string, subjectId?: number, fileType?: 'file' | 'folder'): FileNode[] {
    try {
      let sql = `
        SELECT * FROM file_nodes 
        WHERE name LIKE ? AND is_deleted = 0
      `;
      const params: any[] = [`%${keyword}%`];

      if (subjectId) {
        sql += ' AND subject_id = ?';
        params.push(subjectId);
      }

      if (fileType) {
        sql += ' AND type = ?';
        params.push(fileType);
      }

      sql += ' ORDER BY type DESC, name ASC LIMIT 50';

      const stmt = this.getDb().prepare(sql);
      const rows = stmt.all(...params) as FileNode[];

      return rows;
    } catch (error) {
      console.error('FileDao.search error:', error);
      throw new Error('搜索文件失败');
    }
  }

  /**
   * 获取文件的实际内容
   * @param fileNode 文件节点信息
   * @param basePath 文件存储基础路径
   * @returns 文件内容
   */
  getFileContent(fileNode: FileNode, basePath: string = './data/files'): string {
    try {
      if (fileNode.type !== 'file') {
        throw new Error('只能读取文件类型的内容');
      }

      // 构建实际文件路径
      const fullPath = path.join(basePath, fileNode.subject_id.toString(), fileNode.path);

      // 检查文件是否存在
      if (!fs.existsSync(fullPath)) {
        throw new Error('文件不存在');
      }

      // 读取文件内容
      const content = fs.readFileSync(fullPath, 'utf-8');
      return content;
    } catch (error) {
      console.error('FileDao.getFileContent error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('读取文件内容失败');
    }
  }

  /**
   * 获取文件夹下的子节点数量
   * @param id 文件夹ID
   * @returns 子节点数量统计
   */
  getChildrenCount(id: number): { fileCount: number; folderCount: number } {
    try {
      const sql = `
        SELECT 
          COUNT(CASE WHEN type = 'file' THEN 1 END) as fileCount,
          COUNT(CASE WHEN type = 'folder' THEN 1 END) as folderCount
        FROM file_nodes 
        WHERE parent_id = ? AND is_deleted = 0
      `;
      const stmt = this.getDb().prepare(sql);
      const result = stmt.get(id) as any;

      return {
        fileCount: result.fileCount || 0,
        folderCount: result.folderCount || 0
      };
    } catch (error) {
      console.error('FileDao.getChildrenCount error:', error);
      throw new Error('获取子节点数量失败');
    }
  }
}

// 导出单例实例
export const fileDao = new FileDao();