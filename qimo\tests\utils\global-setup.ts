import { testDataManager } from './test-data'

/**
 * Playwright全局设置
 * 在所有测试开始前执行
 */
async function globalSetup() {
  console.log('🚀 开始全局测试设置...')
  
  try {
    // 初始化测试数据库
    await testDataManager.initTestDatabase()
    
    // 重置测试数据
    await testDataManager.resetTestData()
    
    console.log('✅ 全局测试设置完成')
  } catch (error) {
    console.error('❌ 全局测试设置失败:', error)
    throw error
  }
}

export default globalSetup
