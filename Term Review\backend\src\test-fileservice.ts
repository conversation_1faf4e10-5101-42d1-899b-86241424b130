// 临时测试文件，用于验证FileService功能
import { FileService } from './services/FileService.js'

async function testFileService() {
  console.log('🧪 Testing FileService...')
  
  try {
    // 测试参数验证
    console.log('1. Testing parameter validation...')
    
    // 测试空学科ID
    try {
      await FileService.getFilesByParent({ subject_id: '' })
      console.log('❌ Should throw error for empty subject_id')
    } catch (error) {
      console.log('✅ Correctly validates empty subject_id:', (error as Error).message)
    }
    
    // 测试空搜索关键词
    try {
      await FileService.searchFiles({ subject_id: 'test', query: '' })
      console.log('❌ Should throw error for empty query')
    } catch (error) {
      console.log('✅ Correctly validates empty query:', (error as Error).message)
    }
    
    // 测试过长搜索关键词
    try {
      const longQuery = 'a'.repeat(101)
      await FileService.searchFiles({ subject_id: 'test', query: longQuery })
      console.log('❌ Should throw error for long query')
    } catch (error) {
      console.log('✅ Correctly validates long query:', (error as Error).message)
    }
    
    // 测试不存在的学科
    try {
      await FileService.getFilesByParent({ subject_id: 'non-existent' })
      console.log('❌ Should throw error for non-existent subject')
    } catch (error) {
      console.log('✅ Correctly validates non-existent subject:', (error as Error).message)
    }
    
    console.log('✅ All parameter validation tests passed!')
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// 只在直接运行时执行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testFileService()
}

export { testFileService }
